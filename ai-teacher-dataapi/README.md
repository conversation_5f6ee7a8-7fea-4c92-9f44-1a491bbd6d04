# AI Teacher DataAPI Module

## 项目概述

### 项目名称
AI Teacher DataAPI Module - AI伴学服务数据访问层模块

### 项目描述
AI Teacher DataAPI模块是AI伴学服务的数据访问层，提供了统一的数据访问接口和多种数据源的集成能力。该模块封装了MongoDB、Elasticsearch、图数据库(NebulaGraph)以及AI能力服务的访问逻辑，为上层业务提供标准化的数据操作接口。

### 版本信息
- **当前版本**: 1.0.2-SNAPSHOT
- **Java版本**: JDK 8
- **编码格式**: UTF-8

### 许可证信息
Apache License, Version 2.0

## 技术架构

### 整体技术架构描述
- **架构模式**: 数据访问对象模式(DAO)、仓储模式(Repository)
- **设计模式**: 
  - 门面模式(Facade)：AITeacherDataHub统一数据访问入口
  - 工厂模式：数据源连接管理
  - 模板方法模式：MongoDB操作模板
- **核心技术栈**:
  - Spring Data MongoDB：MongoDB数据访问
  - Elasticsearch：全文搜索和数据分析
  - NebulaGraph：图数据库支持
  - Apache HttpClient：HTTP客户端
  - Spring Boot：应用框架

### 模块职责说明
- **数据访问统一入口**: AITeacherDataHub提供所有数据服务的统一访问
- **MongoDB数据层**: 存储问卷、用户画像、学习规划等业务数据
- **Elasticsearch集成**: 提供特征数据查询和分析能力
- **图数据库支持**: 知识图谱数据访问和关系查询
- **AI能力服务**: 集成外部AI服务接口调用
- **提示词管理**: 本地文件系统的提示词加载和缓存

### 架构图

```mermaid
graph TB
    A[AI Teacher DataAPI Module] --> B[Data Hub Layer]
    A --> C[Service Layer]
    A --> D[Repository Layer]
    A --> E[Entity Layer]
    A --> F[External Integration]
    
    B --> B1[AITeacherDataHub]
    
    C --> C1[QuestionnaireService]
    C --> C2[PlanResultService]
    C --> C3[UserPortraitService]
    C --> C4[StorageInfoService]
    C --> C5[PromptService]
    C --> C6[AiAbilityService]
    
    D --> D1[QuestionnaireRepository]
    D --> D2[PlanResultRepository]
    D --> D3[UserPortraitRepository]
    D --> D4[StorageInfoRepository]
    
    E --> E1[QuestionnaireEntity]
    E --> E2[PlanResultEntity]
    E --> E3[UserPortraitEntity]
    E --> E4[StorageInfoEntity]
    
    F --> F1[MongoDB]
    F --> F2[Elasticsearch]
    F --> F3[NebulaGraph]
    F --> F4[AI Thor Service]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

## 主要功能

### 功能模块列表

#### 1. 问卷数据管理 (QuestionnaireService)
- **核心功能**: 问卷题目查询和问卷回答数据管理
- **数据存储**: MongoDB集合 `xxj_ai_teacher_questionnaire`、`xxj_ai_teacher_questionnaire_answer`
- **业务场景**: 学习前问卷调研、用户学习偏好分析
- **关键方法**: 
  - `query(String traceId, Integer index)`: 根据索引查询问卷题目
  - 问卷回答数据的增删改查操作

#### 2. 学习规划数据管理 (PlanResultService)
- **核心功能**: 学习规划结果的存储和查询
- **数据存储**: MongoDB集合 `xxj_ai_teacher_plan_result`
- **业务场景**: 个性化学习路径规划、学习进度跟踪
- **关键方法**:
  - `query(PlanResultRequest)`: 查询学习规划结果
  - `insert(PlanResultData)`: 存储新的规划结果
  - `update(PlanResultData)`: 更新规划结果
  - `delete(PlanResultRequest)`: 删除规划结果

#### 3. 用户画像管理 (UserPortraitService)
- **核心功能**: 用户学习画像数据管理
- **数据存储**: MongoDB集合 `xxj_ai_teacher_user_portrait`
- **业务场景**: 用户能力评估、个性化推荐
- **关键方法**:
  - `query(UserPortraitRequest)`: 查询用户画像
  - `insert(UserPortraitData)`: 创建用户画像
  - `update(UserPortraitData)`: 更新用户画像
  - `delete(String userId)`: 删除用户画像

#### 4. 存储信息管理 (StorageInfoService)
- **核心功能**: 问卷总结和规划总结数据管理
- **数据存储**: MongoDB集合 `xxj_ai_teacher_storage_info`
- **业务场景**: AI分析结果存储、历史记录查询
- **关键方法**:
  - `query(StorageInfoRequest)`: 查询存储信息
  - `queryLatest(StorageInfoRequest)`: 查询最新记录
  - `insert(StorageInfoData)`: 插入新记录
  - `update(StorageInfoData)`: 更新记录

#### 5. AI能力服务 (AiAbilityService)
- **核心功能**: 集成外部AI服务接口
- **服务地址**: AI Thor智能体平台
- **业务场景**: 大模型对话、流式响应处理
- **关键方法**:
  - `getChatResult(ChatRequestParam, ChatHeader, SseListener)`: SSE流式对话

#### 6. 提示词服务 (PromptService)
- **核心功能**: 提示词模板管理和缓存
- **数据来源**: 本地文件系统
- **业务场景**: AI模型提示词配置、模板管理
- **支持类型**: 课前对话、知识簇选择、用户画像推理、学习规划

### 关键技术点
- **MongoDB集成**: 使用Spring Data MongoDB进行文档数据库操作
- **Elasticsearch集成**: 支持复杂查询和数据分析
- **图数据库支持**: NebulaGraph集成用于知识图谱查询
- **SSE流式处理**: 支持Server-Sent Events实时数据推送
- **连接池管理**: HTTP连接池优化和超时控制
- **缓存机制**: 提示词和知识数据的内存缓存
- **重试机制**: 网络请求失败自动重试

## 业务处理逻辑分析

### 核心业务逻辑

#### 1. MongoDB数据操作流程
```java
// 以StorageInfoRepository为例
public void insert(StorageInfoEntity entity) {
    entity.setCreateTime(Instant.now());
    entity.setIsDelete(DeleteFlagEnum.EXIST.getFlag());
    mongoTemplate.insert(entity, COLLECTION_NAME);
}
```
- **数据流转**: 业务对象 → 实体转换 → MongoDB存储 → 结果返回
- **条件判断**: 根据查询条件构建MongoDB Query对象
- **异常处理**: 数据库操作异常统一处理和日志记录

#### 2. AI能力服务调用流程
```java
// AiAbilityServiceImpl核心逻辑
public void getChatResult(ChatRequestParam param, ChatHeader chatHeader, SseListener listener) {
    // 重试机制
    for (int attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            // HTTP请求处理
            // SSE流式响应解析
            // 回调监听器处理
        } catch (Exception e) {
            // 重试逻辑
        }
    }
}
```
- **数据流转**: 请求参数 → HTTP调用 → SSE流解析 → 事件回调
- **条件判断**: 根据响应状态判断是否需要重试
- **循环结构**: 重试循环和SSE数据流处理循环
- **异常处理**: 网络异常重试、解析异常记录

#### 3. 提示词加载和缓存流程
```java
// PromptServiceImpl初始化逻辑
@PostConstruct
public void init() throws IOException {
    loadPrompt(classChat, PromptType.CLASS_CHAT);
    loadPrompt(clusterChoice, PromptType.CLUSTER_CHOICE);
    // ... 其他提示词加载
}
```
- **数据流转**: 文件路径 → 文件读取 → 内容缓存 → 服务提供
- **异常处理**: 文件不存在时的降级处理

### 关键算法说明

#### 1. MongoDB查询优化算法
- **业务场景**: 大数据量下的高效查询
- **算法实现**: 复合索引设计、分页查询、条件过滤
- **优化点**: 使用MongoDB的聚合管道进行复杂查询

#### 2. SSE流式数据解析算法
```java
private ChatResponse parseChatResponse(String line) {
    String json = line.substring(5).trim(); // 去除"data:"前缀
    return JSONObject.parseObject(json, ChatResponse.class);
}
```
- **业务场景**: 实时AI对话响应处理
- **算法实现**: 逐行解析SSE数据流
- **优化点**: 异步处理提高响应性能

#### 3. 重试机制算法
- **业务场景**: 网络不稳定环境下的服务调用
- **算法实现**: 指数退避重试策略
- **优化点**: 区分不同异常类型采用不同重试策略

## 主要对外接口

### 接口类型说明
- **数据访问接口**: 提供标准化的CRUD操作
- **AI服务接口**: 集成外部AI能力
- **缓存服务接口**: 提示词和知识数据缓存
- **统一访问入口**: AITeacherDataHub门面接口

### 接口详细信息

| 服务类型 | 接口名称 | 主要方法 | 功能描述 | 数据源 |
|---------|---------|---------|---------|--------|
| 问卷服务 | QuestionnaireService | query | 查询问卷题目 | MongoDB |
| 问卷回答服务 | QuestionnaireAnswerService | query/insert/update | 问卷回答管理 | MongoDB |
| 规划结果服务 | PlanResultService | query/insert/update/delete | 学习规划数据管理 | MongoDB |
| 用户画像服务 | UserPortraitService | query/insert/update/delete | 用户画像管理 | MongoDB |
| 存储信息服务 | StorageInfoService | query/queryLatest/insert/update | 总结信息管理 | MongoDB |
| AI能力服务 | AiAbilityService | getChatResult | AI对话服务 | AI Thor |
| 提示词服务 | PromptService | getContentByKey | 提示词获取 | 本地文件 |
| 知识问答服务 | AnchorKnowledgeClasschatQaService | getContent | 知识问答数据 | 本地文件 |

### 数据库集合设计

| 集合名称 | 功能描述 | 主要字段 | 索引设计 |
|---------|---------|---------|---------|
| xxj_ai_teacher_questionnaire | 问卷题目 | index, dimension, questionContent | index |
| xxj_ai_teacher_questionnaire_answer | 问卷回答 | userId, roundId, bizAction | userId+roundId |
| xxj_ai_teacher_user_portrait | 用户画像 | userId, userLevel, userRank | userId |
| xxj_ai_teacher_plan_result | 规划结果 | userId, catalogId, functionCode | userId+catalogId |
| xxj_ai_teacher_storage_info | 存储信息 | userId, functionCode, updateTime | userId+functionCode |

## 系统配置

### 运行环境要求
- **JDK版本**: JDK 8+
- **操作系统**: 支持Windows、Linux、macOS
- **内存要求**: 最小512MB，推荐1GB+
- **数据库**: MongoDB 4.0+、Elasticsearch 7.x、NebulaGraph 3.x

### 配置文件说明

#### MongoDB配置
```properties
spring.data.mongodb.uri=********************************:port/database?authSource=admin
```

#### Elasticsearch配置
```properties
zion.es-host=***********:9200,***********:9200,***********:9200
zion.es-user-name=elastic
zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
```

#### 图数据库配置
```properties
skylab.data.api.graph.hosts=***********:9669
skylab.data.api.graph.username=root
skylab.data.api.graph.password=nebula
skylab.data.api.graph.maxConnSize=1000
```

#### AI服务配置
```properties
chat.app.id=xxjcpx-xcjzx
chat.app.agent.id=682557525cf2002c9fb76e1d
chat.sse.url=https://ai-thor-prelt.ceshiservice.cn/ai-thor-dispatcher/api/v1/workflow/chat
chat.connect.timeout=5000
chat.socket.timeout=30000
chat.http.retry.time=2
```

### 启动和部署
```xml
<!-- Maven依赖集成 -->
<dependency>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-dataapi</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

## 快速开始

### 环境准备
1. 安装MongoDB 4.0+
2. 安装Elasticsearch 7.x
3. 配置NebulaGraph图数据库
4. 准备提示词文件

### 数据库初始化
```javascript
// MongoDB集合创建
db.createCollection("xxj_ai_teacher_questionnaire");
db.createCollection("xxj_ai_teacher_questionnaire_answer");
db.createCollection("xxj_ai_teacher_user_portrait");
db.createCollection("xxj_ai_teacher_plan_result");
db.createCollection("xxj_ai_teacher_storage_info");
```

### 项目集成
1. **添加Maven依赖**
```xml
<dependency>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-dataapi</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

2. **使用数据服务**
```java
// 通过AITeacherDataHub访问数据服务
QuestionnaireService questionnaireService = AITeacherDataHub.getQuestionnaireService();
List<QuestionnaireData> questions = questionnaireService.query(traceId, index);

// 使用AI能力服务
AiAbilityService aiService = AITeacherDataHub.getAiAbilityService();
aiService.getChatResult(param, header, response -> {
    // 处理AI响应
});
```

3. **配置数据源**
```properties
# 配置MongoDB连接
spring.data.mongodb.uri=mongodb://localhost:27017/ai-teacher

# 配置AI服务
chat.app.id=your-app-id
chat.sse.url=your-ai-service-url
```

### 验证测试
1. 启动应用检查数据库连接
2. 调用问卷服务验证MongoDB操作
3. 测试AI服务连接和响应

## 开发指南

### 代码结构
```
ai-teacher-dataapi/
├── src/main/java/
│   └── com/iflytek/ebgai/ai/teacher/dataapi/
│       ├── AITeacherDataHub.java      # 数据访问统一入口
│       ├── agent/                     # AI服务集成
│       ├── entity/                    # 数据传输对象
│       ├── mongo/                     # MongoDB实体和DAO
│       └── service/                   # 数据服务接口和实现
└── pom.xml                           # Maven配置
```

### 开发规范
- **数据访问**: 统一通过AITeacherDataHub访问数据服务
- **异常处理**: 数据库操作异常统一处理和日志记录
- **事务管理**: 使用Spring事务注解管理数据一致性
- **连接管理**: 合理配置连接池参数避免连接泄露
- **缓存策略**: 合理使用缓存提高查询性能

### 测试指南
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn test -Dtest=*IntegrationTest

# 编译打包
mvn clean package
```
