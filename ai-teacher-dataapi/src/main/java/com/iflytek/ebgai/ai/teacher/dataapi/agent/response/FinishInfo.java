package com.iflytek.ebgai.ai.teacher.dataapi.agent.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class FinishInfo implements Serializable {
    /**
     * 开始时间
     * 单位为ms
     */
    private Long startTime;
    /**
     * 结束时间
     * 单位为ms
     */
    private Long endTime;
    /**
     * 记录token相关数据
     */
    private TokenInfo tokenInfo;
    /**
     * 调试信息
     * 预留，格式待定
     * 非必填
     */
    private Object debugInfo;

}