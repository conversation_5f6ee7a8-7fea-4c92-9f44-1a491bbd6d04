package com.iflytek.ebgai.ai.teacher.dataapi.mongo;


import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.Option;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;
import java.util.List;

/**
 * 问卷表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Document(collection = "xxj_ai_teacher_questionnaire")
public class QuestionnaireEntity {

    @Id
    private String id;

    /**
     * 在问卷中的索引 在问卷中固定顺序
     * 成绩排名 1
     * 作业感受 2
     * 学习目标 3
     * 学习障碍 4
     */
    @Field(name = "index")
    private int index;

    /**
     * 问卷维度，枚举值：
     * USER_RANK("成绩排名","USER_RANK"),
     * WORK_STATE("作业感受","WORK_STATE"),
     * STUDY_TARGET("学习目标","STUDY_TARGET"),
     * CORE_LITERACY("学习素养","CORE_LITERACY"),
     */
    @Field(name = "dimension")
    private String dimension;

    /**
     * 题干信息
     */
    @Field(name = "question_content")
    private String questionContent;

    /**
     * 选项信息
     */
    @Field(name = "options")
    private List<Option> options;

    /**
     * 单选 = true or 多选 =false 默认true
     */
    @Field(name = "is_radio")
    private boolean isRadio;

    /**
     * 最多选几个答案 默认=1
     */
    @Field(name = "choice_num")
    private int choiceNum;

    /**
     * 创建时间
     */
    @Field(name = "create_time")
    private Instant createTime;
}
