package com.iflytek.ebgai.ai.teacher.dataapi.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class StorageInfoRequest implements Serializable {

    private static final long serialVersionUID = -4193904145571266063L;


    /**
     * 跟踪ID 用户一次学习流程内唯一，用于后续日志记录、问题排查
     */
    @NotBlank(message = "traceId不能为空")
    private String traceId;

    /**
     * 锚点所在的目录
     * 规划范围目录列表，如: 章、节、小节、小小节、课时
     * 以目录维度存储规划结果
     * 规划总结 会用到，问卷总结无该字段
     */
    private String catalogId;


    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 引擎功能，枚举：
     * QUSNAIRE_RECNAIRE("问卷解释-问卷","QUSNAIRE_RECNAIRE"),
     * QUSNAIRE_USERPORTRAIT("问卷解释-用户画像计算","QUSNAIRE_USERPORTRAIT"),
     * QUSNAIRE_EXPLAIN("问卷解释-解释LLM","AI_TUTORING_LEARNING_PLAN"),
     * LEARNINGPLAN_CHAT("学习规划-课前聊一聊LLM","LEARNINGPLAN_CHAT"),
     * LEARNINGPLAN_CLUSTERCHOICE("学习规划-知识簇选择LLM","LEARNINGPLAN_CLUSTERCHOICE"),
     * LEARNINGPLAN_PLAN("学习规划-知识点规划LLM","LEARNINGPLAN_PLAN"),
     * LEARNINGPLAN_CHANGE("学习规划-规划调整","LEARNINGPLAN_CHANGE"),
     * OTHERS("其他功能","OTHERS");
     */
    @NotBlank(message = "引擎功能不能为空")
    private String functionCode;
}
