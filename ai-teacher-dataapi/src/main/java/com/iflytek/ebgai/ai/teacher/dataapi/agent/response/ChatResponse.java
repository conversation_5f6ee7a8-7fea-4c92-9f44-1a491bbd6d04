package com.iflytek.ebgai.ai.teacher.dataapi.agent.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class ChatResponse implements Serializable {

    /**
     * 错误码
     * 必填
     */
    private Integer code;
    /**
     * 错误信息
     * 必填
     */
    private String msg;
    /**
     * 客户端生成的traceID
     * 必填
     */
    private String traceId;
    /**
     * 消息体信息
     * 必填
     */
    private Message message;
    /**
     * 结束消息
     * responseFinish为true时才会传递
     * 非必填
     */
    private FinishInfo finishInfo;
    /**
     * 额外的数据，用于兼容特殊情况
     * 非必填
     */
    private Object extra;


}
