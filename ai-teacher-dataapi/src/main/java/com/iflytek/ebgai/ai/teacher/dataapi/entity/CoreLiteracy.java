package com.iflytek.ebgai.ai.teacher.dataapi.entity;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CoreLiteracy implements Serializable {


    private static final long serialVersionUID = 7568122607453292726L;
    /**
     * 抽象能力
     */
    private Float abstracty;

    /**
     * 计算能力
     */
    private Float computing;

    /**
     * 几何直观
     */
    private Float geometric;

    /**
     * 几何直观
     */
    private Float spatialConcept;

    /**
     * 推理能力
     */
    private Float reasoning;

    /**
     * 数据观念
     */
    private Float dataConcept;

    /**
     * 模型观念
     */
    private Float modelAwareness;

    /**
     * 应用意识
     */
    private Float applicationAwareness;

    /**
     * 创新意识
     */
    private Float innovationAwareness;

}
