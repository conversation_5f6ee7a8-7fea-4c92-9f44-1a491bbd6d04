package com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 用户选择结果
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class Option implements Serializable {

    private static final long serialVersionUID = 7584221375848547516L;
    /**
     * 问题选项序号 按照1 2 3 4顺序对应A B C D展示给用户
     */
    private int order;

    /**
     * 问题选项内容
     */
    private String content;

    /**
     * 与该选项互斥的选项 比如:当前选项=5,rejects=1,2,3,4  ,选择5后1,2,3,4要置灰
     * 默认为空
     */
    private List<Integer> rejects;
}
