package com.iflytek.ebgai.ai.teacher.dataapi.agent.Exception;

public class ChatServiceException extends Exception {
    private final int statusCode;
    private final String responseBody;

    public ChatServiceException(String message) {
        super(message);
        this.statusCode = -1;
        this.responseBody = null;
    }

    public ChatServiceException(String message, Throwable cause) {
        super(message, cause);
        this.statusCode = -1;
        this.responseBody = null;
    }

    public ChatServiceException(String message, int statusCode, String responseBody) {
        super(message);
        this.statusCode = statusCode;
        this.responseBody = responseBody;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public String getResponseBody() {
        return responseBody;
    }
}
