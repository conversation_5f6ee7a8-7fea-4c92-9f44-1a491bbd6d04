package com.iflytek.ebgai.ai.teacher.dataapi.service.impl;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireData;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.QuestionnaireRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.QuestionnaireService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class QuestionnaireServiceImpl implements QuestionnaireService {

    @Resource
    private QuestionnaireRepository questionnaireRepository;

    @Override
    public List<QuestionnaireData> query(String traceId, Integer index) {
        log.info("QuestionnaireService.query请求参数：traceId= {};index= {}", traceId, index);
        List<QuestionnaireData> questionnaireDataList = questionnaireRepository.query(index);
        log.info("QuestionnaireService.query查库返回值：traceId= {};questionnaireDataList= {}", traceId, CollectionUtils.isEmpty(questionnaireDataList) ? 0 : questionnaireDataList.size());
        return questionnaireDataList;
    }
}
