package com.iflytek.ebgai.ai.teacher.dataapi.mongo;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.Option;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;
import java.util.List;

/**
 * 问卷回答表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Document(collection = "xxj_ai_teacher_questionnaire_answer")
public class QuestionnaireAnswerEntity {

    @Id
    private String id;

    /**
     * 用户id
     */
    @Field(name = "user_id")
    private String userId;

    /**
     * 本轮id 一轮问卷流程内唯一
     */
    @Field(name = "round_id")
    private String roundId;

    /**
     * 问卷功能 ， 伴学老师问卷场景，枚举：
     * AI_TUTORING_TEACHER_QUSNAIRE("问卷解释","AI_TUTORING_TEACHER_QUSNAIRE"),
     * AI_TUTORING_LEARNING_PLAN("学习规划","AI_TUTORING_LEARNING_PLAN"),
     * OTHERS("其他LLM能力","OTHERS");
     */
    @Field(name = "biz_action")
    private String bizAction;

    /**
     * 在问卷中的索引 在问卷中固定顺序
     */
    @Field(name = "index")
    private int index;

    /**
     * 问卷维度，枚举值：
     * USER_RANK("成绩排名","USER_RANK"),
     * WORK_STATE("作业感受","WORK_STATE"),
     * STUDY_TARGET("学习目标","STUDY_TARGET"),
     * CORE_LITERACY("学习素养","CORE_LITERACY"),
     */
    @Field(name = "dimension")
    private String dimension;

    /**
     * 用户选择结果
     */
    @Field(name = "options")
    private List<Option> options;

    /**
     * 创建时间
     */
    @Field(name = "create_time")
    private Instant createTime;

    /**
     * 更新时间
     */
    @Field(name = "update_time")
    private long updateTime;

    /**
     * 跟踪ID 用户一次学习流程内唯一，用于后续日志记录、问题排查
     */
    @Field(name = "trace_id")
    private String traceId;
}
