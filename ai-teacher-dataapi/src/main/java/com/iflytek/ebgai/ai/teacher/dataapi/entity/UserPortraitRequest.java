package com.iflytek.ebgai.ai.teacher.dataapi.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UserPortraitRequest implements Serializable {

    private static final long serialVersionUID = 4483797734566179662L;
    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 跟踪ID 用户一次学习流程内唯一，用于后续日志记录、问题排查
     */
    @NotBlank(message = "traceId不能为空")
    private String traceId;

}
