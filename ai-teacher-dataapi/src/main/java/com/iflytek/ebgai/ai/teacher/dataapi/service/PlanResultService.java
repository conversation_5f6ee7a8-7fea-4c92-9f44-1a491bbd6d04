package com.iflytek.ebgai.ai.teacher.dataapi.service;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultRequest;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlanResultService {


    /**
     * 规划结果表查询接口
     *
     * @param planResultRequest
     * @return
     */
    List<PlanResultData> query(@NotNull @Valid PlanResultRequest planResultRequest);

    /**
     * 删除规划结果表查询接口
     *
     * @param planResultRequest
     * @return
     */
    void delete(@NotNull @Valid PlanResultRequest planResultRequest);

    /**
     * 规划结果表存储
     *
     * @param planResultData
     */
    void insert(@NotNull @Valid PlanResultData planResultData);

    /**
     * 规划结果表更新
     *
     * @param planResultData
     */
    void update(@NotNull @Valid PlanResultData planResultData);
}
