package com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class QuestionnaireAnswerData implements Serializable {
    private static final long serialVersionUID = -3349322573214889161L;
    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 本轮id 一轮问卷流程内唯一
     */
    private String roundId;

    /**
     * 问卷功能 ， 伴学老师问卷场景，枚举：
     * AI_TUTORING_TEACHER_QUSNAIRE("问卷解释","AI_TUTORING_TEACHER_QUSNAIRE"),
     * AI_TUTORING_LEARNING_PLAN("学习规划","AI_TUTORING_LEARNING_PLAN"),
     * OTHERS("其他LLM能力","OTHERS");
     */
    private String bizAction;

    /**
     * 在问卷中的索引 在问卷中固定顺序
     */
    private int index;

    /**
     * 问卷维度，枚举值：
     * USER_RANK("成绩排名","USER_RANK"),
     * WORK_STATE("作业感受","WORK_STATE"),
     * STUDY_TARGET("学习目标","STUDY_TARGET"),
     * CORE_LITERACY("学习素养","CORE_LITERACY"),
     */
    private String dimension;

    /**
     * 用户选择结果
     */
    private List<Option> options;


    /**
     * 跟踪ID 用户一次学习流程内唯一，用于后续日志记录、问题排查
     */
    private String traceId;

}
