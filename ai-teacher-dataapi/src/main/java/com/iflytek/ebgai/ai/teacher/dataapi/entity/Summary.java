package com.iflytek.ebgai.ai.teacher.dataapi.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class Summary implements Serializable {

    private static final long serialVersionUID = 4944548619823292949L;
    /**
     * 总结标题 比如：基础补漏型、重点突破考点、时间安排 等
     */
    private String type;

    /**
     * 总结内容展示位
     */
    private int order;

    /**
     * 总结内容 ，按照顺序排列
     */
    private List<String> contents;
}
