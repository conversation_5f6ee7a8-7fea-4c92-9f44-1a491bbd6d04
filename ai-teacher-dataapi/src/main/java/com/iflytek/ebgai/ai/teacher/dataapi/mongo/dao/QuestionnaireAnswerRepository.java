package com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireAnswerData;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.QuestionnaireAnswerEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class QuestionnaireAnswerRepository {

    private final String COLLECTION_NAME = "xxj_ai_teacher_questionnaire_answer";

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 新增问卷回答
     */
    public void insert(QuestionnaireAnswerEntity entity) {
        entity.setCreateTime(Instant.now());
        mongoTemplate.insert(entity, COLLECTION_NAME);
    }

    public void update(QuestionnaireAnswerData questionnaireAnswerData) {
        // 1. 创建查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(questionnaireAnswerData.getUserId());
        criteria.and("roundId").is(questionnaireAnswerData.getRoundId());
        Query query = new Query(criteria);
        // 2. 创建更新操作
        Update update = new Update();
        update.set("updateTime", DateTime.now().getTime());
        if (ObjectUtil.isNotEmpty(questionnaireAnswerData.getIndex())) {
            update.set("index", questionnaireAnswerData.getIndex());
        }
        if (StringUtils.isNotEmpty(questionnaireAnswerData.getDimension())) {
            update.set("dimension", questionnaireAnswerData.getDimension());
        }

        if (ObjectUtil.isNotEmpty(questionnaireAnswerData.getOptions())) {
            update.set("options", questionnaireAnswerData.getOptions());
        }
        mongoTemplate.updateFirst(query, update, QuestionnaireAnswerEntity.class, COLLECTION_NAME);
    }

    /**
     * 查询问卷回答
     *
     * @param userId
     * @param roundId
     * @return
     */
    public List<QuestionnaireAnswerData> query(String userId, String roundId) {
        // 创建一个新的查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(userId);
        criteria.and("roundId").is(roundId);
        Query query = new Query();
        query.addCriteria(criteria);
        // 执行聚合查询
        List<QuestionnaireAnswerEntity> results = mongoTemplate.find(query, QuestionnaireAnswerEntity.class, COLLECTION_NAME);

        List<QuestionnaireAnswerData> questionnaireAnswerData = BeanUtil.copyToList(results, QuestionnaireAnswerData.class);

        return questionnaireAnswerData;
    }
}
