package com.iflytek.ebgai.ai.teacher.dataapi.service.impl;

import com.iflytek.ebgai.ai.teacher.dataapi.enums.PromptType;
import com.iflytek.ebgai.ai.teacher.dataapi.service.PromptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PromptServiceImpl
 * @Date: 2025/7/9 15:45
 * @Description:
 */
@Slf4j
@Service
public class PromptServiceImpl implements PromptService {
    @Value("${engine.prompt.class_chat}")
    private String classChat;

    @Value("${engine.prompt.cluster_choice}")
    private String clusterChoice;

    @Value("${engine.prompt.portrait_infer}")
    private String portraitInfer;

    @Value("${engine.prompt.study_plan}")
    private String studyPlan;


    private static final ConcurrentHashMap<String, String> CACHE = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() throws IOException {
        loadPrompt(classChat, PromptType.CLASS_CHAT);
        loadPrompt(clusterChoice, PromptType.CLUSTER_CHOICE);
        loadPrompt(portraitInfer, PromptType.PORTRAIT_INFER);
        loadPrompt(studyPlan, PromptType.STUDY_PLAN);
    }

    private void loadPrompt(String path, PromptType promptType) throws IOException {
        log.info("初始化提示词数据:{}", path);
        try {
            String content = new String(Files.readAllBytes(Paths.get(path)), StandardCharsets.UTF_8);
            CACHE.put(promptType.getValue(), content);
            log.info("初始化提示词成功");
        } catch (Exception e) {
            log.error("初始化提示词异常,提示词文件不存在:{}", e.getMessage());
        }

    }


    @Override
    public String getContentByKey(PromptType promptType) {
        log.info("开始获取提示词：{}", promptType.getValue());
        String content = CACHE.get(promptType.getValue());
        if (StringUtils.isEmpty(content)) {
            log.error("获取提示词失败：{}", content);
            return StringUtils.EMPTY;
        }
        log.info("获取提示词成功：{}", content.length());
        return content;
    }
}
