package com.iflytek.ebgai.ai.teacher.dataapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.PlanResultEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.PlanResultRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.PlanResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PlanResultServiceImpl implements PlanResultService {

    @Resource
    private PlanResultRepository planResultRepository;


    @Override
    public List<PlanResultData> query(PlanResultRequest planResultRequest) {
        log.info("PlanResultService.query请求参数：traceId= {};planResultRequest= {}", planResultRequest.getTraceId(), JSON.toJSONString(planResultRequest));
        List<PlanResultData> planResultDataList = planResultRepository.query(planResultRequest);
        log.info("PlanResultService.query返回值：traceId= {};planResultDataList= {}", planResultRequest.getTraceId(), CollectionUtils.isEmpty(planResultDataList) ? 0 : planResultDataList.size());

        return planResultDataList;
    }

    @Override
    public void delete(PlanResultRequest planResultRequest) {
        log.info("PlanResultService.delete请求参数：traceId= {};planResultRequest= {}", planResultRequest.getTraceId(), JSON.toJSONString(planResultRequest));
        planResultRepository.delete(planResultRequest);
    }

    @Override
    public void insert(PlanResultData planResultData) {
        log.info("PlanResultService.insert请求参数：traceId= {};planResultData= {}", planResultData.getTraceId(), JSON.toJSONString(planResultData));

        PlanResultEntity planResultEntity = new PlanResultEntity();
        BeanUtil.copyProperties(planResultData, planResultEntity);
        planResultRepository.insert(planResultEntity);
    }

    @Override
    public void update(PlanResultData planResultData) {
        log.info("PlanResultService.update请求参数：traceId= {};planResultData= {}", planResultData.getTraceId(), JSON.toJSONString(planResultData));
        planResultRepository.update(planResultData);
    }


}
