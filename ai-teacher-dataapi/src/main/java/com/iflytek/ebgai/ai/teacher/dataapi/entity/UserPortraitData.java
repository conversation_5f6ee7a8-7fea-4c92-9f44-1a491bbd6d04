package com.iflytek.ebgai.ai.teacher.dataapi.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UserPortraitData implements Serializable {

    private static final long serialVersionUID = 4070002892463883518L;
    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 成绩排名 [1,100] 表示1%~100%
     */
    private String userRank;

    /**
     * 作业感受
     */
    private String workState;

    /**
     * 学习目标
     */
    private String studyTarget;

    /**
     * 核心素养
     */
    private CoreLiteracy coreLiteracy;

    /**
     * 学生层级
     */
    private String userLevel;

    /**
     * 跟踪ID 用户一次学习流程内唯一，用于后续日志记录、问题排查
     */
    private String traceId;

    /**
     * 创建时间
     */
    private Instant createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
