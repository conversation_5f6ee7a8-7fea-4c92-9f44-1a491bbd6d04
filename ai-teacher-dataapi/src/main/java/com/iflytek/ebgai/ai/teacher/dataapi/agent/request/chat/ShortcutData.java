package com.iflytek.ebgai.ai.teacher.dataapi.agent.request.chat;

import lombok.Data;

@Data
public class ShortcutData {
    /**
     * 区分快捷指令类型
     * 0-工作流；1-工具；2-知识库
     * 必填
     */
    private Integer shortcutType;
    /**
     * Type为工作流时，指定工作流ID
     * 非必填
     */
    private String workflowId;
    /**
     * Type为工作流时，指定工作流版本
     * 非必填
     */
    private String workflowVersion;
    /**
     * Type为工具时，指定工具ID
     * 非必填
     */
    private String toolId;
    /**
     * Type为工具时，指定工具版本
     * 非必填
     */
    private String toolVersion;
    /**
     * Type为知识库时，指定知识库ID
     * 非必填
     */
    private String knowledgeBaseId;
    /**
     * TType为知识库时，指定知识库版本
     * 非必填
     */
    private String knowledgeBaseType;

}