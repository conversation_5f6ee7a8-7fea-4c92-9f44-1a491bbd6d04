package com.iflytek.ebgai.ai.teacher.dataapi.agent.request;

import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.chat.ChatHistory;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.chat.ShortcutData;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ChatRequestParam {

    /**
     * 智能体ID
     * 必填
     */
    private String agentId;
    /**
     * 智能体版本ID
     * 版本功能上线前可为空
     * 必填
     */
    private String versionId;
    /**
     * 智能体类型
     * 0-意图识别；1-快捷指令
     * 必填
     */
    private Integer agentType;
    /**
     * 快捷指令数据
     * 可以快捷指令调用工作流，工具，知识库
     * 非必填
     */
    private ShortcutData shortcutData;
    /**
     * 智能体参数类型工作流勿用，内容类别
     * 0-自定义；1-文字；2-图片；3-音频；4-视频；5-文件
     * 必填
     */
    private Integer contentType;
    /**
     * 智能体聊天内容，工作流勿用，请求内容
     * txt-string;img/audio/video/file-url;
     * 必填
     */
    private String content;
    /**
     * 调试标记位
     * 标记是否测试场景，影响返回内容，和数据回流
     * 必填
     */
    private Boolean debugMode;
    /**
     * 会话ID
     * 会话是 Agent和用户之间的一段问答交互。一个会话包含一条或多条消息，想要在一轮对话中保留上下文的关联关系务必保证这个字段不变。chatID必须先通过sdk从运营接口UserChatFeign#createChat获取后方才能调用，不得传递未获取id。
     * 必填
     */
    private String chatId;
    /**
     * 对话ID
     * 消息重新生成时会传递messageID来基于这个消息重新生成
     * 非必填
     */
    private String regenMessageId;
    /**
     * 历史消息
     * 用户额外提供的历史聊天记录
     * 非必填
     */
    private List<ChatHistory> chatHistory;
    /**
     * 用户可以指定的入参
     * 用户定义参数，调度服务会将参数映射到开始节点的同名参数中
     * 工作流通过这个map传递参数给开始节点，字段按照同名映射
     * 非必填
     */
    private Map<String, String> customVariables;
    /**
     * 额外的数据
     * 用于特殊情况兼容
     * 非必填
     */
    private Map extra;


}
