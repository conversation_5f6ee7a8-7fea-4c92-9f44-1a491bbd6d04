package com.iflytek.ebgai.ai.teacher.dataapi.service.impl;

import com.iflytek.ebgai.ai.teacher.dataapi.service.AnchorKnowledgeClasschatQaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName AnchorKnowledgeClasschatQaServiceImpl
 * @Date: 2025/7/21 16:44
 * @Description:
 */
@Slf4j
@Service
public class AnchorKnowledgeClasschatQaServiceImpl implements AnchorKnowledgeClasschatQaService {
    @Value("${engine.knowledge.qa}")
    private String knowledgeQa;

    private static final Map<String, String> CACHE = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() throws IOException {
        loadKnowledge(knowledgeQa);
    }

    private void loadKnowledge(String knowledgeQa) {
        log.info("初始化锚点知识问答数据:{}", knowledgeQa);
        try {
            Path path = Paths.get(knowledgeQa);
            List<String> allKnowledgeList = Files.readAllLines(path);

            for (String linestr : allKnowledgeList) {
                String[] lineLis = linestr.split("\t");
                if (lineLis.length > 1) {
                    CACHE.put(lineLis[0], lineLis[1]);
                }
            }
            log.info("初始化锚点知识问答数据成功");
        } catch (Exception e) {
            log.error("初始化锚点知识问答数据异常,文件不存在:{}", e.getMessage());
        }
    }

    @Override
    public Map<String, String> getContent() {
        return CACHE;
    }
}
