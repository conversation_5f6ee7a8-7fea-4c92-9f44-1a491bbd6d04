package com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class QuestionnaireData implements Serializable {

    private static final long serialVersionUID = -61740157852471628L;
    /**
     * 在问卷中的索引 在问卷中固定顺序
     */
    private int index;

    /**
     * 问卷维度，枚举值：
     * USER_RANK("成绩排名","USER_RANK"),
     * WORK_STATE("作业感受","WORK_STATE"),
     * STUDY_TARGET("学习目标","STUDY_TARGET"),
     * CORE_LITERACY("学习素养","CORE_LITERACY"),
     */
    private String dimension;

    /**
     * 题干信息
     */
    private String questionContent;

    /**
     * 选项信息
     */
    private List<Option> options;

    /**
     * 单选 = true or 多选 =false 默认true
     */
    private boolean isRadio;

    /**
     * 最多选几个答案 默认=1
     */
    private int choiceNum;

    /**
     * 更新时间
     */
    private long updateTime;
}
