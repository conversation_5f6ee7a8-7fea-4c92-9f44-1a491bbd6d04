package com.iflytek.ebgai.ai.teacher.dataapi.agent.chat;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.Exception.ChatServiceException;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.constant.Constant;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.ChatHeader;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.ChatRequestParam;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@Service
public class AiAbilityServiceImpl implements AiAbilityService {

    @Value("${chat.app.id:xxjcpx-xcjzx}")
    public String appId;
    @Value("${chat.sse.url:https://ai-thor-prelt.ceshiservice.cn/ai-thor-dispatcher/api/v1/workflow/chat}")
    public String sseUrl;
    @Value("${chat.connect.timeout:5000}")
    public int connectTimeout;
    @Value("${chat.socket.timeout:30000}")
    public int socketTimeout;
    @Value("${chat.app.agent.id:682557525cf2002c9fb76e1d}")
    public String agentId;

    @Value("${chat.http.retry.time:2}")
    public Integer retryTime;

    public interface SseListener {
        void onEvent(ChatResponse chatResponse);
    }

    @Override
    public void getChatResult(ChatRequestParam param, ChatHeader chatHeader, SseListener listener) throws Exception {
        param.setAgentId(agentId);
        String requestBody = JSONObject.toJSONString(param);
        long startTime = System.currentTimeMillis();
        String traceId = chatHeader.getTraceId();
        log.info("traceId={} getChatResult请求入参param={}, chatHeader={}, time={}", traceId, requestBody, JSONObject.toJSONString(chatHeader), startTime);
        int maxRetries = retryTime;
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try (CloseableHttpClient httpClient = createHttpClient(connectTimeout, socketTimeout)) {
                HttpPost request = new HttpPost(sseUrl);
                addHeaders(request, chatHeader);
                request.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    int statusCode = response.getStatusLine().getStatusCode();
                    if (statusCode != Constant.HTTP_SUCCESS_CODE) {
                        String responseBody = EntityUtils.toString(response.getEntity());
                        log.error("traceId={} getChatResult请求失败resp={}", traceId, responseBody);
                        throw new ChatServiceException("HTTP Error " + statusCode, statusCode, responseBody);
                    }

                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                        String line;
                        //sse返回数据: data:{"code":0,"msg":"连接建立","messageContent":null,"traceId":"5587c8e9-1204-4d63-861b-761de818d6cf","message":{"responseFinish":false,"stage":"workflow","stageFinish":false,"seqId":null,"contentType":0,"content":"","outputNode":null,"outputNodeFinish":null,"stream":null,"chatId":"5587c8e9-1204-4d63-861b-761de818d6cf","messageId":"68482cef5cf2bd9b3752d1d0","replyId":"68482cef5cf2bd9b3752d1cf"},"finishInfo":{"startTime":1749560559223,"endTime":0,"tokenInfo":null,"debugInfo":null},"extra":null}
                        while ((line = reader.readLine()) != null) {
                            if (line.startsWith(Constant.SSE_START)) {
                                listener.onEvent(parseChatResponse(line));
                            }
                        }
                    }
                }
                // 请求成功直接返回
                long endTime = System.currentTimeMillis();
                log.info("traceId={} getChatResult请求成功，time={}, 耗时={}", traceId, endTime, endTime - startTime);
                return;

            } catch (ConnectException | SocketTimeoutException e) {
                if (attempt >= maxRetries) {
                    log.error("traceId={} getChatResult重试失败e={}", traceId, e.getMessage());
                    throw new ChatServiceException("Connection failed after " + maxRetries + " retries", e);
                }
                log.error("traceId={} Retry getChatResult attempt={}, e={}", traceId, attempt + 1, e.getMessage());
            }
        }
    }

    private ChatResponse parseChatResponse(String line) {
        String json = line.substring(5).trim();
        return JSONObject.parseObject(json, ChatResponse.class);
    }

    private CloseableHttpClient createHttpClient(int connectTimeout, int socketTimeout) {
        RequestConfig config = RequestConfig.custom().setConnectTimeout(connectTimeout)  // 连接超时时间（毫秒）
                .setSocketTimeout(socketTimeout)    // 数据传输超时时间
                .build();

        return HttpClients.custom().setDefaultRequestConfig(config).build();
    }

    private void addHeaders(HttpPost request, ChatHeader chatHeader) {
        request.setHeader("Accept", "text/event-stream");
        request.setHeader("Content-Type", "application/json");
        request.setHeader("Cache-Control", "no-cache");

        request.setHeader("X-TRACE-ID", chatHeader.getTraceId());
        request.setHeader("X-USER-ID", chatHeader.getUserId());
        request.setHeader("X-DEVICE-ID", chatHeader.getDeviceId());
        request.setHeader("X-APPID", appId);
        request.setHeader("X-PLATFORM", chatHeader.getPlatForm());
        request.setHeader("X-USER-TAG", chatHeader.getUserTag());
        String token = "Bearer " + chatHeader.getAuthorization();
        request.setHeader("Authorization", token);

        if (chatHeader.getForwardedFor() != null) {
            request.addHeader("X-FORWARDED-FOR", chatHeader.getForwardedFor());
        }
        if (chatHeader.getExtra() != null) {
            for (Map.Entry<?, ?> entry : ((Map<?, ?>) chatHeader.getExtra()).entrySet()) {
                request.setHeader(entry.getKey().toString(), entry.getValue().toString());
            }
        }
    }
}
