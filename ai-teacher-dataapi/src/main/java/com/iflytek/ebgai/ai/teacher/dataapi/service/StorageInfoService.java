package com.iflytek.ebgai.ai.teacher.dataapi.service;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoRequest;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface StorageInfoService {


    /**
     * 问卷总结+规划总结表 查询
     *
     * @param storageInfoRequest
     * @return
     */
    List<StorageInfoData> query(@NotNull @Valid StorageInfoRequest storageInfoRequest);

    /**
     * 问卷总结+规划总结表 查询最新一条
     *
     * @param storageInfoRequest
     * @return
     */
    StorageInfoData queryLatest(@NotNull @Valid StorageInfoRequest storageInfoRequest);

    /**
     * 删除问卷总结+规划总结表接口
     *
     * @param storageInfoRequest
     * @return
     */
    void delete(@NotNull @Valid StorageInfoRequest storageInfoRequest);

    /**
     * 问卷总结+规划总结表存储
     *
     * @param storageInfoData
     */
    void insert(@NotNull @Valid StorageInfoData storageInfoData);

    /**
     * 问卷总结+规划总结表更新
     *
     * @param storageInfoData
     */
    void update(@NotNull @Valid StorageInfoData storageInfoData);


}
