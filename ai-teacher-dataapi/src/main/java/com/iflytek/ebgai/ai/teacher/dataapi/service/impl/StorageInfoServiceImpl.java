package com.iflytek.ebgai.ai.teacher.dataapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.StorageInfoEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.StorageInfoRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.StorageInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class StorageInfoServiceImpl implements StorageInfoService {

    @Resource
    private StorageInfoRepository storageInfoRepository;

    @Override
    public List<StorageInfoData> query(StorageInfoRequest storageInfoRequest) {
        log.info("StorageInfoService.query请求参数：traceId= {};storageInfoRequest= {}", storageInfoRequest.getTraceId(), JSON.toJSONString(storageInfoRequest));
        List<StorageInfoData> storageInfoDataList = storageInfoRepository.query(storageInfoRequest);
        log.info("StorageInfoService.query返回值：traceId= {};storageInfoDataList= {}", storageInfoRequest.getTraceId(), CollectionUtils.isEmpty(storageInfoDataList) ? 0 : storageInfoDataList.size());
        return storageInfoDataList;
    }

    @Override
    public StorageInfoData queryLatest(StorageInfoRequest storageInfoRequest) {
        log.info("StorageInfoService.queryLatest 请求参数：traceId= {};storageInfoRequest= {}", storageInfoRequest.getTraceId(), JSON.toJSONString(storageInfoRequest));
        return storageInfoRepository.queryLatest(storageInfoRequest);
    }

    @Override
    public void delete(StorageInfoRequest storageInfoRequest) {
        log.info("StorageInfoService.delete请求参数：traceId= {};storageInfoRequest= {}", storageInfoRequest.getTraceId(), JSON.toJSONString(storageInfoRequest));
        storageInfoRepository.delete(storageInfoRequest);
    }

    @Override
    public void insert(StorageInfoData storageInfoData) {
        log.info("StorageInfoService.insert请求参数：traceId= {};storageInfoRequest= {}", storageInfoData.getTraceId(), JSON.toJSONString(storageInfoData));

        StorageInfoEntity entity = new StorageInfoEntity();
        BeanUtil.copyProperties(storageInfoData, entity);
        storageInfoRepository.insert(entity);

    }

    @Override
    public void update(StorageInfoData storageInfoData) {
        log.info("StorageInfoService.update请求参数：traceId= {};storageInfoRequest= {}", storageInfoData.getTraceId(), JSON.toJSONString(storageInfoData));
        storageInfoRepository.update(storageInfoData);

    }
}
