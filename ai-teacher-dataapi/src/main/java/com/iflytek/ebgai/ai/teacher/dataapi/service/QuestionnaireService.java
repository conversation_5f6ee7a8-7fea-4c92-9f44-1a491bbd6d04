package com.iflytek.ebgai.ai.teacher.dataapi.service;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireData;
import lombok.NonNull;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface QuestionnaireService {

    /**
     * 问卷表查询接口
     *
     * @param traceId
     * @param index
     * @return
     */
    List<QuestionnaireData> query(@NotBlank String traceId, @NonNull Integer index);
}
