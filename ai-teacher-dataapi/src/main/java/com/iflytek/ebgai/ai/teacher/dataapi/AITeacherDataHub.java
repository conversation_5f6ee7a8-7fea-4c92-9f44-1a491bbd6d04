package com.iflytek.ebgai.ai.teacher.dataapi;

import com.iflytek.ebgai.ai.teacher.dataapi.agent.chat.AiAbilityService;
import com.iflytek.ebgai.ai.teacher.dataapi.service.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 数据访问句柄 统一工具入口
 *
 * <AUTHOR>
 * @date 2022/2/28 8:48 下午
 */
@Component
public class AITeacherDataHub implements ApplicationContextAware {
    /**
     * spring上下文
     */
    private static ApplicationContext applicationContext;

    /**
     * 规划结果接口
     */
    public static PlanResultService getPlanResultService() {
        return applicationContext.getBean(PlanResultService.class);
    }

    /**
     * 问卷回答数据接口
     *
     * @return
     */
    public static QuestionnaireAnswerService getQuestionnaireAnswerService() {
        return applicationContext.getBean(QuestionnaireAnswerService.class);
    }

    public static PromptService getPromptService() {
        return applicationContext.getBean(PromptService.class);
    }

    public static AnchorKnowledgeClasschatQaService getAnchorKnowledgeClasschatQaService() {
        return applicationContext.getBean(AnchorKnowledgeClasschatQaService.class);
    }

    /**
     * 问卷数据接口
     *
     * @return
     */
    public static QuestionnaireService getQuestionnaireService() {
        return applicationContext.getBean(QuestionnaireService.class);
    }

    /**
     * 问卷总结+知识簇选择+规划总结表接口
     */
    public static StorageInfoService getStorageInfoService() {
        return applicationContext.getBean(StorageInfoService.class);
    }

    /**
     * 用户画像表接口
     */
    public static UserPortraitService getUserPortraitService() {
        return applicationContext.getBean(UserPortraitService.class);
    }

    /**
     * 大模型接口
     */
    public static AiAbilityService getAiAbilityService() {
        return applicationContext.getBean(AiAbilityService.class);
    }


    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        AITeacherDataHub.applicationContext = applicationContext;
    }
}
