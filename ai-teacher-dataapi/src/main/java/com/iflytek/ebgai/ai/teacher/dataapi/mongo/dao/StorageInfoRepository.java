package com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.enums.DeleteFlagEnum;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.StorageInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class StorageInfoRepository {

    private final String COLLECTION_NAME = "xxj_ai_teacher_storage_info";

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 新增问卷总结+知识簇选择+规划总结表：保留多份，取最新一条给引擎
     */
    public void insert(StorageInfoEntity entity) {
        entity.setCreateTime(Instant.now());
        entity.setIsDelete(DeleteFlagEnum.EXIST.getFlag());
        mongoTemplate.insert(entity, COLLECTION_NAME);
    }

    /**
     * 删除问卷总结+规划总结表
     */
    public void delete(StorageInfoRequest storageInfoRequest) {
        // 1. 创建查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(storageInfoRequest.getUserId());
        criteria.and("functionCode").is(storageInfoRequest.getFunctionCode());
        criteria.and("isDelete").is(DeleteFlagEnum.EXIST.getFlag());

        Query query = new Query(criteria);
        // 2. 创建更新操作
        Update update = new Update();
        update.set("isDelete", DeleteFlagEnum.DELETED.getFlag());
        update.set("updateTime", DateTime.now().getTime());
        mongoTemplate.updateFirst(query, update, StorageInfoEntity.class, COLLECTION_NAME);
    }

    /**
     * 更新问卷总结+规划总结表
     */
    public void update(StorageInfoData storageInfoData) {
        // 1. 创建查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(storageInfoData.getUserId());
        criteria.and("functionCode").is(storageInfoData.getFunctionCode());
        criteria.and("isDelete").is(DeleteFlagEnum.EXIST.getFlag());
        if (ObjectUtil.isNotEmpty(storageInfoData.getCatalogId())) {
            criteria.and("catalogId").is(storageInfoData.getCatalogId());
        }
        Query query = new Query(criteria);
        // 2. 创建更新操作
        Update update = new Update();
        update.set("updateTime", DateTime.now().getTime());
        if (ObjectUtil.isNotEmpty(storageInfoData.getUserLevel())) {
            update.set("userLevel", storageInfoData.getUserLevel());
        }
        if (StringUtils.isNotEmpty(storageInfoData.getRoundId())) {
            update.set("roundId", storageInfoData.getRoundId());
        }
        if (StringUtils.isNotEmpty(storageInfoData.getQuery())) {
            update.set("query", storageInfoData.getQuery());
        }
        if (StringUtils.isNotEmpty(storageInfoData.getOriContent())) {
            update.set("oriContent", storageInfoData.getOriContent());
        }
        if (ObjectUtil.isNotEmpty(storageInfoData.getSummaryInfo())) {
            update.set("summaryInfo", storageInfoData.getSummaryInfo());
        }
        mongoTemplate.updateFirst(query, update, StorageInfoEntity.class, COLLECTION_NAME);
    }

    /**
     * 查询问卷总结+知识簇选择+规划总结表
     *
     * @param storageInfoRequest
     * @return
     */
    public List<StorageInfoData> query(StorageInfoRequest storageInfoRequest) {
        // 创建一个新的查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(storageInfoRequest.getUserId());
        criteria.and("isDelete").is(DeleteFlagEnum.EXIST.getFlag());
        if (StringUtils.isNotEmpty(storageInfoRequest.getCatalogId())) {
            criteria.and("catalogId").is(storageInfoRequest.getCatalogId());
        }
        criteria.and("functionCode").is(storageInfoRequest.getFunctionCode());

        Query query = new Query();
        query.addCriteria(criteria);
        // 执行聚合查询
        List<StorageInfoEntity> results = mongoTemplate.find(query, StorageInfoEntity.class, COLLECTION_NAME);

        List<StorageInfoData> storageInfoData = BeanUtil.copyToList(results, StorageInfoData.class);

        return storageInfoData;
    }

    /**
     * 查询问卷总结+知识簇选择+规划总结表，最新一条
     *
     * @param storageInfoRequest
     * @return
     */
    public StorageInfoData queryLatest(StorageInfoRequest storageInfoRequest) {
        try {
            // 创建一个新的查询条件
            Criteria criteria = new Criteria();
            criteria.and("userId").is(storageInfoRequest.getUserId());
            criteria.and("isDelete").is(DeleteFlagEnum.EXIST.getFlag());
            if (StringUtils.isNotEmpty(storageInfoRequest.getCatalogId())) {
                criteria.and("catalogId").is(storageInfoRequest.getCatalogId());
            }
            criteria.and("functionCode").is(storageInfoRequest.getFunctionCode());
            Query query = new Query();
            query.addCriteria(criteria);
            query.with(Sort.by(Sort.Direction.DESC, "update_time"));
            query.limit(1);
            // 执行聚合查询
            StorageInfoEntity results = mongoTemplate.findOne(query, StorageInfoEntity.class, COLLECTION_NAME);
            if (Objects.isNull(results)) {
                return null;
            }
            return BeanUtil.copyProperties(results, StorageInfoData.class);
        } catch (Exception e) {
            log.error("queryLatest error", e);
            return null;
        }
    }
}
