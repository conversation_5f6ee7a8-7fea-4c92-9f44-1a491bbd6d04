package com.iflytek.ebgai.ai.teacher.dataapi.entity.plan;


import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PlanResultRequest implements Serializable {


    private static final long serialVersionUID = 1246459461871837965L;
    /**
     * 业务方功能（服务层定义）
     */
    @NotBlank(message = "业务方功能不能为空")
    private String bizAction;

    /**
     * 学习场景（服务层定义）,默认=SYNC_TUTORING
     */
    private String studyCode;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 锚点所在的目录
     * 规划范围目录列表，如: 章、节、小节、小小节、课时
     * 以目录维度存储规划结果
     */
    private String catalogId;

    /**
     * 跟踪ID 用户一次学习流程内唯一，用于后续日志记录、问题排查
     */
    @NotBlank(message = "traceId不能为空")
    private String traceId;

    /**
     * 点ID
     */
    private String nodeId;

}
