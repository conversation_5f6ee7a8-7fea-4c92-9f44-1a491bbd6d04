package com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.UserPortraitEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class UserPortraitRepository {

    private final String COLLECTION_NAME = "xxj_ai_teacher_user_portrait";

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 新增用户画像表：用户id就是唯一索引，每个用户只保留一份画像
     */
    public void insert(UserPortraitEntity entity) {
        entity.setCreateTime(Instant.now());
        mongoTemplate.insert(entity, COLLECTION_NAME);
    }

    public void update(UserPortraitData userPortrait) {
        // 1. 创建查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(userPortrait.getUserId());
        Query query = new Query(criteria);
        // 2. 创建更新操作
        Update update = new Update();
        update.set("updateTime", DateTime.now().getTime());
        if (ObjectUtil.isNotEmpty(userPortrait.getUserName())) {
            update.set("userName", userPortrait.getUserName());
        }
        if (ObjectUtil.isNotEmpty(userPortrait.getUserLevel())) {
            update.set("userLevel", userPortrait.getUserLevel());
        }
        if (StringUtils.isNotEmpty(userPortrait.getUserRank())) {
            update.set("userRank", userPortrait.getUserRank());
        }
        if (StringUtils.isNotEmpty(userPortrait.getWorkState())) {
            update.set("workState", userPortrait.getWorkState());
        }
        if (StringUtils.isNotEmpty(userPortrait.getStudyTarget())) {
            update.set("studyTarget", userPortrait.getStudyTarget());
        }
        if (ObjectUtil.isNotEmpty(userPortrait.getCoreLiteracy())) {
            update.set("coreLiteracy", userPortrait.getCoreLiteracy());
        }
        mongoTemplate.updateFirst(query, update, UserPortraitEntity.class, COLLECTION_NAME);
    }

    /**
     * 用户画像表
     *
     * @param userPortraitRequest
     * @return
     */
    public List<UserPortraitData> query(UserPortraitRequest userPortraitRequest) {
        // 创建一个新的查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(userPortraitRequest.getUserId());

        Query query = new Query();
        query.addCriteria(criteria);
        // 执行聚合查询
        List<UserPortraitEntity> results = mongoTemplate.find(query, UserPortraitEntity.class, COLLECTION_NAME);

        List<UserPortraitData> userPortraitData = BeanUtil.copyToList(results, UserPortraitData.class);

        return userPortraitData;
    }

    public void delete(String userId) {
        Criteria criteria = new Criteria();
        criteria.and("userId").is(userId);
        Query query = new Query();
        query.addCriteria(criteria);
        mongoTemplate.remove(query, UserPortraitEntity.class, COLLECTION_NAME);
    }
}
