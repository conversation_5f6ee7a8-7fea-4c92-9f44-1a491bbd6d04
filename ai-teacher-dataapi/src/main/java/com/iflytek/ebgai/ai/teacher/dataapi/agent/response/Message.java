package com.iflytek.ebgai.ai.teacher.dataapi.agent.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class Message implements Serializable {
    /**
     * 返回是否结束
     * 非流式场景固定为true，流式场景最后一帧true，其他为false
     * 必填
     */
    private Boolean responseFinish;
    /**
     * 智能体执行阶段
     * plan/workflow
     * 必填
     */
    private String stage;
    /**
     * 阶段执行完成
     * 通知前端某个阶段执行完成
     * 必填
     */
    private Boolean stageFinish;
    /**
     * 流式标记位
     * 表示流式包的顺序位，非流式为0
     * 必填
     */
    private Integer seqId;
    /**
     * 请求内容类型
     * 必填
     */
    private Integer contentType;
    /**
     * 请求内容
     * txt-string;img/audio/video-url;
     * 必填
     */
    private String content;
    /**
     * 全局唯一的标识字段，标记是哪个输出节点的输出数据
     * 非必填
     */
    private String outputNode;
    /**
     * 输出节点输出完毕标志
     * 非流式场景固定为true，流式场景最后一帧true，其他为false
     * 非必填
     */
    private Boolean outputNodeFinish;
    /**
     * 会话ID
     * 会话是 Agent和用户之间的一段问答交互。一个会话包含一条或多条消息
     * 必填
     */
    private String chatId;
    /**
     * 消息ID
     * 服务端生成的正式ID，客户端应该更换
     * 必填
     */
    private String messageId;
    /**
     * 回复消息ID
     * 用于给用户信息传递永久ID
     * 必填
     */
    private String replyId;
}
