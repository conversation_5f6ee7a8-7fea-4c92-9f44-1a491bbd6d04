package com.iflytek.ebgai.ai.teacher.dataapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireAnswerData;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.QuestionnaireAnswerEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.QuestionnaireAnswerRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.QuestionnaireAnswerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class QuestionnaireAnswerServiceImpl implements QuestionnaireAnswerService {

    @Resource
    private QuestionnaireAnswerRepository questionnaireAnswerRepository;

    @Override
    public List<QuestionnaireAnswerData> query(String traceId, String userId, String roundId) {
        log.info("QuestionnaireAnswerService.query请求参数：traceId= {};userId= {};roundId= {}", traceId, userId, roundId);
        List<QuestionnaireAnswerData> dataList = questionnaireAnswerRepository.query(userId, roundId);
        log.info("QuestionnaireAnswerService.query返回值：traceId= {};dataList= {}", traceId, CollectionUtils.isEmpty(dataList) ? 0 : dataList.size());
        return dataList;
    }

    @Override
    public void insert(QuestionnaireAnswerData questionnaireAnswerData) {
        log.info("QuestionnaireAnswerService.insert请求参数：traceId= {};questionnaireAnswerData= {}", questionnaireAnswerData.getTraceId(), JSON.toJSONString(questionnaireAnswerData));

        QuestionnaireAnswerEntity questionnaireAnswerEntity = new QuestionnaireAnswerEntity();
        BeanUtil.copyProperties(questionnaireAnswerData, questionnaireAnswerEntity);
        questionnaireAnswerRepository.insert(questionnaireAnswerEntity);
    }

    @Override
    public void update(QuestionnaireAnswerData questionnaireAnswerData) {
        log.info("QuestionnaireAnswerService.update请求参数：traceId= {};questionnaireAnswerData= {}", questionnaireAnswerData.getTraceId(), JSON.toJSONString(questionnaireAnswerData));
        questionnaireAnswerRepository.update(questionnaireAnswerData);
    }
}
