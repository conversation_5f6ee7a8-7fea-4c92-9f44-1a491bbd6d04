package com.iflytek.ebgai.ai.teacher.dataapi.entity.plan;


import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 规划结果数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PlanResultData implements Serializable {

    private static final long serialVersionUID = 2274657792677658352L;
    /**
     * 业务方功能（服务层定义）
     */
    private String bizAction;

    /**
     * 学习场景（服务层定义）,默认=SYNC_TUTORING
     */
    private String studyCode;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 更新时间
     */
    private long updateTime;

    /**
     * 规划排序 1 2 3 4优先级
     */
    private int order;

    /**
     * 点ID
     */
    private String nodeId;

    /**
     * 点类型
     */
    private String nodeType;

    /**
     * 点属性 中心点 or 延展点  枚举值:
     * CENTRAL_POINT("中心点","CENTRAL_POINT"),
     * EXTENSION_POINT("延展点","EXTENSION_POINT"),
     * OTHERS("其他点","OTHERS");
     */
    private String nodeAttribute;

    /**
     * 学习行为，枚举值：
     * LEARN("学","LEARN"),
     * PRACTICE("练","PRACTICE"),
     * OTHERS("其他学习行为","OTHERS");
     */
    private String learndBehavior;

    /**
     * 学习时长 单位min
     */
    private int learndTimes;

    /**
     * 锚点所在的目录
     * 规划范围目录列表，如: 章、节、小节、小小节、课时
     * 以目录维度存储规划结果
     */
    private String catalogId;

    /**
     * 跟踪ID 用户一次学习流程内唯一，用于后续日志记录、问题排查
     */
    @NotBlank(message = "traceId不能为空")
    private String traceId;
}
