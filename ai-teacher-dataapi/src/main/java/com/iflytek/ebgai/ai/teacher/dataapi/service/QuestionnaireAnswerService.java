package com.iflytek.ebgai.ai.teacher.dataapi.service;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireAnswerData;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface QuestionnaireAnswerService {

    /**
     * 问卷回答表查询接口
     *
     * @param traceId
     * @param userId
     * @param roundId
     * @return
     */
    List<QuestionnaireAnswerData> query(@NotBlank String traceId, @NotBlank String userId, @NotBlank String roundId);

    /**
     * 问卷回答表新增
     *
     * @param questionnaireAnswerData
     */
    void insert(@NotNull @Valid QuestionnaireAnswerData questionnaireAnswerData);

    /**
     * 问卷回答表更新
     *
     * @param questionnaireAnswerData
     */
    void update(@NotNull @Valid QuestionnaireAnswerData questionnaireAnswerData);
}
