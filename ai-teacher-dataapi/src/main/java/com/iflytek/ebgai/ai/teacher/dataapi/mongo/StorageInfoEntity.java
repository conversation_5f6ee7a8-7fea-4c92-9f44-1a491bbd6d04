package com.iflytek.ebgai.ai.teacher.dataapi.mongo;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.SummaryInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;


/**
 * <AUTHOR>
 * 问卷总结+知识簇选择+规划总结表
 */
@Data
@Document(collection = "xxj_ai_teacher_storage_info")
@NoArgsConstructor
public class StorageInfoEntity {


    @Id
    private String id;

    /**
     * 用户id
     */
    @Field(name = "user_id")
    private String userId;

    /**
     * 用户层级 or 用户类型（只在问卷总结输出并存储），枚举值：conventional、highScoreAdvanced、thinkingExpansion
     * CONVENTIONAL("基础用户","conventional"),
     * HIGHSCOREADVANCED("进阶用户","highScoreAdvanced"),
     * THINKINGEXPANSION("拔高用户","thinkingExpansion");
     */
    @Field(name = "user_level")
    private String userLevel;

    /**
     * 跟踪ID 用户一次学习流程内唯一
     */
    @Field(name = "trace_id")
    private String traceId;

    /**
     * 业务方功能,枚举：
     * AI_TUTORING_TEACHER_QUSNAIRE("问卷解释","AI_TUTORING_TEACHER_QUSNAIRE"),
     * AI_TUTORING_LEARNING_PLAN("学习规划","AI_TUTORING_LEARNING_PLAN"),
     * OTHERS("其他LLM能力","OTHERS");
     */
    @Field(name = "biz_action")
    private String bizAction;

    /**
     * 引擎功能，枚举：
     * QUSNAIRE_RECNAIRE("问卷解释-问卷","QUSNAIRE_RECNAIRE"),
     * QUSNAIRE_USERPORTRAIT("问卷解释-用户画像计算","QUSNAIRE_USERPORTRAIT"),
     * QUSNAIRE_EXPLAIN("问卷解释-解释LLM","AI_TUTORING_LEARNING_PLAN"),
     * LEARNINGPLAN_CHAT("学习规划-课前聊一聊LLM","LEARNINGPLAN_CHAT"),
     * LEARNINGPLAN_CLUSTERCHOICE("学习规划-知识簇选择LLM","LEARNINGPLAN_CLUSTERCHOICE"),
     * LEARNINGPLAN_PLAN("学习规划-知识点规划LLM","LEARNINGPLAN_PLAN"),
     * LEARNINGPLAN_CHANGE("学习规划-规划调整","LEARNINGPLAN_CHANGE"),
     * OTHERS("其他功能","OTHERS");
     */
    @Field(name = "function_code")
    private String functionCode;

    /**
     * 本轮id 一轮问卷流程内唯一，可以和会话id共用
     */
    @Field(name = "round_id")
    private String roundId;

    /**
     * 用户输入
     */
    @Field(name = "query")
    private String query;

    /**
     * 原始大模型输出 去除think
     */
    @Field(name = "ori_content")
    private String oriContent;

    /**
     * 锚点所在的目录
     * 规划范围目录列表，如: 章、节、小节、小小节、课时
     * 以目录维度存储规划结果
     * 规划总结 会用到，问卷总结无该字段
     */
    @Field(name = "catalog_id")
    private String catalogId;

    /**
     * 格式化 问卷总结、规划总结结果 非流式输出；问卷总结+规划总结格式化存储数据结构（学习机业务方使用）
     */
    @Field(name = "summary_info")
    private SummaryInfo summaryInfo;

    /**
     * 更新时间
     */
    @Field(name = "update_time")
    private Long updateTime;

    /**
     * 创建时间
     */
    @Field(name = "create_time")
    private Instant createTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @Field(name = "is_delete")
    private Integer isDelete;
}
