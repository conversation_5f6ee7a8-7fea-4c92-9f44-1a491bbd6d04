package com.iflytek.ebgai.ai.teacher.dataapi.mongo;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.CoreLiteracy;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

/**
 * <AUTHOR>
 * 用户画像表
 */
@Data
@Accessors(chain = true)
@Document(collection = "xxj_ai_teacher_user_portrait")
public class UserPortraitEntity {

    @Id
    private String id;

    /**
     * 用户id
     */
    @Field(name = "user_id")
    private String userId;

    /**
     * 用户姓名
     */
    @Field(name = "user_name")
    private String userName;

    /**
     * 成绩排名 [1,100] 表示1%~100%
     */
    @Field(name = "user_rank")
    private String userRank;

    /**
     * 作业感受
     */
    @Field(name = "work_state")
    private String workState;

    /**
     * 学习目标
     */
    @Field(name = "study_target")
    private String studyTarget;

    /**
     * 核心素养
     */
    @Field(name = "core_literacy")
    private CoreLiteracy coreLiteracy;

    /**
     * 学生层级
     */
    @Field(name = "user_level")
    private String userLevel;

    /**
     * 创建时间
     */
    @Field(name = "create_time")
    private Instant createTime;

    /**
     * 更新时间
     */
    @Field(name = "update_time")
    private Long updateTime;
}
