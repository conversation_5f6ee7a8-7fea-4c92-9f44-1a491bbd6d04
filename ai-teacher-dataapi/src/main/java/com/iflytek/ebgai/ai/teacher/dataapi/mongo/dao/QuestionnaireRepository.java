package com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao;

import cn.hutool.core.bean.BeanUtil;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireData;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.QuestionnaireEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class QuestionnaireRepository {

    private final String COLLECTION_NAME = "xxj_ai_teacher_questionnaire";

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 新增问卷：提前预制进去的数据
     */
    public void insert(QuestionnaireEntity entity) {
        entity.setCreateTime(Instant.now());
        mongoTemplate.insert(entity, COLLECTION_NAME);
    }

    /**
     * 查询问卷
     *
     * @param index
     * @return
     */
    public List<QuestionnaireData> query(Integer index) {
        // 创建一个新的查询条件
        Criteria criteria = new Criteria();
        criteria.and("index").is(index);
        Query query = new Query();
        query.addCriteria(criteria);
        // 执行聚合查询
        List<QuestionnaireEntity> results = mongoTemplate.find(query, QuestionnaireEntity.class, COLLECTION_NAME);

        List<QuestionnaireData> questionnaireData = BeanUtil.copyToList(results, QuestionnaireData.class);

        return questionnaireData;
    }
}
