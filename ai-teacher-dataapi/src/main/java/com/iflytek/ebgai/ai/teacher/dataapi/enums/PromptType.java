package com.iflytek.ebgai.ai.teacher.dataapi.enums;

public enum PromptType {
    CLASS_CHAT("classChat"), CLUSTER_CHOICE("clusterChoice"), PORTRAIT_INFER("portraitInfer"), STUDY_PLAN("studyPlan");

    private final String value;

    PromptType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return this.value;
    }

    // 可选：从字符串值反向查找枚举
    public static PromptType fromValue(String value) {
        for (PromptType type : PromptType.values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的提示词类型: " + value);
    }
}