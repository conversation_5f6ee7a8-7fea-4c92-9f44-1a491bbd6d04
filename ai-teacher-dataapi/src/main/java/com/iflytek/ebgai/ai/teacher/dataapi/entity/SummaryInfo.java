package com.iflytek.ebgai.ai.teacher.dataapi.entity;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SummaryInfo implements Serializable {

    private static final long serialVersionUID = -8882938495517612287L;
    /**
     * 总结详情
     */
    private List<Summary> summaries;

    /**
     * 总结类型 ：问卷总结 or 规划总结 ，枚举：
     * AI_TUTORING_TEACHER_QUSNAIRE("问卷解释","AI_TUTORING_TEACHER_QUSNAIRE"),
     * AI_TUTORING_LEARNING_PLAN("学习规划","AI_TUTORING_LEARNING_PLAN"),
     * OTHERS("其他LLM能力","OTHERS");
     */
    private String summaryType;
}
