package com.iflytek.ebgai.ai.teacher.dataapi.service;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitRequest;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserPortraitService {


    /**
     * 用户画像表查询
     *
     * @param userPortraitRequest
     * @return
     */
    List<UserPortraitData> query(@NotNull @Valid UserPortraitRequest userPortraitRequest);

    /**
     * 用户画像表存储
     *
     * @param userPortrait
     */
    void insert(@NotNull @Valid UserPortraitData userPortrait);

    /**
     * 用户画像表更新
     *
     * @param userPortrait
     */
    void update(@NotNull @Valid UserPortraitData userPortrait);

    /**
     * 用户画像删除
     *
     * @param userId
     */
    void delete(@NotNull @Valid String userId);
}
