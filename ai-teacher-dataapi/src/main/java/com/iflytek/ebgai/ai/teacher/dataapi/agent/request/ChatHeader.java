package com.iflytek.ebgai.ai.teacher.dataapi.agent.request;

import lombok.Data;

import java.util.Map;

@Data
public class ChatHeader {

    /**
     * 追踪ID
     * 用户侧生成ID，用于关联上下游，不保证全局唯一
     * 必填
     */
    private String traceId;
    /**
     * 用户ID
     * 必填
     */
    private String userId;
    /**
     * 设备ID
     * 必填
     */
    private String deviceId;
    /**
     * appID
     * 必填
     */
    private String appId;
    /**
     * 请求平台
     * iOS，Android，Windows
     * 必填
     */
    private String platForm;
    /**
     * 额外的header信息
     * 部分场景用来额外标注用户信息的预留字段
     * 非必填
     */
    private Map extra;
    /**
     * 用户的标记
     * 由业务侧定义，可以用于分流和计费
     * 必填
     */
    private String userTag;
    /**
     * 请求token
     * token前固定添加Bearer
     * 必填
     */
    private String authorization;
    /**
     * 用户ip
     * 非必填
     */
    private String forwardedFor;

}
