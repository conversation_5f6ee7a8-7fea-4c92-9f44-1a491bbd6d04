package com.iflytek.ebgai.ai.teacher.dataapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.UserPortraitEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.UserPortraitRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.UserPortraitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserPortraitServiceImpl implements UserPortraitService {

    @Resource
    private UserPortraitRepository userPortraitRepository;

    @Override
    public List<UserPortraitData> query(UserPortraitRequest userPortraitRequest) {
        log.info("UserPortraitService.query请求参数：traceId= {};userPortraitRequest= {}", userPortraitRequest.getTraceId(), userPortraitRequest.getUserId());
        List<UserPortraitData> userPortraitDataList = userPortraitRepository.query(userPortraitRequest);
        log.info("UserPortraitService.query返回值：traceId= {};userPortraitDataList= {}", userPortraitRequest.getTraceId(), CollectionUtils.isEmpty(userPortraitDataList) ? 0 : userPortraitDataList.size());
        return userPortraitDataList;
    }

    @Override
    public void insert(UserPortraitData userPortrait) {
        log.info("UserPortraitService.insert请求参数：traceId= {};userPortrait= {}", userPortrait.getTraceId(), JSON.toJSONString(userPortrait));

        UserPortraitEntity userPortraitEntity = new UserPortraitEntity();
        BeanUtil.copyProperties(userPortrait, userPortraitEntity);
        userPortraitRepository.insert(userPortraitEntity);
    }

    @Override
    public void update(UserPortraitData userPortrait) {
        userPortraitRepository.update(userPortrait);
    }

    @Override
    public void delete(String userId) {
        userPortraitRepository.delete(userId);
    }
}
