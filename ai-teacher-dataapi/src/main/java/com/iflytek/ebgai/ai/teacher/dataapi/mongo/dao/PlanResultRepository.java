package com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.enums.DeleteFlagEnum;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.PlanResultEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class PlanResultRepository {

    private final String COLLECTION_NAME = "xxj_ai_teacher_plan_result";

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 新增规划结果：根据userid+catalogid取符合条件的最新的锚点结果，锚点可能存在多个，按照目录维度每个用户只存一份
     */
    public void insert(PlanResultEntity entity) {
        entity.setCreateTime(Instant.now());
        entity.setIsDelete(DeleteFlagEnum.EXIST.getFlag());
        mongoTemplate.insert(entity, COLLECTION_NAME);
    }

    /**
     * 更新规划总结表为删除
     */
    public void delete(PlanResultRequest planResultRequest) {
        // 1. 创建查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(planResultRequest.getUserId());
        criteria.and("bizAction").is(planResultRequest.getBizAction());
        criteria.and("isDelete").is(DeleteFlagEnum.EXIST.getFlag());
        if (StringUtils.isNotEmpty(planResultRequest.getStudyCode())) {
            criteria.and("studyCode").is(planResultRequest.getStudyCode());
        }
        if (StringUtils.isNotEmpty(planResultRequest.getCatalogId())) {
            criteria.and("catalogId").is(planResultRequest.getCatalogId());
        }
        if (StringUtils.isNotEmpty(planResultRequest.getNodeId())) {
            criteria.and("nodeId").is(planResultRequest.getNodeId());
        }
        Query query = new Query(criteria);
        // 2. 创建更新操作
        Update update = new Update();
        update.set("isDelete", DeleteFlagEnum.DELETED.getFlag());
        update.set("updateTime", DateTime.now().getTime());

        mongoTemplate.updateMulti(query, update, PlanResultEntity.class, COLLECTION_NAME);
    }

    /**
     * 更新规划总结表
     */
    public void update(PlanResultData planResultData) {
        // 1. 创建查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(planResultData.getUserId());
        criteria.and("bizAction").is(planResultData.getBizAction());
        criteria.and("isDelete").is(DeleteFlagEnum.EXIST.getFlag());
        if (StringUtils.isNotEmpty(planResultData.getStudyCode())) {
            criteria.and("studyCode").is(planResultData.getStudyCode());
        }
        if (StringUtils.isNotEmpty(planResultData.getCatalogId())) {
            criteria.and("catalogId").is(planResultData.getCatalogId());
        }
        if (StringUtils.isNotEmpty(planResultData.getNodeId())) {
            criteria.and("nodeId").is(planResultData.getNodeId());
        }
        Query query = new Query(criteria);
        // 2. 创建更新操作
        Update update = new Update();
        update.set("updateTime", DateTime.now().getTime());
        if (ObjectUtil.isNotEmpty(planResultData.getOrder())) {
            update.set("order", planResultData.getOrder());
        }
        if (StringUtils.isNotEmpty(planResultData.getNodeType())) {
            update.set("nodeType", planResultData.getNodeType());
        }
        if (StringUtils.isNotEmpty(planResultData.getNodeAttribute())) {
            update.set("nodeAttribute", planResultData.getNodeAttribute());
        }
        if (StringUtils.isNotEmpty(planResultData.getLearndBehavior())) {
            update.set("learndBehavior", planResultData.getLearndBehavior());
        }
        if (ObjectUtil.isNotEmpty(planResultData.getLearndTimes())) {
            update.set("learndTimes", planResultData.getLearndTimes());
        }
        mongoTemplate.updateFirst(query, update, PlanResultEntity.class, COLLECTION_NAME);
    }

    /**
     * 查询规划结果
     *
     * @param planResultRequest
     * @return
     */
    public List<PlanResultData> query(PlanResultRequest planResultRequest) {
        // 创建一个新的查询条件
        Criteria criteria = new Criteria();
        criteria.and("userId").is(planResultRequest.getUserId());
        criteria.and("isDelete").is(DeleteFlagEnum.EXIST.getFlag());

        if (StringUtils.isNotEmpty(planResultRequest.getBizAction())) {
            criteria.and("bizAction").is(planResultRequest.getBizAction());
        }
        if (StringUtils.isNotEmpty(planResultRequest.getStudyCode())) {
            criteria.and("studyCode").is(planResultRequest.getStudyCode());
        }
        if (StringUtils.isNotEmpty(planResultRequest.getCatalogId())) {
            criteria.and("catalogId").is(planResultRequest.getCatalogId());
        }
        if (StringUtils.isNotEmpty(planResultRequest.getNodeId())) {
            criteria.and("nodeId").is(planResultRequest.getNodeId());
        }
        Query query = new Query();
        query.addCriteria(criteria);
        // 执行聚合查询
        List<PlanResultEntity> results = mongoTemplate.find(query, PlanResultEntity.class, COLLECTION_NAME);

        List<PlanResultData> resultData = BeanUtil.copyToList(results, PlanResultData.class);

        return resultData;
    }
}
