package com.iflytek.ebgai.ai.teacher.dataapi;


import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.Option;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireData;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.QuestionnaireEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.QuestionnaireRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.QuestionnaireService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class QuestionnaireServiceTest {
    @Resource
    private QuestionnaireRepository questionnaireRepository;

    @Autowired
    private QuestionnaireService questionnaireService;


    @Test
    public void insert() {
        QuestionnaireEntity questionnaireEntity = new QuestionnaireEntity();

        questionnaireEntity.setDimension("USER_RANK");
        questionnaireEntity.setIndex(1);
        questionnaireEntity.setChoiceNum(1);
        List<Option> options = new ArrayList<>();
        Option option = new Option();
        option.setContent("你好");
        option.setOrder(1);
        options.add(option);
        questionnaireEntity.setOptions(options);
        questionnaireRepository.insert(questionnaireEntity);
    }


    @Test
    public void query() {

        List<QuestionnaireData> questionnaireData = questionnaireService.query("222222", 1);
        System.out.println(JSON.toJSONString(questionnaireData));

    }
}
