package com.iflytek.ebgai.ai.teacher.dataapi;


import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.Option;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireAnswerData;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.QuestionnaireAnswerEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.QuestionnaireAnswerRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.QuestionnaireAnswerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class QuestionnaireAnswerServiceTest {

    @Resource
    private QuestionnaireAnswerRepository questionnaireAnswerRepository;

    @Autowired
    private QuestionnaireAnswerService questionnaireAnswerService;


    @Test
    public void insert() {
        QuestionnaireAnswerEntity questionnaireAnswerEntity = new QuestionnaireAnswerEntity();

        questionnaireAnswerEntity.setUserId("123456");
        questionnaireAnswerEntity.setBizAction("AI_TUTORING_TEACHER_QUSNAIRE");
        questionnaireAnswerEntity.setDimension("USER_RANK");
        questionnaireAnswerEntity.setIndex(1);
        questionnaireAnswerEntity.setRoundId("1");
        List<Option> options = new ArrayList<>();
        Option option = new Option();
        option.setContent("你好");
        option.setOrder(1);
        options.add(option);
        questionnaireAnswerEntity.setOptions(options);
        questionnaireAnswerRepository.insert(questionnaireAnswerEntity);
    }


    @Test
    public void query() {

        List<QuestionnaireAnswerData> questionnaireAnswerData = questionnaireAnswerService.query("2222", "22222", "1");
        System.out.println(JSON.toJSONString(questionnaireAnswerData));

    }
}
