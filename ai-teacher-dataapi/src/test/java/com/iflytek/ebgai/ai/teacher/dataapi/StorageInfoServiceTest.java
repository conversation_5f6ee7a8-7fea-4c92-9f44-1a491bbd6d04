package com.iflytek.ebgai.ai.teacher.dataapi;


import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.StorageInfoEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.StorageInfoRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.StorageInfoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class StorageInfoServiceTest {

    @Resource
    private StorageInfoRepository storageInfoRepository;

    @Autowired
    private StorageInfoService storageInfoService;


    @Test
    public void insert() {
        StorageInfoEntity storageInfoEntity = new StorageInfoEntity();
        storageInfoEntity.setUserId("123456");
        storageInfoEntity.setRoundId("1");
        storageInfoEntity.setBizAction("AI_TUTORING_LEARNING_PLAN");
        storageInfoEntity.setFunctionCode("LEARNINGPLAN_CHANGE");
        storageInfoEntity.setQuery("你好");
        storageInfoRepository.insert(storageInfoEntity);
    }


    @Test
    public void insertQuest() {
        StorageInfoEntity storageInfoEntity = new StorageInfoEntity();
        storageInfoEntity.setUserId("123456");
        storageInfoEntity.setRoundId("1");
        storageInfoEntity.setBizAction("AI_TUTORING_LEARNING_PLAN");
        storageInfoEntity.setFunctionCode("LEARNINGPLAN_CHANGE");
        storageInfoEntity.setQuery("你好");
        storageInfoRepository.insert(storageInfoEntity);
    }


    @Test
    public void query() {
        StorageInfoRequest storageInfoRequest = new StorageInfoRequest();
        storageInfoRequest.setUserId("123456");
        storageInfoRequest.setFunctionCode("LEARNINGPLAN_CHANGE");

        List<StorageInfoData> storageInfoData = storageInfoService.query(storageInfoRequest);
        System.out.println(JSON.toJSONString(storageInfoData));

    }
}
