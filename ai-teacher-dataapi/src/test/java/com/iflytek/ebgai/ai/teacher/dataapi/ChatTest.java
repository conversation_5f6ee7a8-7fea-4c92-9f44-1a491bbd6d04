package com.iflytek.ebgai.ai.teacher.dataapi;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.chat.AiAbilityService;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.chat.AiAbilityServiceImpl;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.AuthBase;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.AuthRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.ChatHeader;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.ChatRequestParam;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.request.chat.ShortcutData;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.response.AuthResponse;
import com.iflytek.ebgai.ai.teacher.dataapi.agent.response.ChatResponse;
import com.iflytek.ebgai.ai.teacher.dataapi.util.Md5Util;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.iflytek.ebgai.ai.teacher.dataapi.agent.constant.Constant.CHARSET_UTF8;
import static com.iflytek.ebgai.ai.teacher.dataapi.agent.constant.Constant.HTTP_SUCCESS_CODE;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class ChatTest {

    @Test
    public void chat() throws Exception {
        //diyibu huoqu token
        //{  "retCode":"000000",  "accessToken":"983f2a4d-7ed8-42aa-a419-ac14875f359c",  "expiresIn":"60000" } 55000
//        AuthResponse response = getToken();
//        if (!"000000".equals(response.getRetCode())) {
//            return;
//
//        }
//        String token = response.getAccessToken();
//        System.out.println("token" + JSONObject.toJSONString(response));

        String uuid = UUID.randomUUID().toString();

        ChatRequestParam param = new ChatRequestParam();
        param.setAgentType(1);
        param.setVersionId("");
        ShortcutData shortcutData = new ShortcutData();
        shortcutData.setShortcutType(0);
        shortcutData.setWorkflowId("682554b25cf2002c9fb76e1a");
        shortcutData.setWorkflowVersion("");
        param.setShortcutData(shortcutData);
        param.setContentType(1);
        param.setContent("txt-string");
        param.setDebugMode(true);
        Map<String, String> map = new HashMap<>();
        map.put("content", "用五句话回复我武汉有什么特点");
        param.setCustomVariables(map);
        param.setChatId(uuid);

        ChatHeader header = new ChatHeader();

        header.setTraceId(uuid);
        header.setUserId("12345678");
        header.setDeviceId(uuid);
        header.setPlatForm("Android");
        header.setUserTag("tag123");
        header.setAuthorization("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhaWQiOiJ4eGpjcHgteGNqengiLCJleHAiOiIxNzUwMjEyNzM3In0.kUjcSA4JVC9-QdC_BXRVTfB6attlOjvC2J4eQcCdbR");
        AiAbilityService aiAbilityService = AITeacherDataHub.getAiAbilityService();
        aiAbilityService.getChatResult(param, header, new AiAbilityServiceImpl.SseListener() {
            @Override
            public void onEvent(ChatResponse chatResponse) {
                System.out.println("111" + JSONObject.toJSONString(chatResponse));

            }
        });
    }


    public static AuthResponse getToken() {
        String timeStamp = String.valueOf(System.currentTimeMillis());
        //请求体
        AuthBase base = new AuthBase();
        base.setAppId("xxjcpx-xcjzx");
        base.setTimestamp(timeStamp);
        AuthRequest authRequest = new AuthRequest();
        authRequest.setBase(base);

        try {
            try (CloseableHttpClient httpClient = createHttpClient(5000, 5000)) {
                // 创建POST请求
                HttpPost httpPost = new HttpPost("https://pre-assistant.eduaiplat.com/auth/v3/token");

                //获得Authorization
                String sk = Md5Util.getMD5("G2xhHrSo064d0Bax" + timeStamp);
                String requestBodyStr = JSON.toJSONString(authRequest);
                String sign = Md5Util.getMD5(sk + requestBodyStr);
                //请求头
                httpPost.setHeader("Authorization", sign);
                httpPost.setHeader("content-type", "application/json");

                // 设置请求实体
                httpPost.setEntity(new StringEntity(requestBodyStr, CHARSET_UTF8));
                try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                    int statusCode = response.getStatusLine().getStatusCode();
                    if (HTTP_SUCCESS_CODE == statusCode) {
                        HttpEntity entity = response.getEntity();
                        if (null != entity) {
                            String res = EntityUtils.toString(entity, CHARSET_UTF8);
                            return JSONObject.parseObject(res, AuthResponse.class);
                        }
                    }
                }
            }
        } catch (IOException e) {
            System.err.println("IO Error: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    private static CloseableHttpClient createHttpClient(int connectTimeout, int socketTimeout) {
        RequestConfig config = RequestConfig.custom().setConnectTimeout(connectTimeout)  // 连接超时时间（毫秒）
                .setSocketTimeout(socketTimeout)    // 数据传输超时时间
                .build();

        return HttpClients.custom().setDefaultRequestConfig(config).build();
    }

}
