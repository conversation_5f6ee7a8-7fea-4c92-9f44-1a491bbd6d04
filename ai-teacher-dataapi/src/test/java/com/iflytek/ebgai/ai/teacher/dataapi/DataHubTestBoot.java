package com.iflytek.ebgai.ai.teacher.dataapi;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication(exclude = RedisAutoConfiguration.class)
@ComponentScan("com.iflytek.ebgai.ai.teacher.dataapi")
public class DataHubTestBoot {

    public static void main(String[] args) {
        SpringApplication.run(DataHubTestBoot.class, args);
    }
}
