package com.iflytek.ebgai.ai.teacher.dataapi;


import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.CoreLiteracy;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.service.UserPortraitService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class UserPortraitServiceTest {

    @Resource
    private UserPortraitService userPortraitService;

    @Test
    public void testInsert() {
        UserPortraitData userPortrait = new UserPortraitData();
        userPortrait.setUserId("123456");
        userPortrait.setUserLevel("测试1");
        userPortrait.setUserName("用户名");
        userPortrait.setUserRank("1");
        userPortrait.setWorkState("作业感受");
        userPortrait.setStudyTarget("学习目标");
        CoreLiteracy coreLiteracy = new CoreLiteracy();
        coreLiteracy.setAbstracty(1f);
        coreLiteracy.setComputing(2f);
        userPortrait.setCoreLiteracy(coreLiteracy);


        userPortraitService.insert(userPortrait);
    }

    @Test
    public void testQuery() {
        UserPortraitRequest userPortrait = new UserPortraitRequest();
        userPortrait.setUserId("123456");
        List<UserPortraitData> data = AITeacherDataHub.getUserPortraitService().query(userPortrait);

//        List<UserPortraitData> data = userPortraitService.query(userPortrait);
        System.out.println(JSON.toJSONString(data));
    }
}
