package com.iflytek.ebgai.ai.teacher.dataapi;


import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.PlanResultEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.PlanResultRepository;
import com.iflytek.ebgai.ai.teacher.dataapi.service.PlanResultService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class PlanResultServiceTest {

    @Resource
    private PlanResultRepository planResultRepository;

    @Autowired
    private PlanResultService planResultService;


    @Test
    public void insert() {
        PlanResultEntity planResult = new PlanResultEntity();

        planResult.setUserId("123456789");
        planResult.setBizAction("AI_TUTORING_LEARNING_PLAN");
        planResult.setCatalogId("12345621212212e21");
        planResult.setLearndBehavior("LEARN");
        planResult.setNodeId("1234562333e12312");
        planResult.setNodeAttribute("CENTRAL_POINT");
        planResultRepository.insert(planResult);
    }

    @Test
    public void delete() {
        PlanResultRequest planResultRequest = new PlanResultRequest();
        planResultRequest.setUserId("123456789");
        planResultRequest.setBizAction("AI_TUTORING_LEARNING_PLAN");
        planResultService.delete(planResultRequest);

    }

    @Test
    public void update() {
        PlanResultData planResultData = new PlanResultData();
        planResultData.setUserId("123456789");
        planResultData.setBizAction("AI_TUTORING_LEARNING_PLAN");
        planResultData.setNodeId("1111");

        planResultService.update(planResultData);

    }

    @Test
    public void query() {
        PlanResultRequest planResultRequest = new PlanResultRequest();
        planResultRequest.setUserId("123456");
        planResultRequest.setBizAction("AI_TUTORING_LEARNING_PLAN");

        List<PlanResultData> planResultData = planResultService.query(planResultRequest);
        System.out.println(JSON.toJSONString(planResultData));

    }


}
