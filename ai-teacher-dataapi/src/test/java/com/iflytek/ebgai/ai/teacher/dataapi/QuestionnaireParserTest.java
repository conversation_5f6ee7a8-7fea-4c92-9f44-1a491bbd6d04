package com.iflytek.ebgai.ai.teacher.dataapi;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.Option;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.QuestionnaireEntity;
import com.iflytek.ebgai.ai.teacher.dataapi.mongo.dao.QuestionnaireRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class QuestionnaireParserTest {
    @Resource
    private QuestionnaireRepository questionnaireRepository;

    public static void main(String[] args) {
        String filePath = "D://work/tjpt/资料/AI伴学/学习目标.txt"; // 替换为你的文件路径
        List<QuestionnaireEntity> entities = parseQuestionnaireFile(filePath);

        // 打印解析结果
        for (QuestionnaireEntity entity : entities) {
            System.out.println(entity);
        }
    }

    @Test
    public void insert() {

        String filePath = "D://work/tjpt/资料/AI伴学/学习目标.txt"; // 替换为你的文件路径
        List<QuestionnaireEntity> entities = parseQuestionnaireFile(filePath);

        // 打印解析结果
        for (QuestionnaireEntity entity : entities) {
//            questionnaireRepository.insert(entity);
        }

    }

    public static List<QuestionnaireEntity> parseQuestionnaireFile(String filePath) {
        List<QuestionnaireEntity> entities = new ArrayList<>();

        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            QuestionnaireEntity currentEntity = null;
            StringBuilder variantContent = new StringBuilder();
            boolean inVariantBlock = false;

            while ((line = br.readLine()) != null) {
                line = line.trim();

                // 检测变体开始
                if (line.startsWith("### ")) {
                    if (currentEntity != null) {
                        parseVariantContent(currentEntity, variantContent.toString());
                        entities.add(currentEntity);
                        variantContent.setLength(0);
                    }

                    // 创建新实体
                    currentEntity = new QuestionnaireEntity();
                    currentEntity.setDimension("STUDY_TARGET"); // 设置维度为成绩排名
                    currentEntity.setRadio(true); // 默认为单选
                    currentEntity.setChoiceNum(1); // 默认选择1个答案
                    currentEntity.setCreateTime(Instant.now()); // 设置创建时间

                    // 提取变体编号
//                    Pattern pattern = Pattern.compile("(\\d+)");
//                    Matcher matcher = pattern.matcher(line);
//                    if (matcher.find()) {
//                        currentEntity.setIndex(Integer.parseInt(matcher.group(1)));
//                    }
                    currentEntity.setIndex(1);

                    inVariantBlock = true;
                    continue;
                }

                // 收集变体内容
                if (inVariantBlock && !line.isEmpty()) {
                    variantContent.append(line).append("\n");
                }
            }

            // 处理最后一个变体
            if (currentEntity != null && variantContent.length() > 0) {
                parseVariantContent(currentEntity, variantContent.toString());
                entities.add(currentEntity);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return entities;
    }

    private static void parseVariantContent(QuestionnaireEntity entity, String content) {
        // 分割内容行
        String[] lines = content.split("\n");

        // 解析问题内容
        for (String line : lines) {
            if (line.startsWith("你") || line.startsWith("说") || line.startsWith("谈") || line.startsWith("从") || line.startsWith("每") || line.startsWith("在") || line.startsWith("关") || line.startsWith("觉") || line.startsWith("提") || line.startsWith("放") || line.startsWith("回") || line.startsWith("当") || line.startsWith("总") || line.startsWith("反")) {
                // 去除选项标记
                String question = line.replaceAll("[A-Z]、", "").trim();
                entity.setQuestionContent(question);
                break;
            }
        }

        // 解析选项
        List<Option> options = new ArrayList<>();
        Pattern optionPattern = Pattern.compile("([A-Z])、(.+)");

        for (String line : lines) {
            Matcher matcher = optionPattern.matcher(line);
            if (matcher.find()) {
                Option option = new Option();
                // 将A/B/C转换为1/2/3
                option.setOrder(convertOptionOrder(matcher.group(1)));
                option.setContent(line);

                List<Integer> rejects = new ArrayList<>();
                if (entity.getDimension().equals("CORE_LITERACY")) {
                    if (option.getOrder() == 5) {
                        rejects.addAll(Arrays.asList(1, 2, 3, 4));
                    }
                }
                option.setRejects(rejects);
                options.add(option);
            }
        }

        entity.setOptions(options);
    }

    private static int convertOptionOrder(String letter) {
        switch (letter) {
            case "A":
                return 1;
            case "B":
                return 2;
            case "C":
                return 3;
            case "D":
                return 4;
            case "E":
                return 5;
            default:
                return 0;
        }
    }
}

