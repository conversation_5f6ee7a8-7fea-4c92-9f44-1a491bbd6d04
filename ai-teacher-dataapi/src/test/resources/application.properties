spring.application.name=ai-teacher
server.port=32399
# log config
#---------------------------------------------------------------
recommend.auth.url=https://testllm-aiplat.ceshiservice.cn/auth/v3/token_check
recommend.auth.token.url=https://testllm-aiplat.ceshiservice.cn/auth/v3/token
recommend.auth.enable=true
spring.data.mongodb.uri=*******************************************************************************************************************************************
#-------------------------------------------------------------
skyline.spring.cloud.zookeeper.discovery.enabled=false
skyline.spring.cloud.zookeeper.discovery.register=false
skyline.spring.cloud.zookeeper.enabled=false
IP=***********
spring.cloud.zookeeper.connect-string=${IP}:2181
spring.cloud.zookeeper.discovery.root=/skynet/discovery/skylab
#chat.app.id=xxjcpx-xcjzx
#chat.app.agent.id=682557525cf2002c9fb76e1d
#chat.sse.url=https://ai-thor-prelt.ceshiservice.cn/ai-thor-dispatcher/api/v1/workflow/chat
#chat.connect.timeout=5000
#chat.socket.timeout=30000
#-----------------ES-------------------
zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.es-host=***********:9200,***********:9200,***********:9200
zion.es-user-name=elastic
#zion.es-password=bx90ZOw1IZbx8fWCIo64
zion.query-data-base=es
zion.dict-refresh-period-seconds=10
zion.cache-ttl=PT3M




