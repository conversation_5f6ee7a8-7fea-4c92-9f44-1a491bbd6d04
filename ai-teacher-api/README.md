# AI Teacher API Module

## 项目概述

### 项目名称
AI Teacher API Module - AI伴学服务接口定义模块

### 项目描述
AI Teacher API模块是AI伴学服务的接口定义层，提供了系统对外暴露的所有Dubbo服务接口、数据传输对象(DTO)和业务模型定义。该模块采用接口与实现分离的设计原则，为分布式服务调用提供标准化的接口契约。

### 版本信息
- **当前版本**: 1.0.2-SNAPSHOT
- **Java版本**: JDK 8
- **编码格式**: UTF-8

### 许可证信息
Apache License, Version 2.0

## 技术架构

### 整体技术架构描述
- **架构模式**: 接口与实现分离、面向服务架构(SOA)
- **设计模式**: 
  - 门面模式(Facade)：统一服务接口入口
  - 数据传输对象模式(DTO)：标准化数据传输格式
  - 契约优先设计：接口定义驱动开发
- **核心技术栈**:
  - Apache Dubbo：分布式服务框架
  - Java Serialization：对象序列化
  - Lombok：代码简化
  - Maven：依赖管理

### 模块职责说明
- **接口定义**: 定义Dubbo服务接口契约
- **模型定义**: 提供请求响应数据模型
- **数据传输**: 标准化服务间数据交换格式
- **版本管理**: 接口版本控制和兼容性管理

### 架构图

```mermaid
graph TB
    A[AI Teacher API Module] --> B[Service Layer]
    A --> C[Model Layer]
    
    B --> B1[DemoService]
    B --> B2[QuestionnaireInferApiService]
    B --> B3[LearningPlanApiService]
    
    C --> C1[Question Models]
    C --> C2[Plan Models]
    C --> C3[Demo Models]
    
    C1 --> C11[QueryQuestionnaireInferRequest]
    C1 --> C12[QueryQuestionnaireInferResponse]
    C1 --> C13[StorageInfo]
    C1 --> C14[SummaryInfo]
    
    C2 --> C21[QueryLearningPlanRequest]
    C2 --> C22[QueryLearningPlanResponse]
    C2 --> C23[DeleteLearningPlanRequest]
    C2 --> C24[PlanResultDataAO]
    
    C3 --> C31[DemoModel]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style B1 fill:#e8f5e8
    style B2 fill:#e8f5e8
    style B3 fill:#e8f5e8
```

## 主要功能

### 功能模块列表

#### 1. 问卷推理服务接口 (QuestionnaireInferApiService)
- **接口功能**: 查询用户问卷结果和解释
- **核心方法**: `query(QueryQuestionnaireInferRequest)`
- **业务场景**: 学习机后端查询用户问卷分析结果
- **数据模型**: 
  - 请求模型：QueryQuestionnaireInferRequest
  - 响应模型：QueryQuestionnaireInferResponse

#### 2. 学习规划服务接口 (LearningPlanApiService)
- **接口功能**: 学习规划查询和管理
- **核心方法**: 
  - `queryPlan(QueryLearningPlanRequest)`: 查询用户学习规划
  - `deletePlan(DeleteLearningPlanRequest)`: 放弃学习规划
- **业务场景**: 个性化学习路径规划和调整
- **数据模型**:
  - 查询请求：QueryLearningPlanRequest
  - 查询响应：QueryLearningPlanResponse
  - 删除请求：DeleteLearningPlanRequest

#### 3. 演示服务接口 (DemoService)
- **接口功能**: 系统演示和测试
- **核心方法**: 
  - `getDemoList()`: 获取演示数据列表
  - `waitTime()`: 等待时间测试
- **业务场景**: 系统功能演示和接口测试
- **数据模型**: DemoModel

### 关键技术点
- **Dubbo服务暴露**: 通过XML配置暴露服务接口
- **序列化支持**: 所有模型实现Serializable接口
- **泛型支持**: 使用CommonResponse<T>统一响应格式
- **数据验证**: 集成参数校验和业务规则验证
- **链路追踪**: 所有请求包含traceId用于分布式追踪

## 业务处理逻辑分析

### 核心业务逻辑

#### 1. 问卷推理查询流程
```java
// 问卷推理接口核心逻辑
CommonResponse<QueryQuestionnaireInferResponse> query(QueryQuestionnaireInferRequest request)
```
- **数据流转**: 客户端请求 → 参数校验 → 数据查询 → 结果封装 → 响应返回
- **条件判断**: 根据userId和functionCode查询最新问卷解释结果
- **异常处理**: 数据为空时返回空结果，异常时返回错误码

#### 2. 学习规划查询流程
```java
// 学习规划查询核心逻辑
CommonResponse<QueryLearningPlanResponse> queryPlan(QueryLearningPlanRequest request)
```
- **数据流转**: 请求参数 → 主键构建(userId+catalogId+functionCode) → 数据库查询 → 结果映射
- **条件判断**: 根据学习场景和目录ID筛选规划结果
- **循环结构**: 遍历多个规划结果进行数据转换

#### 3. 规划删除流程
```java
// 规划删除核心逻辑
CommonResponse<Serializable> deletePlan(DeleteLearningPlanRequest request)
```
- **数据流转**: 删除请求 → 参数验证 → 逻辑删除 → 状态更新
- **异常处理**: 删除失败时回滚操作并返回错误信息

### 关键算法说明

#### 1. 主键构建算法
- **业务场景**: 唯一标识用户的学习规划记录
- **算法实现**: userId + catalogId + functionCode组合主键
- **优化点**: 确保数据唯一性和查询效率

#### 2. 数据模型映射算法
- **业务场景**: 内部数据模型与外部接口模型转换
- **算法实现**: 使用Mapping类进行对象属性映射
- **优化点**: 减少手动映射代码，提高开发效率

## 主要对外接口

### 接口类型说明
- **Dubbo RPC接口**: 基于Dubbo协议的远程过程调用
- **接口注册方式**: EPAS注册中心 + Zookeeper注册中心
- **序列化协议**: Java原生序列化
- **超时设置**: 30秒

### 接口详细信息

| 接口名称 | 方法 | 请求参数 | 返回值 | 功能描述 | 使用场景 |
|---------|------|---------|--------|---------|---------|
| QuestionnaireInferApiService | query | QueryQuestionnaireInferRequest | CommonResponse<QueryQuestionnaireInferResponse> | 查询用户问卷结果 | 学习机获取问卷分析 |
| LearningPlanApiService | queryPlan | QueryLearningPlanRequest | CommonResponse<QueryLearningPlanResponse> | 查询学习规划 | 获取个性化学习计划 |
| LearningPlanApiService | deletePlan | DeleteLearningPlanRequest | CommonResponse<Serializable> | 放弃学习规划 | 用户取消学习计划 |
| DemoService | getDemoList | 无 | List<DemoModel> | 获取演示列表 | 系统功能演示 |
| DemoService | waitTime | 无 | Boolean | 等待时间测试 | 接口性能测试 |

### 接口配置示例

#### Dubbo服务配置
```xml
<!-- 问卷推理服务 -->
<dubbo:service registry="epas" ref="questionnaireInferApiService"
               interface="com.iflytek.ebgai.ai.teacher.dubbo.service.QuestionnaireInferApiService"/>

<!-- 学习规划服务 -->
<dubbo:service registry="epas" ref="learningPlanApiService"
               interface="com.iflytek.ebgai.ai.teacher.dubbo.service.LearningPlanApiService"/>
```

#### 请求示例
```java
// 问卷查询请求
QueryQuestionnaireInferRequest request = new QueryQuestionnaireInferRequest();
request.setTraceId("trace-123");
request.setUserId("user-456");

// 学习规划查询请求
QueryLearningPlanRequest planRequest = new QueryLearningPlanRequest();
planRequest.setTraceId("trace-123");
planRequest.setUserId("user-456");
planRequest.setCatalogId("catalog-789");
planRequest.setStudyCode("SYNC_TUTORING");
```

## 系统配置

### 运行环境要求
- **JDK版本**: JDK 8+
- **操作系统**: 支持Windows、Linux、macOS
- **网络要求**: 支持Dubbo协议通信
- **注册中心**: EPAS + Zookeeper

### 配置文件说明
该模块为纯接口定义模块，无独立配置文件，配置由实现模块管理。

#### 重要参数说明
- **dubbo.service-port**: Dubbo服务端口
- **application.dubbo.zookeeper.address**: Zookeeper注册中心地址
- **application.epas.***: EPAS注册中心配置

### 启动和部署
```xml
<!-- Maven依赖集成 -->
<dependency>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-api</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

## 快速开始

### 环境准备
1. 确保JDK 8+环境
2. Maven 3.6+构建工具
3. Dubbo服务注册中心

### 项目集成
1. **添加Maven依赖**
```xml
<dependency>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-api</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

2. **Dubbo消费者配置**
```xml
<!-- 引用问卷服务 -->
<dubbo:reference id="questionnaireInferApiService" 
                 interface="com.iflytek.ebgai.ai.teacher.dubbo.service.QuestionnaireInferApiService"/>

<!-- 引用规划服务 -->
<dubbo:reference id="learningPlanApiService"
                 interface="com.iflytek.ebgai.ai.teacher.dubbo.service.LearningPlanApiService"/>
```

3. **服务调用示例**
```java
@Autowired
private QuestionnaireInferApiService questionnaireService;

@Autowired
private LearningPlanApiService planService;

public void queryUserQuestionnaire(String userId) {
    QueryQuestionnaireInferRequest request = new QueryQuestionnaireInferRequest();
    request.setUserId(userId);
    request.setTraceId(UUID.randomUUID().toString());
    
    CommonResponse<QueryQuestionnaireInferResponse> response = 
        questionnaireService.query(request);
    
    if ("000000".equals(response.getCode())) {
        // 处理成功结果
        QueryQuestionnaireInferResponse data = response.getData();
    }
}
```

### 验证测试
1. 启动Dubbo提供者服务
2. 配置消费者并调用接口
3. 检查返回结果和链路追踪

## 开发指南

### 代码结构
```
ai-teacher-api/
├── src/main/java/
│   └── com/iflytek/ebgai/ai/teacher/dubbo/
│       ├── model/                  # 数据模型
│       │   ├── paln/              # 规划相关模型
│       │   └── question/          # 问卷相关模型
│       └── service/               # 服务接口
└── pom.xml                        # Maven配置
```

### 开发规范
- **接口设计**: 遵循RESTful风格和Dubbo最佳实践
- **模型定义**: 所有模型必须实现Serializable接口
- **参数校验**: 使用JSR-303注解进行参数验证
- **版本控制**: 接口变更需要考虑向后兼容性
- **文档维护**: 及时更新接口文档和注释

### 测试指南
```bash
# 编译打包
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests
```

### 接口版本管理
- **向后兼容**: 新增字段使用默认值
- **废弃标记**: 使用@Deprecated标记过时接口
- **版本号**: 遵循语义化版本控制
