package com.iflytek.ebgai.ai.teacher.dubbo.model.paln;

import lombok.Data;

import java.io.Serializable;

/**
 * 放弃规划 请求
 *
 * @Author:huizhang68
 * @Date:2025/5/23
 */
@Data

public class DeleteLearningPlanRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    private String traceId;
    /**
     * 用户ID（用户中台 或 业务自定义）
     */
    private String userId;
    /**
     * 锚点所在的目录
     * 规划范围目录列表，如: 章、节、小节、小小节、课时
     * 以目录维度存储规划结果
     * 规划总结 会用到，问卷总结无该字段
     */
    private String catalogId;

    /**
     * 学习场景（服务层定义）,默认=SYNC_TUTORING
     */
    private String studyCode;

}
