package com.iflytek.ebgai.ai.teacher.dubbo.model.question;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 格式化 问卷总结、规划总结结果 非流式输出；问卷总结+规划总结格式化存储数据结构（学习机业务方使用）
 *
 * @Author:huizhang68
 * @Date:2025/5/23
 */
@Data
public class SummaryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总结详情
     */
    private List<Summary> summaries;
    /**
     * 总结类型 ：问卷总结 or 规划总结 ，枚举：
     * AI_TUTORING_TEACHER_QUSNAIRE("问卷解释","AI_TUTORING_TEACHER_QUSNAIRE"),
     * AI_TUTORING_LEARNING_PLAN("学习规划","AI_TUTORING_LEARNING_PLAN"),
     * OTHERS("其他LLM能力","OTHERS");
     */
    private String summaryType;
}
