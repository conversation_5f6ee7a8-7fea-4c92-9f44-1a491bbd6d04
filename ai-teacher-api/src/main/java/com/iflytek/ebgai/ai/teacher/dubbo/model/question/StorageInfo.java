package com.iflytek.ebgai.ai.teacher.dubbo.model.question;

import lombok.Data;

import java.io.Serializable;

/**
 * 存储信息
 *
 * @Author:huizhang68
 * @Date:2025/5/23
 */
@Data
public class StorageInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户层级 or 用户类型（只在问卷总结输出并存储），枚举值：conventional、highScoreAdvanced、thinkingExpansion
     * CONVENTIONAL("基础用户","conventional"),
     * HIGHSCOREADVANCED("进阶用户","highScoreAdvanced"),
     * THINKINGEXPANSION("拔高用户","thinkingExpansion");
     */
    private String userLevel;
    /**
     * 跟踪ID 用户一次学习流程内唯一
     */
    private String traceId;
    /**
     * 锚点所在的目录
     * 规划范围目录列表，如: 章、节、小节、小小节、课时
     * 以目录维度存储规划结果
     * 规划总结 会用到，问卷总结无该字段
     */
    private String catalogId;
    /**
     * 本轮id 一轮问卷流程内唯一，可以和会话id共用
     */
    private String roundId;
    /**
     * 用户输入
     */
    private String query;
    /**
     * 原始大模型输出 去除think
     */
    private String oriContent;
    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 格式化 问卷总结、规划总结结果 非流式输出；问卷总结+规划总结格式化存储数据结构（学习机业务方使用）
     */
    private SummaryInfo summaryInfo;
}
