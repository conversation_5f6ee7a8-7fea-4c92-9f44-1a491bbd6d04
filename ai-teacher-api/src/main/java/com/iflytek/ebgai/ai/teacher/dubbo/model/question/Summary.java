package com.iflytek.ebgai.ai.teacher.dubbo.model.question;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 总结详情
 *
 * @Author:huizhang68
 * @Date:2025/5/23
 */
@Data
public class Summary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总结标题 比如：基础补漏型、重点突破考点、时间安排 等
     */
    private String headline;

    /**
     * 总结内容展示位
     */
    private Integer order;

    /**
     * 总结内容 ，按照顺序排列
     */
    private List<String> contents;
}
