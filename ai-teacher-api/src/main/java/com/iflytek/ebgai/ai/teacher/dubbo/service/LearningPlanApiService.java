package com.iflytek.ebgai.ai.teacher.dubbo.service;


import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.DeleteLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanResponse;

import java.io.Serializable;

/**
 * 规划相关接口
 * double
 *
 * @Author:huizhang68
 * @Date:2025/5/23
 */
public interface LearningPlanApiService {

    /**
     * 根据查询用户规划结果
     * 主键:userId+catalogId+functionCode
     *
     * @param request
     * @return
     */
    CommonResponse<QueryLearningPlanResponse> queryPlan(QueryLearningPlanRequest request);

    /**
     * 放弃规划
     *
     * @param request
     * @return
     */
    CommonResponse<Serializable> deletePlan(DeleteLearningPlanRequest request);
}
