package com.iflytek.ebgai.ai.teacher.dubbo.service;


import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferResponse;

/**
 * 【学习机后端】查询用户问卷结果接口开发
 * double
 *
 * @Author:huizhang68
 * @Date:2025/5/23
 */
public interface QuestionnaireInferApiService {

    /**
     * 查询用户问卷结果接口
     * 根据updateTime查到最新一条
     *
     * @param request
     * @return
     */
    CommonResponse<QueryQuestionnaireInferResponse> query(QueryQuestionnaireInferRequest request);

}
