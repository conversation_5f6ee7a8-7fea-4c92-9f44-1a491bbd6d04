package com.iflytek.ebgai.ai.teacher.dubbo.model.paln;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author:huizhang68
 * @Date:2025/5/26
 */
@Data
public class PlanResultDataAO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务方功能（服务层定义）
     */
    private String bizAction;
    /**
     * 学习场景（服务层定义）,默认=SYNC_TUTORING
     */
    private String studyCode = "SYNC_TUTORING";
    /**
     * 用户id
     */
    private String userId;
    /**
     * 更新时间
     */
    private long updateTime;
    /**
     * 规划排序 1 2 3 4优先级
     */
    private int order;
    /**
     * 点ID
     */
    private String nodeId;
    /**
     * 点类型
     */
    private String nodeType;
    /**
     * 点属性 中心点 or 延展点  枚举值:
     * CENTRAL_POINT("中心点","CENTRAL_POINT"),
     * EXTENSION_POINT("延展点","EXTENSION_POINT"),
     * OTHERS("其他点","OTHERS");
     */
    private String nodeAttribute;
    /**
     * 学习行为，枚举值：
     * LEARN("学","LEARN"),
     * PRACTICE("练","PRACTICE"),
     * OTHERS("其他学习行为","OTHERS");
     */
    private String learndBehavior;
    /**
     * 学习时长 单位min
     */
    private int learndTimes;
    /**
     * 锚点所在的目录
     * 规划范围目录列表，如: 章、节、小节、小小节、课时
     * 以目录维度存储规划结果
     */
    private String catalogId;

}
