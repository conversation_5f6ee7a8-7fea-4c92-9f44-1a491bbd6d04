<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.ebg.ai</groupId>
        <artifactId>ai-teacher</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai-teacher-api</artifactId>
    <packaging>jar</packaging>

    <description>接口定义</description>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <skipTests>true</skipTests>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.iflytek.ebg.ai</groupId>
            <artifactId>ai-teacher-common</artifactId>
            <exclusions>
                <!-- api中只存在基础类即可，后续common层增加任何包，都过滤掉-->
                <exclusion>
                    <groupId>com.iflytek.skynet</groupId>
                    <artifactId>skynet-boot-starter-metrics</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>