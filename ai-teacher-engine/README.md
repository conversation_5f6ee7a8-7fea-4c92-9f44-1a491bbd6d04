# AI Teacher Engine Module

## 项目概述

### 项目名称
AI Teacher Engine Module - AI伴学服务引擎核心模块

### 项目描述
AI Teacher Engine模块是AI伴学服务的核心引擎层，提供了AI能力的统一封装和调用接口。该模块集成了问卷分析、用户画像计算、知识簇选择、学习规划等核心AI算法，通过工厂模式和单例模式管理不同类型的AI引擎实例，为上层业务提供高性能的AI服务能力。

### 版本信息
- **当前版本**: 1.0.2-SNAPSHOT
- **Java版本**: JDK 8
- **编码格式**: UTF-8

### 许可证信息
Apache License, Version 2.0

## 技术架构

### 整体技术架构描述
- **架构模式**: 门面模式(Facade)、工厂模式(Factory)、单例模式(Singleton)
- **设计模式**: 
  - 门面模式：AbstractEngineFacade统一AI引擎访问入口
  - 工厂模式：LlmInstanceFactory创建不同类型的AI实例
  - 单例模式：各种SingletonHelper管理引擎实例生命周期
  - 模板方法模式：AbstractEngineFacade定义通用处理流程
- **核心技术栈**:
  - Project Reactor：响应式编程支持
  - Spring Framework：依赖注入和组件管理
  - 科大讯飞AI引擎：底层AI能力支撑
  - Java并发编程：线程安全的单例实现

### 模块职责说明
- **引擎门面层**: 提供统一的AI引擎访问接口
- **工厂管理层**: 负责不同类型AI引擎实例的创建和管理
- **单例管理层**: 确保引擎实例的唯一性和线程安全
- **流式处理层**: 支持AI响应的实时流式处理
- **监控集成层**: 集成性能监控和链路追踪

### 架构图

```mermaid
graph TB
    A[AI Teacher Engine Module] --> B[Facade Layer]
    A --> C[Factory Layer]
    A --> D[Singleton Layer]
    A --> E[Stream Processing]
    
    B --> B1[AbstractEngineFacade]
    B --> B2[EngineFacadeImpl]
    
    C --> C1[LlmInstanceFactory]
    C --> C2[LlmType Enum]
    
    D --> D1[QuestionnaireInferSingleHelper]
    D --> D2[LearningPlanSingletonHelper]
    D --> D3[IclusterChoiceSingletonHelper]
    
    E --> E1[Flux Stream Processing]
    E --> E2[Resource Management]
    
    B2 --> F[AI Engine Implementations]
    F --> F1[QuestionnaireInferImpl]
    F --> F2[LearningPlanImpl]
    F --> F3[ClusterChoiceRuleImpl]
    F --> F4[ClassChatImpl]
    F --> F5[PortraitInferImpl]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

## 主要功能

### 功能模块列表

#### 1. 问卷分析引擎 (QuestionnaireInfer)
- **核心功能**: 问卷数据分析和用户画像计算
- **引擎类型**: QuestionnaireInferImpl
- **业务场景**: 学习前问卷分析、用户能力评估
- **关键方法**:
  - `questionnaire(QuestionnaireRequest)`: 问卷分析处理
  - `userPortrait(UserPortraitRequest)`: 用户画像计算
- **处理模式**: 同步处理，返回确定结果

#### 2. 学习规划引擎 (LearningPlan)
- **核心功能**: 个性化学习路径规划和调整
- **引擎类型**: LearningPlanImpl
- **业务场景**: 学习计划生成、规划调整、进度跟踪
- **关键方法**:
  - `createStudyPlan4Stream(LlmRequest, LlmType)`: 流式学习规划生成
  - `planAjust(PlanAjustRequest)`: 学习规划调整
  - `planSearch(PlanSearchRequest)`: 规划结果查询
- **处理模式**: 支持流式和同步两种处理模式

#### 3. 知识簇选择引擎 (ClusterChoice)
- **核心功能**: 智能知识点聚类和选择
- **引擎类型**: ClusterChoiceRuleImpl
- **业务场景**: 知识点推荐、学习内容筛选
- **关键方法**:
  - `selectKnowledgeCluster(LlmRequest)`: 知识簇选择
- **处理模式**: 同步处理，基于规则引擎

#### 4. 课前对话引擎 (ClassChat)
- **核心功能**: 课前互动对话和学习准备
- **引擎类型**: ClassChatImpl
- **业务场景**: 课前热身、学习状态调整
- **关键方法**:
  - `conductPreClassDialogue(LlmRequest, LlmType)`: 课前对话处理
- **处理模式**: 流式处理，实时交互

#### 5. 问卷解释引擎 (PortraitInfer)
- **核心功能**: 问卷结果解释和分析报告生成
- **引擎类型**: PortraitInferImpl
- **业务场景**: 问卷结果解读、学习建议生成
- **关键方法**:
  - `explainQuestionnaire(LlmRequest, LlmType)`: 问卷解释处理
- **处理模式**: 流式处理，支持实时解释

### 关键技术点
- **响应式编程**: 使用Project Reactor的Flux进行流式数据处理
- **资源管理**: 自动管理AI引擎会话的创建和销毁
- **线程安全**: 双重检查锁定模式确保单例的线程安全
- **工厂创建**: 根据LlmType枚举动态创建对应的引擎实例
- **监控集成**: 集成@ServiceRequestMetrics注解进行性能监控
- **异常处理**: 统一的异常处理和资源清理机制

## 业务处理逻辑分析

### 核心业务逻辑

#### 1. 流式AI处理流程
```java
// AbstractEngineFacade核心流式处理逻辑
protected Flux<LlmResource> streamLlmResources(IflowLlmInstance instance, LlmRequest llmRequest) {
    instance.createSession(llmRequest);
    Flux<LlmResource> resourceFlux = instance.getResource();
    return resourceFlux;
}
```
- **数据流转**: 请求参数 → 会话创建 → 资源流获取 → 流式响应
- **资源管理**: 使用Flux.using确保资源的正确释放
- **异常处理**: 流处理异常自动触发资源清理

#### 2. 引擎实例创建流程
```java
// LlmInstanceFactory工厂创建逻辑
public static IflowLlmInstance createInstance(LlmType type) {
    switch (type) {
        case CLASS_CHAT: return new ClassChatImpl();
        case CLUSTER_CHOICE: return new ClusterChoiceImpl();
        case LEARN_PLAN: return new LearnPlanImpl();
        case PORTRAIT_INFER: return new PortraitInferImpl();
        default: throw new IllegalArgumentException("Unsupported LLM type: " + type);
    }
}
```
- **条件判断**: 根据LlmType枚举选择对应的实现类
- **异常处理**: 不支持的类型抛出IllegalArgumentException

#### 3. 单例管理流程
```java
// 双重检查锁定单例模式
public static LearningPlanImpl getInstance() {
    if (instance == null) {
        synchronized (LearningPlanImpl.class) {
            if (instance == null) {
                instance = new LearningPlanImpl();
            }
        }
    }
    return instance;
}
```
- **线程安全**: 使用volatile关键字和synchronized确保线程安全
- **性能优化**: 双重检查避免不必要的同步开销
- **延迟初始化**: 只在需要时创建实例

### 关键算法说明

#### 1. 响应式流处理算法
- **业务场景**: AI模型的实时响应处理
- **算法实现**: 基于Project Reactor的背压处理
- **优化点**: 异步非阻塞处理提高并发性能

#### 2. 资源生命周期管理算法
```java
// Flux.using资源管理模式
return Flux.using(
    () -> getFlowLlmInstance(llmType),           // 资源创建
    instance -> streamLlmResources(instance, llmRequest), // 资源使用
    instance -> instance.closeSession()          // 资源清理
);
```
- **业务场景**: AI引擎会话的自动管理
- **算法实现**: 函数式资源管理模式
- **优化点**: 确保资源不泄露，提高系统稳定性

#### 3. 工厂模式选择算法
- **业务场景**: 根据业务需求选择合适的AI引擎
- **算法实现**: 基于枚举的策略模式
- **优化点**: 编译时类型安全，运行时高效选择

## 主要对外接口

### 接口类型说明
- **同步接口**: 返回确定结果的AI处理接口
- **流式接口**: 返回Flux流的实时AI处理接口
- **管理接口**: 规划调整和查询等管理功能接口

### 接口详细信息

| 接口类型 | 方法名称 | 请求参数 | 返回值 | 功能描述 | 处理模式 |
|---------|---------|---------|--------|---------|---------|
| 问卷分析 | questionnaire | QuestionnaireRequest | QuestionnaireResponse | 问卷数据分析 | 同步 |
| 用户画像 | userPortrait | UserPortraitRequest | UserPortraitResponse | 用户画像计算 | 同步 |
| 问卷解释 | explainQuestionnaire | LlmRequest, LlmType | Flux<LlmResource> | 问卷结果解释 | 流式 |
| 课前对话 | conductPreClassDialogue | LlmRequest, LlmType | Flux<LlmResource> | 课前互动对话 | 流式 |
| 知识簇选择 | selectKnowledgeCluster | LlmRequest | LlmResource | 知识点聚类选择 | 同步 |
| 学习规划 | createStudyPlan4Stream | LlmRequest, LlmType | Flux<LlmResource> | 学习计划生成 | 流式 |
| 规划调整 | planAjust | PlanAjustRequest | PlanAjustResponse | 学习规划调整 | 同步 |
| 规划查询 | planSearch | PlanSearchRequest | PlanSearchResponse | 规划结果查询 | 同步 |

### LlmType枚举定义

| 枚举值 | 描述 | 对应实现类 | 使用场景 |
|-------|------|-----------|---------|
| CLASS_CHAT | 课前对话 | ClassChatImpl | 课前互动、学习准备 |
| CLUSTER_CHOICE | 知识簇选择 | ClusterChoiceImpl | 知识点推荐、内容筛选 |
| LEARN_PLAN | 学习规划 | LearnPlanImpl | 学习路径规划 |
| PORTRAIT_INFER | 问卷解释 | PortraitInferImpl | 问卷结果解读 |

### 使用示例

#### 流式接口调用
```java
@Autowired
private AbstractEngineFacade engineFacade;

// 学习规划流式处理
Flux<LlmResource> planFlux = engineFacade.createStudyPlan4Stream(llmRequest, LlmType.LEARN_PLAN);
planFlux.subscribe(
    resource -> {
        // 处理流式响应
        log.info("收到规划数据: {}", resource.getContent());
    },
    error -> {
        // 处理异常
        log.error("规划生成失败", error);
    },
    () -> {
        // 处理完成
        log.info("规划生成完成");
    }
);
```

#### 同步接口调用
```java
// 知识簇选择
LlmResource clusterResult = engineFacade.selectKnowledgeCluster(llmRequest);

// 用户画像计算
UserPortraitResponse portraitResult = engineFacade.userPortrait(userPortraitRequest);
```

## 系统配置

### 运行环境要求
- **JDK版本**: JDK 8+
- **操作系统**: 支持Windows、Linux、macOS
- **内存要求**: 最小1GB，推荐2GB+（AI引擎需要较多内存）
- **CPU要求**: 多核CPU，推荐4核+

### 配置文件说明
该模块主要通过Spring配置管理，无独立配置文件。

#### 重要参数说明
- **AI引擎配置**: 由底层AI引擎SDK管理
- **线程池配置**: 响应式处理的线程池配置
- **监控配置**: @ServiceRequestMetrics注解的监控参数

### 启动和部署
```xml
<!-- Maven依赖集成 -->
<dependency>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-engine</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

## 快速开始

### 环境准备
1. 确保JDK 8+环境
2. 配置科大讯飞AI引擎SDK
3. 准备Spring Boot应用环境

### 项目集成
1. **添加Maven依赖**
```xml
<dependency>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-engine</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

2. **注入引擎门面**
```java
@Autowired
private AbstractEngineFacade engineFacade;
```

3. **使用AI引擎服务**
```java
// 问卷分析
QuestionnaireRequest request = new QuestionnaireRequest();
// 设置请求参数...
QuestionnaireResponse response = engineFacade.questionnaire(request);

// 流式学习规划
LlmRequest llmRequest = new LlmRequest();
// 设置请求参数...
Flux<LlmResource> planStream = engineFacade.createStudyPlan4Stream(llmRequest, LlmType.LEARN_PLAN);
planStream.subscribe(resource -> {
    // 处理流式响应
});
```

### 验证测试
1. 启动应用检查引擎初始化
2. 调用问卷分析接口验证同步处理
3. 调用学习规划接口验证流式处理
4. 检查监控指标和日志输出

## 开发指南

### 代码结构
```
ai-teacher-engine/
├── src/main/java/
│   └── com/iflytek/ebgai/ai/teacher/
│       ├── factory/                    # 工厂和单例管理
│       │   ├── LlmInstanceFactory.java # AI实例工厂
│       │   ├── LlmType.java           # AI类型枚举
│       │   ├── *SingletonHelper.java  # 单例管理器
│       └── service/                   # 引擎服务
│           ├── AbstractEngineFacade.java # 抽象门面
│           └── EngineFacadeImpl.java    # 门面实现
└── pom.xml                           # Maven配置
```

### 开发规范
- **单例管理**: 使用双重检查锁定模式确保线程安全
- **资源管理**: 使用Flux.using模式管理AI引擎资源
- **异常处理**: 统一异常处理和资源清理
- **监控集成**: 在关键方法上添加@ServiceRequestMetrics注解
- **类型安全**: 使用枚举而非字符串标识AI引擎类型

### 扩展指南
1. **新增AI引擎类型**
   - 在LlmType枚举中添加新类型
   - 在LlmInstanceFactory中添加对应的创建逻辑
   - 创建对应的SingletonHelper类
   - 在EngineFacadeImpl中添加对应的接口方法

2. **自定义处理逻辑**
   - 继承AbstractEngineFacade实现自定义门面
   - 重写需要定制的方法
   - 注册为Spring Bean替换默认实现

### 测试指南
```bash
# 运行单元测试
mvn test

# 运行引擎集成测试
mvn test -Dtest=*EngineTest

# 编译打包
mvn clean package
```

### 性能优化建议
- **单例复用**: 合理使用单例模式减少实例创建开销
- **流式处理**: 使用响应式编程提高并发处理能力
- **资源池化**: 考虑AI引擎实例的池化管理
- **监控调优**: 根据监控数据调整线程池和内存配置
