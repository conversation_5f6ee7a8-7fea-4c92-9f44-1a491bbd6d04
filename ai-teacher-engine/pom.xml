<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.ebg.ai</groupId>
        <artifactId>ai-teacher</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>ai-teacher-engine</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <skipTests>true</skipTests>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.ebg.ai</groupId>
            <artifactId>ai-teacher-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.rec</groupId>
            <artifactId>ai-tutoring-teacher</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <!-- 其他必要依赖（如 reactor-core 用于响应式流） -->
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
            <version>3.4.0</version>
        </dependency>

    </dependencies>
</project>