package com.iflytek.ebgai.ai.teacher.service;

import com.iflytek.ebgai.ai.teacher.factory.LlmInstanceFactory;
import com.iflytek.ebgai.ai.teacher.factory.LlmType;
import com.iflytek.rec.teacher.IflowLlmInstance;
import com.iflytek.rec.teacher.interfaces.param.*;
import reactor.core.publisher.Flux;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 引擎接口门面
 *
 * <AUTHOR>
 * @date
 */
public abstract class AbstractEngineFacade {
    /**
     * 问卷
     *
     * @param questionnaireRequest
     * @return
     */
    public abstract QuestionnaireResponse questionnaire(@Valid @NotNull QuestionnaireRequest questionnaireRequest);


    /**
     * 用户画像计算
     *
     * @param userPortraitRequest
     * @return
     */
    public abstract UserPortraitResponse userPortrait(@Valid @NotNull UserPortraitRequest userPortraitRequest);

    /**
     * 问卷解释
     *
     * @param llmRequest
     * @param llmType
     * @return
     */
    public abstract Flux<LlmResource> explainQuestionnaire(LlmRequest llmRequest, LlmType llmType);

    /**
     * 课前对话
     *
     * @param llmRequest
     * @param llmType
     * @return
     */
    public abstract Flux<LlmResource> conductPreClassDialogue(LlmRequest llmRequest, LlmType llmType);

    /**
     * 知识簇选择
     *
     * @param llmRequest
     * @param llmType
     * @return
     */
    public abstract Flux<LlmResource> selectKnowledgeCluster(LlmRequest llmRequest, LlmType llmType);

    public abstract LlmResource selectKnowledgeCluster(LlmRequest llmRequest);

    /**
     * 学习规划
     *
     * @param llmRequest
     * @param llmType
     * @return
     */
    public abstract Flux<LlmResource> createStudyPlan4Stream(LlmRequest llmRequest, LlmType llmType);

    /**
     * 规划调整
     *
     * @param planAjustRequest
     * @return
     */
    public abstract PlanAjustResponse planAjust(PlanAjustRequest planAjustRequest);

    /**
     * 规划结果查询
     *
     * @param planSearchRequest
     * @return
     */
    public abstract PlanSearchResponse planSearch(PlanSearchRequest planSearchRequest);

    protected Flux<LlmResource> streamLlmResources(IflowLlmInstance instance, LlmRequest llmRequest) {
        instance.createSession(llmRequest);
        // 订阅getReasource()返回的Flux，并绑定到sink
        Flux<LlmResource> resourceFlux = instance.getResource();
        return resourceFlux;
    }

    protected IflowLlmInstance getFlowLlmInstance(LlmType llmType) {
        IflowLlmInstance instance = LlmInstanceFactory.createInstance(llmType);
        return instance;
    }
}
