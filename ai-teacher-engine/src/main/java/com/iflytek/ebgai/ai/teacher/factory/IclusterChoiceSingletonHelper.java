package com.iflytek.ebgai.ai.teacher.factory;

import com.iflytek.rec.teacher.interfaces.impl.ClusterChoiceRuleImpl;

public class IclusterChoiceSingletonHelper {
    private volatile static ClusterChoiceRuleImpl instance;

    private IclusterChoiceSingletonHelper() {
    }

    public static ClusterChoiceRuleImpl getInstance() {
        if (instance == null) {
            synchronized (ClusterChoiceRuleImpl.class) {
                if (instance == null) {
                    instance = new ClusterChoiceRuleImpl();
                }
            }
        }
        return instance;
    }
}
