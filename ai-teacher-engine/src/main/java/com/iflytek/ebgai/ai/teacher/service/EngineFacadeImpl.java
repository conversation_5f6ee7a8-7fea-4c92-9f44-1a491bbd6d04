package com.iflytek.ebgai.ai.teacher.service;

import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;
import com.iflytek.ebgai.ai.teacher.factory.IclusterChoiceSingletonHelper;
import com.iflytek.ebgai.ai.teacher.factory.LearningPlanSingletonHelper;
import com.iflytek.ebgai.ai.teacher.factory.LlmType;
import com.iflytek.ebgai.ai.teacher.factory.QuestionnaireInferSingleHelper;
import com.iflytek.rec.teacher.interfaces.param.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName EngineFacadeImpl
 * @Date: 2025/5/20 17:05
 * @Description:
 */
@Slf4j
@Component
public class EngineFacadeImpl extends AbstractEngineFacade {

    @ServiceRequestMetrics(desc = "问卷引擎接口", type = "engine")
    @Override
    public QuestionnaireResponse questionnaire(QuestionnaireRequest questionnaireRequest) {
        return QuestionnaireInferSingleHelper.getInstance().questionnaire(questionnaireRequest);
    }

    @ServiceRequestMetrics(desc = "画像引擎接口", type = "engine")
    @Override
    public UserPortraitResponse userPortrait(UserPortraitRequest userPortraitRequest) {
        return QuestionnaireInferSingleHelper.getInstance().userPortrait(userPortraitRequest);
    }


    @ServiceRequestMetrics(desc = "问卷解释引擎接口", type = "engine")
    @Override
    public Flux<LlmResource> explainQuestionnaire(LlmRequest llmRequest, LlmType llmType) {
        return Flux.using(() -> getFlowLlmInstance(llmType), instance -> streamLlmResources(instance, llmRequest), instance -> {
            log.info("explainQuestionnaire closeSession");
            instance.closeSession();
        });
    }

    @ServiceRequestMetrics(desc = "课前对话引擎接口", type = "engine")
    @Override
    public Flux<LlmResource> conductPreClassDialogue(LlmRequest llmRequest, LlmType llmType) {
        return Flux.using(() -> getFlowLlmInstance(llmType), instance -> streamLlmResources(instance, llmRequest), instance -> {
            log.info("conductPreClassDialogue closeSession");
            instance.closeSession();
        });
    }

    @Deprecated
//    @ServiceRequestMetrics(desc = "知识簇选择引擎接口",type = "engine")
    @Override
    public Flux<LlmResource> selectKnowledgeCluster(LlmRequest llmRequest, LlmType llmType) {
        return Flux.using(() -> getFlowLlmInstance(llmType), instance -> streamLlmResources(instance, llmRequest), instance -> {
            log.info("selectKnowledgeCluster closeSession");
            instance.closeSession();
        });
    }

    @ServiceRequestMetrics(desc = "知识簇选择引擎接口", type = "engine")
    @Override
    public LlmResource selectKnowledgeCluster(LlmRequest llmRequest) {
        return IclusterChoiceSingletonHelper.getInstance().clusterChoice(llmRequest);
    }

    @ServiceRequestMetrics(desc = "学习规划引擎接口", type = "engine")
    @Override
    public Flux<LlmResource> createStudyPlan4Stream(LlmRequest llmRequest, LlmType llmType) {
        return Flux.using(() -> getFlowLlmInstance(llmType), instance -> streamLlmResources(instance, llmRequest), instance -> {
            log.info("createStudyPlan4Stream closeSession");
            instance.closeSession();
        });
    }

    @ServiceRequestMetrics(desc = "规划调整引擎接口", type = "engine")
    @Override
    public PlanAjustResponse planAjust(PlanAjustRequest planAjustRequest) {
        return LearningPlanSingletonHelper.getInstance().planAjust(planAjustRequest);
    }

    @ServiceRequestMetrics(desc = "查询规划结果引擎接口", type = "engine")
    @Override
    public PlanSearchResponse planSearch(PlanSearchRequest planSearchRequest) {
        return LearningPlanSingletonHelper.getInstance().planSearch(planSearchRequest);
    }
}
