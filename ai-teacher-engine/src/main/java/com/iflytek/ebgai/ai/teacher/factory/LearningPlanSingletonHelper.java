package com.iflytek.ebgai.ai.teacher.factory;

import com.iflytek.rec.teacher.interfaces.impl.LearningPlanImpl;

public class LearningPlanSingletonHelper {

    private volatile static LearningPlanImpl instance;

    private LearningPlanSingletonHelper() {
    }

    public static LearningPlanImpl getInstance() {
        if (instance == null) {
            synchronized (LearningPlanImpl.class) {
                if (instance == null) {
                    instance = new LearningPlanImpl();
                }
            }
        }
        return instance;
    }
}
