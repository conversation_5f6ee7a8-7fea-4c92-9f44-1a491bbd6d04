package com.iflytek.ebgai.ai.teacher.factory;

import com.iflytek.rec.teacher.interfaces.impl.QuestionnaireInferImpl;

public class QuestionnaireInferSingleHelper {

    private volatile static QuestionnaireInferImpl instance;

    private QuestionnaireInferSingleHelper() {
    }

    public static QuestionnaireInferImpl getInstance() {
        if (instance == null) {
            synchronized (QuestionnaireInferImpl.class) {
                if (instance == null) {
                    instance = new QuestionnaireInferImpl();
                }
            }
        }
        return instance;
    }
}
