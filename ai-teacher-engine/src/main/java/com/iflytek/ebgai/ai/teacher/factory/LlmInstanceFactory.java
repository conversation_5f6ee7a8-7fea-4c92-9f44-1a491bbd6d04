package com.iflytek.ebgai.ai.teacher.factory;

import com.iflytek.rec.teacher.IflowLlmInstance;
import com.iflytek.rec.teacher.interfaces.impl.ClassChatImpl;
import com.iflytek.rec.teacher.interfaces.impl.ClusterChoiceImpl;
import com.iflytek.rec.teacher.interfaces.impl.LearnPlanImpl;
import com.iflytek.rec.teacher.interfaces.impl.PortraitInferImpl;

/**
 * 模型实例创建静态工厂
 */
public class LlmInstanceFactory {
    public static IflowLlmInstance createInstance(LlmType type) {
        switch (type) {
            case CLASS_CHAT:
                return new ClassChatImpl();
            case CLUSTER_CHOICE:
                return new ClusterChoiceImpl();
            case LEARN_PLAN:
                return new LearnPlanImpl();
            case PORTRAIT_INFER:
                return new PortraitInferImpl();
            default:
                throw new IllegalArgumentException("Unsupported LLM type: " + type);
        }
    }
}