# AI Teacher Common Module

## 项目概述

### 项目名称
AI Teacher Common Module - AI伴学服务公共组件模块

### 项目描述
AI Teacher Common模块是AI伴学服务的核心公共组件库，提供了项目中所有模块共享的基础设施、工具类、实体类、异常处理、注解和切面等功能。该模块采用标准的Maven项目结构，为整个AI伴学系统提供统一的基础服务支撑。

### 版本信息
- **当前版本**: 1.0.2-SNAPSHOT
- **Java版本**: JDK 8
- **编码格式**: UTF-8

### 许可证信息
Apache License, Version 2.0

## 技术架构

### 整体技术架构描述
- **框架基础**: Spring Boot + Spring AOP
- **设计模式**: 
  - 切面编程(AOP)：用于监控和指标收集
  - 工厂模式：异常处理体系
  - 单例模式：Spring Context管理
- **核心技术栈**:
  - Spring Boot 2.x (基于skynet-boot-starter-parent 4.0.18)
  - Spring AOP：切面编程支持
  - Micrometer：指标监控
  - Lombok：代码简化
  - Logback：日志处理

### 模块职责说明
- **实体管理**: 提供统一的响应实体和返回码定义
- **异常处理**: 统一的异常处理体系和自定义异常类
- **监控切面**: 接口性能监控和指标收集
- **注解支持**: 自定义注解用于监控和追踪
- **工具类**: Spring上下文工具和链路追踪工具

### 架构图

```mermaid
graph TB
    A[AI Teacher Common Module] --> B[Entity Layer]
    A --> C[Exception Layer]
    A --> D[Aspect Layer]
    A --> E[Annotation Layer]
    A --> F[Utils Layer]
    
    B --> B1[CommonResponse]
    B --> B2[RetCode]
    
    C --> C1[BaseException]
    C --> C2[BizException]
    C --> C3[ParamValidateException]
    
    D --> D1[RequestMetricsAspect]
    
    E --> E1[ServiceRequestMetrics]
    
    F --> F1[SpringContext]
    F --> F2[TraceIdConvert]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

## 主要功能

### 功能模块列表

#### 1. 统一响应实体 (Entity)
- **CommonResponse**: 通用响应包装类，支持泛型数据返回
- **RetCode**: 统一返回码枚举，包含成功、参数错误、业务错误等状态码
- **核心业务逻辑**: 标准化API响应格式，统一错误码管理

#### 2. 异常处理体系 (Exception)
- **BaseException**: 抽象基础异常类，定义异常处理规范
- **BizException**: 业务异常类，用于业务逻辑错误处理
- **ParamValidateException**: 参数校验异常类，专门处理参数验证失败
- **核心业务逻辑**: 分层异常处理，便于问题定位和错误追踪

#### 3. 监控切面 (Aspect)
- **RequestMetricsAspect**: 接口监控切面，自动收集QPS、响应时间、并发数等指标
- **核心业务逻辑**: 基于AOP的无侵入式监控，支持Prometheus指标导出

#### 4. 自定义注解 (Annotation)
- **ServiceRequestMetrics**: 服务请求监控注解，标记需要监控的方法
- **核心业务逻辑**: 声明式监控配置，简化监控代码编写

#### 5. 工具类 (Utils)
- **SpringContext**: Spring上下文工具类，提供Bean获取功能
- **TraceIdConvert**: 链路追踪ID转换器，用于日志追踪
- **核心业务逻辑**: 简化Spring容器操作，支持分布式链路追踪

### 关键技术点
- **AOP切面编程**: 使用@Around注解实现方法级别的监控
- **Micrometer集成**: 支持多种监控系统的指标收集
- **异常链传递**: 完整的异常信息传递和错误码管理
- **泛型支持**: CommonResponse支持任意类型的数据返回
- **线程安全**: 并发请求计数使用AtomicInteger保证线程安全

## 业务处理逻辑分析

### 核心业务逻辑

#### 1. 统一响应处理流程
```java
// CommonResponse的核心处理逻辑
public CommonResponse<T> success(String traceId) {
    this.code = RetCode.SUCCESS.getCode();
    this.message = RetCode.SUCCESS.getDesc();
    this.traceId = traceId;
    return this;
}
```
- **数据流转**: 请求 → 业务处理 → CommonResponse包装 → 返回客户端
- **条件判断**: 根据业务处理结果设置不同的返回码和消息
- **异常处理**: 异常情况下自动设置错误码和错误信息

#### 2. 监控切面处理流程
```java
@Around("@annotation(serviceRequestMetrics)")
public Object around(ProceedingJoinPoint joinPoint, ServiceRequestMetrics serviceRequestMetrics)
```
- **数据流转**: 方法调用 → 切面拦截 → 指标收集 → 方法执行 → 结果返回
- **条件判断**: 根据方法执行结果判断成功或失败状态
- **循环结构**: 持续监控所有标记了@ServiceRequestMetrics注解的方法
- **异常处理**: 捕获并分类处理不同类型的异常

#### 3. 异常处理机制
- **分层异常**: BaseException → BizException/ParamValidateException
- **错误码映射**: 异常类型自动映射到对应的RetCode
- **链路追踪**: 异常信息包含traceId用于问题定位

### 关键算法说明

#### 1. 并发请求计数算法
```java
AtomicInteger concurrentCount = concurrentRequests.computeIfAbsent(methodName, k -> new AtomicInteger(0));
concurrentCount.incrementAndGet(); // 请求开始
// ... 业务处理
concurrentCount.decrementAndGet(); // 请求结束
```
- **业务场景**: 实时监控接口并发访问量
- **优化点**: 使用ConcurrentHashMap + AtomicInteger保证线程安全和高性能

#### 2. 指标标签构建算法
```java
Tags tags = Tags.of("application", application, "ip", ip, "desc", desc, 
                   "method", methodName, "error_code", errorCode, "type", type);
```
- **业务场景**: 为监控指标添加多维度标签，支持细粒度监控分析
- **优化点**: 标签化设计便于监控系统进行数据聚合和查询

## 主要对外接口

### 接口类型说明
- **编程接口**: 提供Java API供其他模块调用
- **注解接口**: 提供声明式编程支持
- **切面接口**: 提供AOP功能集成

### 接口详细信息

| 接口类型 | 接口名称 | 功能描述 | 使用场景 | 示例 |
|---------|---------|---------|---------|------|
| 实体类 | CommonResponse | 统一响应包装 | API响应标准化 | `new CommonResponse<>().success(traceId)` |
| 枚举类 | RetCode | 返回码定义 | 错误码标准化 | `RetCode.SUCCESS.getCode()` |
| 异常类 | BizException | 业务异常 | 业务错误处理 | `throw new BizException("业务处理失败")` |
| 异常类 | ParamValidateException | 参数校验异常 | 参数验证失败 | `throw new ParamValidateException(RetCode.PARAM_ERROR, traceId)` |
| 注解 | @ServiceRequestMetrics | 监控注解 | 方法性能监控 | `@ServiceRequestMetrics(desc="用户查询", type="service")` |
| 工具类 | SpringContext | Spring容器工具 | Bean获取 | `SpringContext.getBean(UserService.class)` |

## 系统配置

### 运行环境要求
- **JDK版本**: JDK 8+
- **操作系统**: 支持Windows、Linux、macOS
- **内存要求**: 最小128MB，推荐256MB+
- **依赖框架**: Spring Boot 2.x

### 配置文件说明
该模块作为公共组件，主要通过Maven依赖方式集成，无独立配置文件。

#### 重要参数说明
- **spring.application.name**: 应用名称，用于监控指标标签
- **监控相关配置**: 由使用方在application.properties中配置

### 启动和部署
```xml
<!-- Maven依赖集成 -->
<dependency>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-common</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

## 快速开始

### 环境准备
1. 确保JDK 8+环境
2. Maven 3.6+构建工具
3. IDE支持（推荐IntelliJ IDEA）

### 项目集成
1. **添加Maven依赖**
```xml
<dependency>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-common</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

2. **使用统一响应**
```java
@RestController
public class UserController {
    @PostMapping("/user")
    public CommonResponse<User> createUser(@RequestBody User user) {
        // 业务处理
        return new CommonResponse<User>().success(traceId).setData(user);
    }
}
```

3. **添加监控注解**
```java
@Service
public class UserService {
    @ServiceRequestMetrics(desc = "用户创建", type = "service")
    public User createUser(User user) {
        // 业务逻辑
        return user;
    }
}
```

### 验证测试
1. 启动应用后检查日志无异常
2. 访问监控端点验证指标收集
3. 触发异常验证异常处理机制

## 开发指南

### 代码结构
```
ai-teacher-common/
├── src/main/java/
│   └── com/iflytek/ebgai/ai/teacher/common/
│       ├── annotation/          # 自定义注解
│       ├── aspect/             # AOP切面
│       ├── entity/             # 实体类
│       └── exception/          # 异常处理
└── pom.xml                     # Maven配置
```

### 开发规范
- **异常处理**: 继承BaseException创建自定义异常
- **监控注解**: 在关键方法上添加@ServiceRequestMetrics注解
- **返回码**: 新增错误码需在RetCode枚举中定义
- **代码风格**: 遵循阿里巴巴Java开发手册

### 测试指南
```bash
# 运行单元测试
mvn test

# 编译打包
mvn clean package
```
