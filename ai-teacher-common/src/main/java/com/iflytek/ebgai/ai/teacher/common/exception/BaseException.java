package com.iflytek.ebgai.ai.teacher.common.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public abstract class BaseException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private String errCode;

    protected BaseException(String errMessage) {
        super(errMessage);
    }

    protected BaseException(String errCode, String errMessage) {
        super(errMessage);
        this.errCode = errCode;
    }

    protected BaseException(String errMessage, Throwable e) {
        super(errMessage, e);
    }

    protected BaseException(String errCode, String errMessage, Throwable e) {
        super(errMessage, e);
        this.errCode = errCode;
    }

    public String getCode() {
        return this.getErrCode();
    }
}

