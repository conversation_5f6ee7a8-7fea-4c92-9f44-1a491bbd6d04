package com.iflytek.ebgai.ai.teacher.common.exception;


import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;

/**
 * <AUTHOR>
 */
public class BizException extends BaseException {
    private static final long serialVersionUID = 1L;
    private static final RetCode DEFAULT_ERR = RetCode.UNKNOWN_ERROR;

    public BizException(String errMessage) {
        super(DEFAULT_ERR.getCode(), errMessage);
    }

    public BizException(String errCode, String errMessage) {
        super(errCode, errMessage);
    }

    public BizException(String errMessage, Throwable e) {
        super(DEFAULT_ERR.getCode(), errMessage, e);
    }

    public BizException(String errorCode, String errMessage, Throwable e) {
        super(errorCode, errMessage, e);
    }

}

