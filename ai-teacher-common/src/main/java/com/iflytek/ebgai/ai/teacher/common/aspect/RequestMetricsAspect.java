package com.iflytek.ebgai.ai.teacher.common.aspect;

import com.google.common.base.Stopwatch;
import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import io.micrometer.core.instrument.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import skynet.boot.AppContext;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 接口监控切面
 * 用于收集接口的请求量、QPS、响应时间等指标
 */
@Slf4j
@Aspect
@Component
public class RequestMetricsAspect {

    @Value("${spring.application.name}")
    private String application;

    private static final String DEFAULT_TAG_VALUE = "unknown";
    private static final String METRIC_PREFIX = "recommend_";

    private final String ip;
    private final MeterRegistry meterRegistry;
    private final ConcurrentHashMap<String, AtomicInteger> concurrentRequests = new ConcurrentHashMap<>();

    public RequestMetricsAspect(MeterRegistry meterRegistry, AppContext appContext) {
        this.meterRegistry = meterRegistry;
        this.ip = StringUtils.isBlank(appContext.getIpEndpoint()) ? DEFAULT_TAG_VALUE : appContext.getIpEndpoint();
    }

    @Around("@annotation(serviceRequestMetrics)")
    public Object around(ProceedingJoinPoint joinPoint, ServiceRequestMetrics serviceRequestMetrics) throws Throwable {
        log.debug("接口指标监控切面开始");
        String methodName = getMethodName(joinPoint);
        String desc = serviceRequestMetrics.desc();
        String type = serviceRequestMetrics.type();

        // 记录并发数
        AtomicInteger concurrentCount = concurrentRequests.computeIfAbsent(methodName, k -> new AtomicInteger(0));
        concurrentCount.incrementAndGet();

        // 记录开始时间
        Stopwatch stopWatch = Stopwatch.createStarted();
        Object result = null;
        String errorCode = "0"; // 默认成功码为0

        try {
            result = joinPoint.proceed();
            // 解析响应码
            errorCode = parseErrorCode(result);
            return result;
        } catch (Throwable t) {
            if (t instanceof ParamValidateException) {
                errorCode = ((ParamValidateException) t).getErrCode();
                throw t;
            }
            errorCode = DEFAULT_TAG_VALUE; // 系统异常使用unknown
            throw t;
        } finally {
            // 减少并发计数
            concurrentCount.decrementAndGet();

            // 计算耗时
            Duration duration = stopWatch.stop().elapsed();

            // 构建标签
            Tags tags = Tags.of("application", application, "ip", ip, "desc", desc, "method", methodName, "error_code", errorCode, "type", type);

            // 记录总请求数
            Counter totalRequests = Counter.builder(METRIC_PREFIX + "requests_total").tags(tags).register(meterRegistry);
            totalRequests.increment();

            // 记录响应时间
            Timer responseTime = Timer.builder(METRIC_PREFIX + "response_time_seconds").tags(tags).register(meterRegistry);
            responseTime.record(duration);

            // 记录并发数
            Gauge.builder(METRIC_PREFIX + "concurrent_requests", concurrentCount, AtomicInteger::get).tags(tags).register(meterRegistry);

            // 记录QPS (通过Timer的count()方法获取)
            Timer qpsTimer = Timer.builder(METRIC_PREFIX + "qps").tags(tags).register(meterRegistry);
            qpsTimer.record(duration);

            if (log.isDebugEnabled()) {
                log.debug("Request metrics - service:{}, method:{}, errorCode:{}, duration:{}ms", desc, methodName, errorCode, duration.toMillis());
            }
        }
    }

    private String getMethodName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }

    private String parseErrorCode(Object result) {
        if (result == null) {
            return DEFAULT_TAG_VALUE;
        }

        try {
            // 优先检查SSE类型
            if (result instanceof SseEmitter) {
                // SSE连接成功建立时返回成功码
                return RetCode.SUCCESS.getCode();
            }
            if (result instanceof CommonResponse) {
                CommonResponse<?> resp = (CommonResponse<?>) result;
                return resp.getCode();
            } else {
                return DEFAULT_TAG_VALUE;
            }
        } catch (Throwable t) {
            log.warn("Parse response error code failed", t);
        }
        return DEFAULT_TAG_VALUE;
    }
}

