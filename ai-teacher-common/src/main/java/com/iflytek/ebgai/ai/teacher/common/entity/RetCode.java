package com.iflytek.ebgai.ai.teacher.common.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @data 2024/10/23
 * @desc 返回码枚举类
 */

@Getter
@AllArgsConstructor
public enum RetCode {
    SUCCESS("000000", "success"), // 参数错误
    PARAM_ERROR("100001", "invalid param"), //场景代码错误
    SCENE_ERROR("100002", "sceneCode error"), //不支持学科学段
    PHA_SUB_ERROR("100003", "phase error"),

    //鉴权错误
    AUTH_ERROR("200001", "auth error"), //请求头缺失
    INPUT_PARAMETERS_MISSING("20001", "入参缺失"), CONTENT_CHECK_FAIL("80003", "内容审核不通过"),

    // 查询问卷解释错误
    QUESTIONNAIRE_DB_QUERY_EMPTY("300001", "query db is empty"), QUESTIONNAIRE_DB_QUERY_FILTER_EMPTY("300002", "query db filter is empty"), QUESTIONNAIRE_DB_QUERY_ERROR("300003", "query db error"), QUESTIONNAIRE_ENGINE_QUERY_EMPTY("300004", "query engine is empty"), QUESTIONNAIRE_ENGINE_QUERY_ERROR("300005", "query engine error"),

    // 引擎入参校验失败
    ENGINE_PARAM_ERROR("400001", "invalid engine param"), //引擎返回异常
    ENGINE_ERROR("400004", "engine error"), //
    AI_TEACHER_ERROR("500", "ai-theacher error"),


    // 未知错误
    UNKNOWN_ERROR("999999", "unknown error");

    private final String code;
    private final String desc;

    public static RetCode getByCode(String code) {
        for (RetCode retCode : RetCode.values()) {
            if (retCode.getCode().equals(code)) {
                return retCode;
            }
        }
        return UNKNOWN_ERROR;
    }
}
