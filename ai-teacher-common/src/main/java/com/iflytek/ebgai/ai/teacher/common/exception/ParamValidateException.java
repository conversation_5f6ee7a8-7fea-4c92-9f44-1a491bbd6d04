package com.iflytek.ebgai.ai.teacher.common.exception;

import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import lombok.Getter;

/**
 * 自定义参数校验异常，用于传递参数校验失败的信息
 */
@Getter
public class ParamValidateException extends BaseException {

    private String traceId;

    public ParamValidateException(String code, String message, String traceId) {
        super(code, message);
        this.traceId = traceId;
    }

    public ParamValidateException(RetCode retCode, String traceId) {
        super(retCode.getCode(), retCode.getDesc());
        this.traceId = traceId;
    }
}