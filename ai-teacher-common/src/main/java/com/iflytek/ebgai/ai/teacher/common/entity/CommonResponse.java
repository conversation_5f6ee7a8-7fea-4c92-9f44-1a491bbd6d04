package com.iflytek.ebgai.ai.teacher.common.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class CommonResponse<T> implements Serializable {
    private String code;
    private String message;
    private String traceId;
    private T data;

    public CommonResponse() {
        this.code = RetCode.SUCCESS.getCode();
        this.message = RetCode.SUCCESS.getDesc();
    }

    public CommonResponse<T> success(String traceId) {
        this.code = RetCode.SUCCESS.getCode();
        this.message = RetCode.SUCCESS.getDesc();
        this.traceId = traceId;
        return this;
    }

    public CommonResponse(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public CommonResponse(String code, String message, String traceId) {
        this.traceId = traceId;
        this.code = code;
        this.message = message;
    }

    public CommonResponse(String code, String message, String traceId, T data) {
        this.traceId = traceId;
        this.code = code;
        this.message = message;
        this.data = data;
    }
} 