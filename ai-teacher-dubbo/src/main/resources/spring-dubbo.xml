<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans.xsd
     http://code.alibabatech.com/schema/dubbo
     http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="ai-teacher-dubbo"/>
    <dubbo:protocol name="dubbo" port="${dubbo.service-port}"/>
    <dubbo:provider timeout="30000"/>

    <!--epas注册-->
    <dubbo:registry id="epas" protocol="epas" address="epasConfig"/>

    <!--dubbo注册-->
    <dubbo:registry id="app" protocol="zookeeper" address="${application.dubbo.zookeeper.address}"
                    file="skyline-science-dubbo-app.cache"/>
    <!--服务提供者demo-->
    <dubbo:service registry="epas" ref="demoService"
                   interface="com.iflytek.ebgai.ai.teacher.dubbo.service.DemoService"/>
    <!--服务提供者 规划接口-->
    <dubbo:service registry="epas" ref="learningPlanApiService"
                   interface="com.iflytek.ebgai.ai.teacher.dubbo.service.LearningPlanApiService"/>

    <!--服务提供者 问卷接口-->
    <dubbo:service registry="epas" ref="questionnaireInferApiService"
                   interface="com.iflytek.ebgai.ai.teacher.dubbo.service.QuestionnaireInferApiService"/>

</beans>