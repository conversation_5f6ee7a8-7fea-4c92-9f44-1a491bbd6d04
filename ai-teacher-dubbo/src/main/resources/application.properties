#------------------------------------------------------------------------------------------------------
# Basic Application Settings
#------------------------------------------------------------------------------------------------------
spring.application.name=ai-teacher-dubbo
server.port=32389
server.servlet.context-path=/ai-teacher-dubbo
#------------------------------------------------------------------------------------------------------
# System Parameters
#------------------------------------------------------------------------------------------------------
#IP=***********
#skynet.ipAddress=**********
skynet.api.swagger2.enabled=false
#------------------------------------------------------------------------------------------------------
# Dubbo Configuration
#------------------------------------------------------------------------------------------------------
application.epas.appKey=ai-teacher-dubbo
application.epas.appSecret=924aeaf3fc5b06b3
application.epas.addrServerUrl=http://pre.epas.changyan.com/address
dubbo.service-port=35880
application.dubbo.zookeeper.address=${IP}:2181
#------------------------------------------------------------------------------------------------------
# Management & Prometheus Monitoring
#------------------------------------------------------------------------------------------------------
management.endpoints.web.exposure.include=*
auth.whitelist.urls[0]=/ai-teacher-dubbo/actuator/prometheus
auth.whitelist.urls[1]=/ai-teacher-dubbo/actuator/health
auth.whitelist.urls[2]=/ai-teacher-dubbo/actuator/info
management.metrics.export.prometheus.enabled=true
#------------------------------------------------------------------------------------------------------
# Encoding Settings
#------------------------------------------------------------------------------------------------------
server.tomcat.uri-encoding=UTF-8
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
#------------------------------------------------------------------------------------------------------
# Logging Configuration
#------------------------------------------------------------------------------------------------------
logging.level.ROOT=ERROR
logging.level.com.iflytek.skyline=ERROR
logging.level.com.iflytek.skylab=ERROR
logging.level.org.mongodb.driver=WARN
logging.level.org.springframework.web=WARN
logging.level.com.iflytek.ebgai.ai.teacher=INFO
logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{TRACE_ID}]){cyan}%clr(:){faint} %m%n
#------------------------------------------------------------------------------------------------------
# MongoDB Configuration
#------------------------------------------------------------------------------------------------------
#---------------------------------mongoDB-------------------------------
#-----------------------------------------------------------------
# skyline-brave
skyline.brave.enabled=true
skyline.brave.trace-debug-enabled=true
skyline.brave.file-enabled=true
skyline.brave.aop-enabled=true
skyline.brave.kafka-enabled=false
skyline.brave.allowSubjectCodes=00,01,02,03,04,05,06,13
#---------------------------------zookeeper-----------------------------------
spring.cloud.zookeeper.connect-string=***********:2181
#---------------------------------engine prompt-------------------------------
engine.prompt.class_chat=
engine.prompt.cluster_choice=
engine.prompt.portrait_infer=
engine.prompt.study_plan=
engine.knowledge.qa=