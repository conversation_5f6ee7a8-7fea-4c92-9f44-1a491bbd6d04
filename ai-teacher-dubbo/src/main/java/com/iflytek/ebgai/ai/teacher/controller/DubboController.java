package com.iflytek.ebgai.ai.teacher.controller;


import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.DeleteLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanResponse;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferResponse;
import com.iflytek.ebgai.ai.teacher.dubbo.service.LearningPlanApiService;
import com.iflytek.ebgai.ai.teacher.dubbo.service.QuestionnaireInferApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;

/**
 * @Auther: huizhang68
 * @date: 2025/6/11 14:26
 */
@Slf4j
@Api(tags = "dubbo相关接口")
@RestController
@RequestMapping(value = "/api/v1/dubbo", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
public class DubboController {
    @Autowired
    private LearningPlanApiService learningPlanApiService;
    @Autowired
    private QuestionnaireInferApiService questionnaireInferApiService;

    @ApiOperation("查询用户问卷结果接口")
    @PostMapping(value = "/queryQuestionnaire", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    public CommonResponse<QueryQuestionnaireInferResponse> queryQuestionnaire(@RequestBody QueryQuestionnaireInferRequest request) {
        return questionnaireInferApiService.query(request);
    }

    @ApiOperation("根据查询用户规划结果接口开发")
    @PostMapping(value = "/queryPlan", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    public CommonResponse<QueryLearningPlanResponse> queryPlan(@RequestBody QueryLearningPlanRequest request) {
        return learningPlanApiService.queryPlan(request);
    }

    @ApiOperation("放弃规划")
    @PostMapping(value = "/deletePlan", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    public CommonResponse<Serializable> deletePlan(@RequestBody DeleteLearningPlanRequest request) {
        return learningPlanApiService.deletePlan(request);
    }
}
