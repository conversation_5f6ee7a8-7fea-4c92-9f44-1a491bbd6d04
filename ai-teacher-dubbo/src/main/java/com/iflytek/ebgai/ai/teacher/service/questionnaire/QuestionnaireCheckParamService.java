package com.iflytek.ebgai.ai.teacher.service.questionnaire;

import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.DeleteLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferRequest;

/**
 * 问卷前端接口参数校验服务
 *
 * @Author:huizhang68
 * @Date:2025/5/28
 */
public interface QuestionnaireCheckParamService {

    void checkInputParameters(QueryQuestionnaireInferRequest requestAO) throws ParamValidateException;

    void checkInputParameters(QueryLearningPlanRequest requestAO) throws ParamValidateException;

    void checkInputParameters(DeleteLearningPlanRequest requestAO) throws ParamValidateException;

}
