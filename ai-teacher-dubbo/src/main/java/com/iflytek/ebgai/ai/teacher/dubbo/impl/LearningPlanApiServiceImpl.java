package com.iflytek.ebgai.ai.teacher.dubbo.impl;


import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.annotation.DubboTrace;
import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.dataapi.AITeacherDataHub;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.mapper.PlanResultMapping;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.DeleteLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.PlanResultDataAO;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanResponse;
import com.iflytek.ebgai.ai.teacher.dubbo.service.LearningPlanApiService;
import com.iflytek.ebgai.ai.teacher.service.questionnaire.QuestionnaireCheckParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * 规划相关接口
 *
 * @Author:huizhang68
 * @Date:2025/5/24
 */
@Slf4j
@Component("learningPlanApiService")
public class LearningPlanApiServiceImpl implements LearningPlanApiService {

    @Autowired
    private QuestionnaireCheckParamService checkParamService;

    @Override
    @ServiceRequestMetrics(desc = "dubbo-查询用户规划结果")
    @DubboTrace(desc = "dubbo-查询用户规划结果")
    public CommonResponse<QueryLearningPlanResponse> queryPlan(QueryLearningPlanRequest requestAO) {
        String traceId = requestAO.getTraceId();
        log.info("[TraceID:{}] [DUBBO][QUERY_PLAN][REQUEST] {}", traceId, JSON.toJSONString(requestAO));
        checkParamService.checkInputParameters(requestAO);
        PlanResultRequest dataApiReq = new PlanResultRequest();
        dataApiReq.setTraceId(requestAO.getTraceId());
        dataApiReq.setUserId(requestAO.getUserId());
        dataApiReq.setCatalogId(requestAO.getCatalogId());
        dataApiReq.setStudyCode(requestAO.getStudyCode());
        dataApiReq.setBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode());
        try {
            CommonResponse<QueryLearningPlanResponse> response = new CommonResponse<QueryLearningPlanResponse>().success(requestAO.getTraceId());
            List<PlanResultData> dataApiRecords = AITeacherDataHub.getPlanResultService().query(dataApiReq);
            List<PlanResultDataAO> planResultDataAOS = PlanResultMapping.getInstance().convert(dataApiRecords);
            QueryLearningPlanResponse resp = new QueryLearningPlanResponse();
            resp.setPlanResultDatas(planResultDataAOS);
            log.info("[TraceID:{}] [DUBBO][QUERY_PLAN][RESPONSE] {}", traceId, JSON.toJSONString(requestAO));
            response.setData(resp);
            return response;
        } catch (Exception e) {
            log.error("[TraceID:{}] [DUBBO][QUERY_PLAN][ERROR]", traceId, e);
            String responseMessage = String.format("根据查询用户规划结果失败 %s_%s", requestAO.getUserId(), requestAO.getCatalogId());
            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), e.getMessage(), traceId);
        }
    }

    @Override
    @ServiceRequestMetrics(desc = "dubbo-放弃规划")
    @DubboTrace(desc = "dubbo-放弃规划")
    public CommonResponse<Serializable> deletePlan(DeleteLearningPlanRequest requestAO) {
        String traceId = requestAO.getTraceId();
        log.info("[TraceID:{}] [DUBBO][DELETE_PLAN][REQUEST] {}", traceId, JSON.toJSONString(requestAO));
        checkParamService.checkInputParameters(requestAO);
        // 删除规划结果，不需要删除课前聊一聊数据，因为有roundid可区分
        PlanResultRequest planResultRequest = new PlanResultRequest();
        planResultRequest.setTraceId(requestAO.getTraceId());
        planResultRequest.setUserId(requestAO.getUserId());
        planResultRequest.setCatalogId(requestAO.getCatalogId());
        planResultRequest.setStudyCode(BizConstants.StudyCodes.SYNC_TUTORING.getCode());
        planResultRequest.setBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode());
        try {
            log.debug("[TraceID:{}] [DUBBO][DELETE_PLAN][DELETE_PLAN_START] {}", traceId, JSON.toJSONString(planResultRequest));
            AITeacherDataHub.getPlanResultService().delete(planResultRequest); // 删除规划结果
            log.info("[TraceID:{}] [DUBBO][DELETE_PLAN][DELETE_PLAN_SUCCESS]", traceId);
            return new CommonResponse<Serializable>().success(requestAO.getTraceId());
        } catch (Exception e) {
            log.error("[TraceID:{}] [DUBBO][DELETE_PLAN][ERROR]", traceId, e);
            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), e.getMessage(), traceId);
        }
    }
}
