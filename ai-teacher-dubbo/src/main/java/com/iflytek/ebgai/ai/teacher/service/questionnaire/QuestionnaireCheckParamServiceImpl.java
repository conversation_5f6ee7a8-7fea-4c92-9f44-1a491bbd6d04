package com.iflytek.ebgai.ai.teacher.service.questionnaire;


import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.DeleteLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Auther: huizhang68
 * @date: 2025/6/11 10:57
 */
@Slf4j
@Service
public class QuestionnaireCheckParamServiceImpl implements QuestionnaireCheckParamService {

    @Override
    public void checkInputParameters(QueryQuestionnaireInferRequest requestAO) throws ParamValidateException {
        List<String> missingParams = new ArrayList<>();
        if (StringUtils.isEmpty(requestAO.getTraceId())) {
            missingParams.add("traceId");
        }
        if (StringUtils.isEmpty(requestAO.getUserId())) {
            missingParams.add("userId");
        }
        // 3. 如果有缺失参数则发送错误响应
        doCheck(requestAO.getTraceId(), StringUtils.EMPTY, Collections.emptyList(), missingParams);
    }

    @Override
    public void checkInputParameters(QueryLearningPlanRequest requestAO) throws ParamValidateException {
        List<String> missingParams = new ArrayList<>();
        if (StringUtils.isEmpty(requestAO.getTraceId())) {
            missingParams.add("traceId");
        }
        if (StringUtils.isEmpty(requestAO.getUserId())) {
            missingParams.add("userId");
        }
        if (StringUtils.isEmpty(requestAO.getCatalogId())) {
            missingParams.add("catalogId");
        }
        if (StringUtils.isEmpty(requestAO.getStudyCode())) {
            missingParams.add("studyCode");
        }
        // 3. 如果有缺失参数则发送错误响应
        doCheck(requestAO.getTraceId(), StringUtils.EMPTY, Collections.emptyList(), missingParams);
    }

    @Override
    public void checkInputParameters(DeleteLearningPlanRequest requestAO) throws ParamValidateException {
        List<String> missingParams = new ArrayList<>();
        if (StringUtils.isEmpty(requestAO.getTraceId())) {
            missingParams.add("traceId");
        }
        if (StringUtils.isEmpty(requestAO.getUserId())) {
            missingParams.add("userId");
        }
        if (StringUtils.isEmpty(requestAO.getCatalogId())) {
            missingParams.add("catalogId");
        }
        if (StringUtils.isEmpty(requestAO.getStudyCode())) {
            missingParams.add("studyCode");
        }
        // 3. 如果有缺失参数则发送错误响应
        doCheck(requestAO.getTraceId(), StringUtils.EMPTY, Collections.emptyList(), missingParams);
    }

    /**
     * 如果有缺失参数则发送错误响应
     */
    private void doCheck(String traceId, String errorInfo, List<String> missingHeaders, List<String> missingParams) throws ParamValidateException {
        // 3. 如果有缺失参数则发送错误响应
        if (CollectionUtils.isNotEmpty(missingHeaders) || CollectionUtils.isNotEmpty(missingParams)) {
            StringBuilder errorMsg = new StringBuilder("参数校验失败: ");
            if (CollectionUtils.isNotEmpty(missingHeaders)) {
                errorMsg.append("缺失请求头 - ").append(String.join(", ", missingHeaders));
            }
            if (CollectionUtils.isNotEmpty(missingParams)) {
                if (!missingHeaders.isEmpty()) {  // 添加花括号
                    errorMsg.append("; ");
                }
                errorMsg.append("缺失请求参数 - ").append(String.join(", ", missingParams));
            }
            errorInfo = errorMsg.toString();
        }

        if (StringUtils.isNotEmpty(errorInfo)) {
            log.info("[TraceID:{}] 参数校验失败:{}", traceId, errorInfo);
            throw new ParamValidateException(RetCode.INPUT_PARAMETERS_MISSING.getCode(), errorInfo, traceId);
        }
    }

}
