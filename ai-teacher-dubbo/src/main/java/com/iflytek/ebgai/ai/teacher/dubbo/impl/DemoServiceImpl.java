package com.iflytek.ebgai.ai.teacher.dubbo.impl;

import com.alibaba.dubbo.common.logger.Logger;
import com.alibaba.dubbo.common.logger.LoggerFactory;
import com.iflytek.ebgai.ai.teacher.dubbo.model.DemoModel;
import com.iflytek.ebgai.ai.teacher.dubbo.service.DemoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * Created by admin on 2017/12/26.
 */
@Slf4j
@Component("demoService")
public class DemoServiceImpl implements DemoService {
    Logger logger = LoggerFactory.getLogger(DemoServiceImpl.class);

    @Override
    public List<DemoModel> getDemoList() {
        DemoModel model = new DemoModel();
        model.setName("test");
        logger.info("test logger");
        return Collections.singletonList(model);
    }

    @Override
    public Boolean waitTime() {
        int mills = 0;
        boolean ret = true;
        try {

        } catch (Exception ex) {
            ret = false;
        }
        if (mills > 0) {
            try {
                Thread.sleep(mills);
            } catch (Exception ex) {
                logger.error("exception, ", ex);
            }
        }
        return ret;
    }
}
