package com.iflytek.ebgai.ai.teacher;

import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import skynet.boot.AppUtils;
import skynet.boot.annotation.EnableSkynetLogging;
import skynet.boot.annotation.EnableSkynetMetrics;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableSkynetMetrics
@EnableSkynetLogging
@EnableSkylineBrave
@SpringBootApplication(exclude = RedisAutoConfiguration.class)
@ImportResource({"classpath:spring-dubbo.xml", "classpath:epas-dubbo.xml"})
@ComponentScan(basePackages = {"com.iflytek.ebgai.ai.teacher.*", "com.iflytek.ebgai.ai.teacher.common"})
public class AiTeacherDubboApplication {
    public static void main(String[] args) {
        AppUtils.run(AiTeacherDubboApplication.class, args);
        log.info("================AI伴学服务dubbo启动成功============================");

    }

}
