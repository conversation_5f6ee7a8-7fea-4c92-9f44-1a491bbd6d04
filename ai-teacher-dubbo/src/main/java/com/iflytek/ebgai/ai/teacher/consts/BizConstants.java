package com.iflytek.ebgai.ai.teacher.consts;

/**
 * 业务常量定义类
 */
public class BizConstants {

    /**
     * 默认业务方代码
     */
    public static final String BIZ_CODE_DEFAULT = "ZSY_XXJ";

    /**
     * 业务功能枚举定义
     */
    public enum BizAction {
        AI_TUTORING_TEACHER_QUSNAIRE("问卷解释", "AI_TUTORING_TEACHER_QUSNAIRE"), AI_TUTORING_LEARNING_PLAN("学习规划", "AI_TUTORING_LEARNING_PLAN"), OTHERS("其他LLM能力", "OTHERS");

        private final String description;
        private final String code;

        BizAction(String description, String code) {
            this.description = description;
            this.code = code;
        }

        public String getDescription() {
            return description;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 引擎功能编码枚举定义
     */
    public enum FunctionCode {
        QUSNAIRE_RECNAIRE("问卷解释-问卷", "QUSNAIRE_RECNAIRE"), QUSNAIRE_USERPORTRAIT("问卷解释-用户画像计算", "QUSNAIRE_USERPORTRAIT"), QUSNAIRE_EXPLAIN("问卷解释-解释LLM", "QUSNAIRE_EXPLAIN"), LEARNINGPLAN_CHAT("学习规划-课前聊一聊LLM", "LEARNINGPLAN_CHAT"), LEARNINGPLAN_CLUSTERCHOICE("学习规划-知识簇选择LLM", "LEARNINGPLAN_CLUSTERCHOICE"), LEARNINGPLAN_PLAN("学习规划-知识点规划LLM", "LEARNINGPLAN_PLAN"), LEARNINGPLAN_CHANGE("学习规划-规划调整", "LEARNINGPLAN_CHANGE"), OTHERS("其他功能", "OTHERS");

        private final String description;
        private final String code;

        FunctionCode(String description, String code) {
            this.description = description;
            this.code = code;
        }

        public String getDescription() {
            return description;
        }

        public String getCode() {
            return code;
        }
    }

    public enum StudyCodes {
        SYNC_TUTORING("同步指导", "SYNC_TUTORING");


        private final String description;
        private final String code;

        StudyCodes(String description, String code) {
            this.description = description;
            this.code = code;
        }

        public String getDescription() {
            return description;
        }

        public String getCode() {
            return code;
        }
    }
}