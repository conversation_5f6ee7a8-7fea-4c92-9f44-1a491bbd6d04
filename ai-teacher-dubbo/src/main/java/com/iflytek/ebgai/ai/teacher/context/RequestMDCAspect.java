package com.iflytek.ebgai.ai.teacher.context;


import cn.hutool.core.lang.Pair;
import com.iflytek.ebgai.ai.teacher.annotation.DubboTrace;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import skynet.boot.AppContext;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 为了mdc增加的aop
 *
 * @Auther: huizhang68
 * @date: 2025/7/1 17:45
 */
@Slf4j
@Aspect
@Component
public class RequestMDCAspect {

    private final Map<String, Pair<Method, Method>> methodCache = new ConcurrentHashMap();
    private static final String DEFAULT_TAG_VALUE = "unknown";
    private final String ip;
    private final MeterRegistry meterRegistry;

    public RequestMDCAspect(MeterRegistry meterRegistry, AppContext appContext) {
        this.meterRegistry = meterRegistry;
        this.ip = StringUtils.isBlank(appContext.getIpEndpoint()) ? DEFAULT_TAG_VALUE : appContext.getIpEndpoint();
    }

    @Around("@annotation(dubboTrace)")
    public Object around(ProceedingJoinPoint joinPoint, DubboTrace dubboTrace) throws Throwable {
        log.debug("aspect metrics");
        try {
            Object[] args = joinPoint.getArgs();
            String traceId = String.valueOf(getValue(args[0]));
            MDC.put("TRACE_ID", traceId);
        } catch (Exception e) {
            log.error("RequestMetricsAspect error", e);
        }
        Object result = null;
        try {
            result = joinPoint.proceed();
            return result;
        } catch (Throwable t) {
            throw t;
        }
    }

    private Object getValue(Object arg) throws InvocationTargetException, IllegalAccessException {
        String key = String.format("%s.%s", arg.getClass(), "getTraceId");
        Pair<Method, Method> pair = methodCache.get(key);
        if (pair == null) {
            Method method = MethodUtils.getMatchingMethod(arg.getClass(), "getTraceId");
            pair = new Pair<>(method, method);
            methodCache.putIfAbsent(key, pair);
        }
        return pair.getKey() != null ? pair.getKey().invoke(arg) : null;
    }
}
