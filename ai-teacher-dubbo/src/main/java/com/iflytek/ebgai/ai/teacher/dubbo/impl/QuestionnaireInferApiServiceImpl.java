package com.iflytek.ebgai.ai.teacher.dubbo.impl;


import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.annotation.DubboTrace;
import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.dataapi.AITeacherDataHub;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.mapper.StorageInfoMapping;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferResponse;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.StorageInfo;
import com.iflytek.ebgai.ai.teacher.dubbo.service.QuestionnaireInferApiService;
import com.iflytek.ebgai.ai.teacher.service.questionnaire.QuestionnaireCheckParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author:huizhang68
 * @Date:2025/5/24
 */
@Slf4j
@Component("questionnaireInferApiService")
public class QuestionnaireInferApiServiceImpl implements QuestionnaireInferApiService {

    @Autowired
    private QuestionnaireCheckParamService checkParamService;

    @Override
    @DubboTrace(desc = "dubbo-查询用户问卷结果")
    @ServiceRequestMetrics(desc = "dubbo-查询用户问卷结果")
    public CommonResponse<QueryQuestionnaireInferResponse> query(QueryQuestionnaireInferRequest requestAO) {
        String traceId = requestAO.getTraceId();
        log.info("[TraceID:{}] [DUBBO][QUERY_QUESTIONNAIRE][REQUEST] {}", traceId, JSON.toJSONString(requestAO));
        checkParamService.checkInputParameters(requestAO);
        QueryQuestionnaireInferResponse res = new QueryQuestionnaireInferResponse();
        StorageInfoRequest dataApiReq = new StorageInfoRequest();
        dataApiReq.setTraceId(requestAO.getTraceId());
        dataApiReq.setUserId(requestAO.getUserId());
        dataApiReq.setFunctionCode(BizConstants.FunctionCode.QUSNAIRE_EXPLAIN.getCode());
        try {
            CommonResponse<QueryQuestionnaireInferResponse> response = new CommonResponse<QueryQuestionnaireInferResponse>().success(requestAO.getTraceId());
            StorageInfoData dataApiResp = AITeacherDataHub.getStorageInfoService().queryLatest(dataApiReq);
            if (Objects.isNull(dataApiResp)) {
                log.info("[TraceID:{}] [DUBBO][QUERY_QUESTIONNAIRE][EMPTY]", traceId);
                response.setTraceId(traceId);
                response.setData(res);
                return response;
            }
            StorageInfo storageInfo = StorageInfoMapping.getInstance().convert(dataApiResp);
            res.setStorageInfo(storageInfo);
            log.info("[TraceID:{}] [DUBBO][QUERY_QUESTIONNAIRE][RESPONSE] {}", traceId, JSON.toJSONString(res));
            response.setData(res);
            return response;
        } catch (Exception e) {
            log.error("[TraceID:{}] [DUBBO][QUERY_QUESTIONNAIRE][ERROR]", traceId, e);
            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), e.getMessage(), traceId);
        }
    }
}
