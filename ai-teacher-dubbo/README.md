# AI Teacher Dubbo Module

## 项目概述

### 项目名称
AI Teacher Dubbo Module - AI伴学服务Dubbo提供者模块

### 项目描述
AI Teacher Dubbo模块是AI伴学服务的分布式服务提供者，基于Apache Dubbo框架为外部系统（如学习机后端）提供标准化的RPC服务接口。该模块实现了问卷推理、学习规划等核心业务的Dubbo服务，支持EPAS和Zookeeper双注册中心，提供高可用的分布式服务能力。

### 版本信息
- **当前版本**: 1.0.2-SNAPSHOT
- **Java版本**: JDK 8
- **编码格式**: UTF-8
- **Dubbo版本**: 2.x

### 许可证信息
Apache License, Version 2.0

## 技术架构

### 整体技术架构描述
- **架构模式**: 分布式服务架构、微服务架构
- **设计模式**: 
  - 门面模式(Facade)：统一的服务接口入口
  - 适配器模式：数据模型转换和映射
  - 单例模式：映射器实例管理
  - 代理模式：Dubbo服务代理
- **核心技术栈**:
  - Apache Dubbo：分布式服务框架
  - Spring Boot：应用框架
  - EPAS：科大讯飞服务注册中心
  - Zookeeper：开源服务注册中心
  - Skynet Boot：科大讯飞内部框架

### 模块职责说明
- **服务提供层**: 实现Dubbo服务接口，提供RPC调用能力
- **数据映射层**: 负责内部数据模型与外部接口模型的转换
- **注册中心集成**: 支持EPAS和Zookeeper双注册中心
- **监控追踪层**: 集成链路追踪和性能监控
- **配置管理层**: Dubbo服务配置和注册中心配置

### 架构图

```mermaid
graph TB
    A[AI Teacher Dubbo Module] --> B[Service Provider Layer]
    A --> C[Mapping Layer]
    A --> D[Registry Layer]
    A --> E[Configuration Layer]
    
    B --> B1[QuestionnaireInferApiServiceImpl]
    B --> B2[LearningPlanApiServiceImpl]
    B --> B3[DemoServiceImpl]
    
    C --> C1[StorageInfoMapping]
    C --> C2[PlanResultMapping]
    
    D --> D1[EPAS Registry]
    D --> D2[Zookeeper Registry]
    
    E --> E1[spring-dubbo.xml]
    E --> E2[epas-dubbo.xml]
    E --> E3[application.properties]
    
    B --> F[External Systems]
    F --> F1[Learning Machine Backend]
    F --> F2[Third-party Systems]
    
    B --> G[Internal Services]
    G --> G1[DataAPI Module]
    G --> G2[Engine Module]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#fce4ec
    style F fill:#f1f8e9
    style G fill:#e8f5e8
```

## 主要功能

### 功能模块列表

#### 1. 问卷推理服务 (QuestionnaireInferApiServiceImpl)
- **核心功能**: 为学习机后端提供问卷结果查询服务
- **服务接口**: QuestionnaireInferApiService
- **业务场景**: 学习机获取用户问卷分析结果和解释
- **关键方法**:
  - `query(QueryQuestionnaireInferRequest)`: 查询用户最新问卷结果
- **数据来源**: 通过DataAPI查询StorageInfo存储信息
- **技术特点**: 
  - 支持链路追踪(@DubboTrace)
  - 集成性能监控(@ServiceRequestMetrics)
  - 统一异常处理和参数校验

#### 2. 学习规划服务 (LearningPlanApiServiceImpl)
- **核心功能**: 提供学习规划查询和管理服务
- **服务接口**: LearningPlanApiService
- **业务场景**: 个性化学习计划查询、规划放弃操作
- **关键方法**:
  - `queryPlan(QueryLearningPlanRequest)`: 查询用户学习规划
  - `deletePlan(DeleteLearningPlanRequest)`: 放弃学习规划
- **数据来源**: 通过DataAPI查询PlanResult规划结果
- **技术特点**:
  - 支持复合主键查询(userId+catalogId+functionCode)
  - 数据模型自动映射转换
  - 逻辑删除操作

#### 3. 演示服务 (DemoServiceImpl)
- **核心功能**: 提供系统演示和测试功能
- **服务接口**: DemoService
- **业务场景**: 系统功能演示、接口连通性测试
- **关键方法**:
  - `getDemoList()`: 获取演示数据列表
  - `waitTime()`: 等待时间测试
- **技术特点**: 简单的测试服务，用于验证Dubbo连接

#### 4. 数据映射服务 (Mapping Layer)
- **核心功能**: 内部数据模型与外部接口模型转换
- **映射器类**:
  - `StorageInfoMapping`: 存储信息映射
  - `PlanResultMapping`: 规划结果映射
- **技术特点**:
  - 单例模式管理映射器实例
  - 使用HuTool工具进行属性复制
  - 支持集合和单对象映射

### 关键技术点
- **双注册中心**: 同时支持EPAS和Zookeeper注册中心
- **服务治理**: 超时控制、负载均衡、容错机制
- **链路追踪**: 自定义@DubboTrace注解支持
- **性能监控**: 集成Skynet监控体系
- **参数校验**: 统一的参数校验机制
- **异常处理**: 标准化的异常处理和错误码返回

## 业务处理逻辑分析

### 核心业务逻辑

#### 1. 问卷推理查询流程
```java
// QuestionnaireInferApiServiceImpl核心逻辑
@Override
public CommonResponse<QueryQuestionnaireInferResponse> query(QueryQuestionnaireInferRequest requestAO) {
    // 参数校验 → 数据查询 → 结果映射 → 响应返回
    StorageInfoData dataApiResp = AITeacherDataHub.getStorageInfoService().queryLatest(dataApiReq);
    QueryQuestionnaireInferResponse response = StorageInfoMapping.getInstance().convert(dataApiResp);
    return new CommonResponse<>().success(traceId).setData(response);
}
```
- **数据流转**: Dubbo请求 → 参数校验 → DataAPI查询 → 数据映射 → 响应返回
- **条件判断**: 根据userId和functionCode查询最新问卷解释结果
- **异常处理**: 数据为空时返回空结果，异常时返回错误码

#### 2. 学习规划查询流程
```java
// LearningPlanApiServiceImpl核心逻辑
@Override
public CommonResponse<QueryLearningPlanResponse> queryPlan(QueryLearningPlanRequest requestAO) {
    // 构建查询条件 → 数据查询 → 批量映射 → 响应封装
    List<PlanResultData> dataApiResp = AITeacherDataHub.getPlanResultService().query(dataApiReq);
    List<PlanResultDataAO> planResults = PlanResultMapping.getInstance().convert(dataApiResp);
}
```
- **数据流转**: 请求参数 → 复合主键构建 → 数据库查询 → 批量映射 → 结果返回
- **条件判断**: 根据学习场景和目录ID筛选规划结果
- **循环结构**: 遍历查询结果进行数据模型转换

#### 3. 数据映射处理流程
```java
// PlanResultMapping单例映射器
public List<PlanResultDataAO> convert(List<PlanResultData> sourceList) {
    // 空值检查 → 循环转换 → 结果收集
    for (PlanResultData source : sourceList) {
        PlanResultDataAO target = convert(source);
        targets.add(target);
    }
}
```
- **数据流转**: 内部数据模型 → 属性复制 → 外部接口模型
- **循环结构**: 批量数据转换处理
- **异常处理**: 空值安全处理

### 关键算法说明

#### 1. 双重检查锁定单例算法
```java
// PlanResultMapping单例实现
public static PlanResultMapping getInstance() {
    if (instance == null) {
        synchronized (PlanResultMapping.class) {
            if (instance == null) {
                instance = new PlanResultMapping();
            }
        }
    }
    return instance;
}
```
- **业务场景**: 映射器实例的线程安全管理
- **算法实现**: 双重检查锁定模式
- **优化点**: 减少同步开销，确保线程安全

#### 2. 复合主键构建算法
- **业务场景**: 学习规划数据的唯一标识
- **算法实现**: userId + catalogId + functionCode组合
- **优化点**: 确保数据唯一性和查询效率

#### 3. 批量数据映射算法
- **业务场景**: 大量数据的高效转换
- **算法实现**: 流式处理 + 属性复制
- **优化点**: 使用HuTool工具提高映射性能

## 主要对外接口

### 接口类型说明
- **Dubbo RPC接口**: 基于Dubbo协议的远程过程调用
- **注册方式**: EPAS + Zookeeper双注册中心
- **序列化协议**: Java原生序列化
- **超时设置**: 30秒
- **负载均衡**: 默认随机负载均衡

### 接口详细信息

| 服务接口 | 方法名称 | 请求参数 | 返回值 | 功能描述 | 业务场景 |
|---------|---------|---------|--------|---------|---------|
| QuestionnaireInferApiService | query | QueryQuestionnaireInferRequest | CommonResponse<QueryQuestionnaireInferResponse> | 查询用户问卷结果 | 学习机获取问卷分析 |
| LearningPlanApiService | queryPlan | QueryLearningPlanRequest | CommonResponse<QueryLearningPlanResponse> | 查询学习规划 | 获取个性化学习计划 |
| LearningPlanApiService | deletePlan | DeleteLearningPlanRequest | CommonResponse<Serializable> | 放弃学习规划 | 用户取消学习计划 |
| DemoService | getDemoList | 无 | List<DemoModel> | 获取演示列表 | 系统功能演示 |
| DemoService | waitTime | 无 | Boolean | 等待时间测试 | 接口性能测试 |

### Dubbo服务配置

#### 服务提供者配置
```xml
<!-- spring-dubbo.xml -->
<dubbo:application name="ai-teacher-dubbo"/>
<dubbo:protocol name="dubbo" port="${dubbo.service-port}"/>
<dubbo:provider timeout="30000"/>

<!-- EPAS注册中心 -->
<dubbo:registry id="epas" protocol="epas" address="epasConfig"/>

<!-- Zookeeper注册中心 -->
<dubbo:registry id="app" protocol="zookeeper" address="${application.dubbo.zookeeper.address}"/>

<!-- 服务暴露 -->
<dubbo:service registry="epas" ref="questionnaireInferApiService"
               interface="com.iflytek.ebgai.ai.teacher.dubbo.service.QuestionnaireInferApiService"/>
<dubbo:service registry="epas" ref="learningPlanApiService"
               interface="com.iflytek.ebgai.ai.teacher.dubbo.service.LearningPlanApiService"/>
```

#### EPAS配置
```xml
<!-- epas-dubbo.xml -->
<bean id="epasConfig" class="com.iflytek.edu.epas.dubbo.config.EpasConfig">
    <property name="appKey" value="${application.epas.appKey}"/>
    <property name="appSecret" value="${application.epas.appSecret}"/>
    <property name="addrServerUrl" value="${application.epas.addrServerUrl}"/>
</bean>
```

### 接口调用示例

#### 消费者配置
```xml
<!-- 引用问卷服务 -->
<dubbo:reference id="questionnaireInferApiService" 
                 interface="com.iflytek.ebgai.ai.teacher.dubbo.service.QuestionnaireInferApiService"/>

<!-- 引用规划服务 -->
<dubbo:reference id="learningPlanApiService"
                 interface="com.iflytek.ebgai.ai.teacher.dubbo.service.LearningPlanApiService"/>
```

#### Java调用示例
```java
// 问卷查询
QueryQuestionnaireInferRequest request = new QueryQuestionnaireInferRequest();
request.setUserId("user123");
request.setTraceId(UUID.randomUUID().toString());

CommonResponse<QueryQuestionnaireInferResponse> response = 
    questionnaireInferApiService.query(request);

// 学习规划查询
QueryLearningPlanRequest planRequest = new QueryLearningPlanRequest();
planRequest.setUserId("user123");
planRequest.setCatalogId("catalog456");
planRequest.setStudyCode("SYNC_TUTORING");

CommonResponse<QueryLearningPlanResponse> planResponse = 
    learningPlanApiService.queryPlan(planRequest);
```

## 系统配置

### 运行环境要求
- **JDK版本**: JDK 8+
- **操作系统**: 支持Windows、Linux、macOS
- **内存要求**: 最小1GB，推荐2GB+
- **网络要求**: 支持Dubbo协议通信
- **注册中心**: EPAS + Zookeeper

### 配置文件说明

#### 主要配置参数
```properties
# 应用基本配置
spring.application.name=ai-teacher-dubbo
server.port=32400
server.servlet.context-path=/ai-teacher-dubbo

# Dubbo配置
dubbo.service-port=35880
application.dubbo.zookeeper.address=${IP}:2181

# EPAS配置
application.epas.appKey=ai-teacher-dubbo
application.epas.appSecret=924aeaf3fc5b06b3
application.epas.addrServerUrl=http://pre.epas.changyan.com/address

# 监控配置
management.endpoints.web.exposure.include=*
auth.whitelist.urls[0]=/ai-teacher-dubbo/actuator/prometheus
auth.whitelist.urls[1]=/ai-teacher-dubbo/actuator/health

# 链路追踪配置
skyline.brave.enabled=true
skyline.brave.trace-debug-enabled=true
skyline.brave.aop-enabled=true
```

#### 数据源配置
```properties
# MongoDB配置
spring.data.mongodb.uri=********************************:port/database

# Zookeeper配置
spring.cloud.zookeeper.connect-string=***********:2181
```

### 启动和部署

#### 本地启动
```bash
# 编译打包
mvn clean package

# 启动应用
java -jar ai-teacher-dubbo-1.0.2-SNAPSHOT.jar

# 或使用Spring Boot Maven插件
mvn spring-boot:run
```

#### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY ai-teacher-dubbo-1.0.2-SNAPSHOT.jar app.jar
EXPOSE 32400 35880
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 快速开始

### 环境准备
1. 安装JDK 8+
2. 配置Zookeeper注册中心
3. 配置EPAS注册中心（可选）
4. 准备MongoDB数据库

### 项目启动
1. **克隆项目**
```bash
git clone <repository-url>
cd ai-teacher/ai-teacher-dubbo
```

2. **配置修改**
```properties
# 修改application.properties
application.dubbo.zookeeper.address=localhost:2181
spring.data.mongodb.uri=mongodb://localhost:27017/ai-teacher
```

3. **启动应用**
```bash
mvn spring-boot:run
```

4. **验证启动**
```bash
# 检查健康状态
curl http://localhost:32400/ai-teacher-dubbo/actuator/health

# 检查Dubbo服务注册
# 查看Zookeeper中的服务注册信息
```

### 服务测试

#### 通过HTTP接口测试
```bash
# 测试问卷查询接口
curl -X POST http://localhost:32400/ai-teacher-dubbo/api/v1/dubbo/queryQuestionnaire \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "traceId": "test-trace-123"
  }'
```

#### 通过Dubbo客户端测试
```java
// 配置Dubbo消费者
@Reference
private QuestionnaireInferApiService questionnaireService;

// 调用服务
QueryQuestionnaireInferRequest request = new QueryQuestionnaireInferRequest();
request.setUserId("user123");
CommonResponse<QueryQuestionnaireInferResponse> response = questionnaireService.query(request);
```

## 开发指南

### 代码结构
```
ai-teacher-dubbo/
├── src/main/java/
│   └── com/iflytek/ebgai/ai/teacher/
│       ├── AiTeacherDubboApplication.java   # 主启动类
│       ├── annotation/                      # 自定义注解
│       ├── controller/                      # HTTP控制器
│       ├── dubbo/                          # Dubbo服务实现
│       │   ├── impl/                       # 服务实现类
│       │   └── mapper/                     # 数据映射器
│       └── config/                         # 配置类
├── src/main/resources/
│   ├── application.properties              # 主配置文件
│   ├── spring-dubbo.xml                   # Dubbo服务配置
│   └── epas-dubbo.xml                     # EPAS配置
└── pom.xml                                # Maven配置
```

### 开发规范
- **服务实现**: 实现API模块定义的接口契约
- **数据映射**: 使用单例模式管理映射器实例
- **异常处理**: 统一异常处理和错误码返回
- **监控集成**: 在服务方法上添加监控注解
- **链路追踪**: 使用@DubboTrace注解支持链路追踪
- **参数校验**: 统一的参数校验机制

### 扩展指南
1. **新增Dubbo服务**
   - 在API模块定义服务接口
   - 在Dubbo模块实现服务接口
   - 在spring-dubbo.xml中配置服务暴露
   - 创建对应的数据映射器

2. **新增数据映射器**
   - 继承单例模式创建映射器类
   - 实现数据模型转换方法
   - 处理空值和异常情况

### 测试指南
```bash
# 运行单元测试
mvn test

# 运行Dubbo集成测试
mvn test -Dtest=*DubboTest

# 编译打包
mvn clean package
```

### 监控和运维
- **服务监控**: 通过Actuator端点监控服务状态
- **链路追踪**: 通过Skyline Brave进行链路追踪
- **性能监控**: 通过Prometheus收集性能指标
- **日志管理**: 统一的日志格式和链路ID
