<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.ebg.ai</groupId>
        <artifactId>ai-teacher</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher-dubbo</artifactId>
    <version>1.0.2-SNAPSHOT</version>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>
    <properties>
        <epas-dubbo.version>1.0.11</epas-dubbo.version>
        <super-diamond-client.version>1.0.1061</super-diamond-client.version>
        <java.version>8</java.version>
        <epas-getaway-sdk.version>1.0.4</epas-getaway-sdk.version>
        <epas.config.version>1.1.0.1019</epas.config.version>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <skipTests>true</skipTests>
        <maven.compiler.source>8</maven.compiler.source>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.iflytek.ebg.ai</groupId>
            <artifactId>ai-teacher-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.ebg.ai</groupId>
            <artifactId>ai-teacher-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.ebg.ai</groupId>
            <artifactId>ai-teacher-dataapi</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>skylab-core-data</artifactId>
                    <groupId>com.iflytek.skylab</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>skylab-core-dataapi</artifactId>
                    <groupId>com.iflytek.skylab</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skyline</groupId>
            <artifactId>skyline-context</artifactId>
            <version>3.0.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.edu</groupId>
            <artifactId>epas-dubbo</artifactId>
            <version>${epas-dubbo.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>libthrift</artifactId>
                    <groupId>org.apache.thrift</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.21.0-GA</version>
        </dependency>
        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
            <version>0.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.diamond</groupId>
            <artifactId>super-diamond-client</artifactId>
            <version>${super-diamond-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.20</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.25.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>4.2.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.fsp.shield</groupId>
            <artifactId>api-gw-sdk-java</artifactId>
            <version>${epas-getaway-sdk.version}</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}-${project.version}-${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
