<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.ebg.ai</groupId>
        <artifactId>ai-teacher</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai-teacher-dubbo</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>8</java.version>
        <skipTests>true</skipTests>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <epas-getaway-sdk.version>1.0.4</epas-getaway-sdk.version>
        <epas-dubbo.version>1.0.11</epas-dubbo.version>
        <super-diamond-client.version>1.0.1061</super-diamond-client.version>
        <epas.config.version>1.1.0.1019</epas.config.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.iflytek.ebg.ai</groupId>
            <artifactId>ai-teacher-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.ebg.ai</groupId>
            <artifactId>ai-teacher-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.ebg.ai</groupId>
            <artifactId>ai-teacher-dataapi</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.iflytek.skylab</groupId>
                    <artifactId>skylab-core-data</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iflytek.skylab</groupId>
                    <artifactId>skylab-core-dataapi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skyline</groupId>
            <artifactId>skyline-context</artifactId>
            <version>3.0.7-SNAPSHOT</version>
        </dependency>

        <!--服务注册到epas-->
        <dependency>
            <groupId>com.iflytek.edu</groupId>
            <artifactId>epas-dubbo</artifactId>
            <version>${epas-dubbo.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.thrift</groupId>
                    <artifactId>libthrift</artifactId>
                </exclusion>
            </exclusions>
            <!--            <scope>compile</scope>-->
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.21.0-GA</version>
        </dependency>

        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
            <version>0.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.diamond</groupId>
            <artifactId>super-diamond-client</artifactId>
            <version>${super-diamond-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-metrics</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
         </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.25.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>4.2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
            <version>4.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.iflytek.fsp.shield</groupId>
            <artifactId>api-gw-sdk-java</artifactId>
            <version>${epas-getaway-sdk.version}</version>
        </dependency>

    </dependencies>
    <build>
        <finalName>${project.artifactId}-${project.version}-${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>