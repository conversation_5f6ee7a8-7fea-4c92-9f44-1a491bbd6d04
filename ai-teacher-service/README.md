# AI Teacher Service Module

## 项目概述

### 项目名称
AI Teacher Service Module - AI伴学服务主应用模块

### 项目描述
AI Teacher Service模块是AI伴学服务的主应用程序，提供了完整的Web服务能力和业务逻辑实现。该模块集成了问卷管理、学习规划、知识簇选择等核心业务功能，通过RESTful API和SSE流式接口为前端和第三方系统提供服务，是整个AI伴学系统的核心服务入口。

### 版本信息
- **当前版本**: 1.0.2-SNAPSHOT
- **Java版本**: JDK 8
- **编码格式**: UTF-8
- **Spring Boot版本**: 基于skynet-boot-starter-parent 4.0.18

### 许可证信息
Apache License, Version 2.0

## 技术架构

### 整体技术架构描述
- **架构模式**: 分层架构、微服务架构、响应式编程
- **设计模式**: 
  - MVC模式：Controller-Service-Repository分层
  - 门面模式：统一的业务服务入口
  - 策略模式：不同业务场景的处理策略
  - 观察者模式：SSE事件驱动机制
- **核心技术栈**:
  - Spring Boot：应用框架和自动配置
  - Spring WebMVC：Web层框架
  - Server-Sent Events (SSE)：实时流式响应
  - Skynet Boot：科大讯飞内部框架
  - Swagger：API文档生成
  - Dubbo：分布式服务调用

### 模块职责说明
- **Web控制层**: 处理HTTP请求和响应，参数校验
- **业务服务层**: 核心业务逻辑处理和编排
- **数据访问层**: 通过DataAPI模块访问数据
- **引擎集成层**: 集成AI引擎提供智能能力
- **配置管理层**: 应用配置和环境管理
- **监控追踪层**: 性能监控和链路追踪

### 架构图

```mermaid
graph TB
    A[AI Teacher Service Module] --> B[Web Layer]
    A --> C[Service Layer]
    A --> D[Integration Layer]
    A --> E[Configuration Layer]
    
    B --> B1[QuestionnaireController]
    B --> B2[LearnPlanController]
    B --> B3[KnowledgeClusterSelectController]
    B --> B4[DubboController]
    
    C --> C1[QuestionnaireBizService]
    C --> C2[PlanBizService]
    C --> C3[CheckParamService]
    
    D --> D1[AI Engine Integration]
    D --> D2[DataAPI Integration]
    D --> D3[External Service Integration]
    
    E --> E1[ThreadPoolConfig]
    E --> E2[WhitelistConfig]
    E --> E3[CommonProperties]
    
    B --> F[SSE Stream Processing]
    F --> F1[SseEmitter]
    F --> F2[Async Thread Pool]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

## 主要功能

### 功能模块列表

#### 1. 问卷管理模块 (QuestionnaireController)
- **核心功能**: 问卷获取、用户回答存储、问卷结果解释
- **接口路径**: `/api/v1/questionnaire`
- **业务场景**: 学习前问卷调研、用户画像分析
- **关键接口**:
  - `POST /`: 获取问卷题目
  - `POST /answer`: 存储用户回答
  - `POST /explain`: 问卷结果解释
- **技术特点**: 同步处理、参数校验、链路追踪

#### 2. 学习规划模块 (LearnPlanController)
- **核心功能**: 个性化学习路径规划生成
- **接口路径**: `/api/v1/plan`
- **业务场景**: 智能学习计划制定、个性化推荐
- **关键接口**:
  - `POST /sse/learn`: SSE流式学习规划生成
- **技术特点**: 
  - SSE流式响应处理
  - 异步线程池处理
  - 实时进度反馈
  - 自动资源管理

#### 3. 知识簇选择模块 (KnowledgeClusterSelectController)
- **核心功能**: 智能知识点聚类和推荐
- **接口路径**: `/api/v1/plan/knowledge`
- **业务场景**: 知识点推荐、学习内容筛选
- **关键接口**:
  - `POST /cluster`: 知识簇智能选择
- **技术特点**: 同步处理、AI引擎集成

#### 4. Dubbo服务模块 (DubboController)
- **核心功能**: 为学习机等外部系统提供Dubbo接口
- **接口路径**: `/api/v1/dubbo`
- **业务场景**: 系统间服务调用、数据查询
- **关键接口**:
  - `POST /queryQuestionnaire`: 查询问卷结果
  - `POST /queryPlan`: 查询学习规划
  - `POST /deletePlan`: 删除学习规划
- **技术特点**: RPC调用、服务治理

### 关键技术点
- **SSE流式处理**: 支持实时数据推送和长连接
- **异步线程池**: 优化并发处理能力
- **参数校验**: 统一的请求参数验证机制
- **链路追踪**: 全链路监控和问题定位
- **异常处理**: 统一异常处理和错误响应
- **配置管理**: 灵活的配置参数管理
- **白名单机制**: 接口访问权限控制

## 业务处理逻辑分析

### 核心业务逻辑

#### 1. SSE流式学习规划处理流程
```java
// LearnPlanController核心流式处理逻辑
@PostMapping(value = "/sse/learn", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
public SseEmitter learnPlan(@RequestBody XxjLlmRequest xxjLlmRequest, HttpServletRequest request) {
    SseEmitter emitter = new SseEmitter(timeOut);
    // 参数校验 → 引擎调用 → 流式响应处理 → 异步发送
}
```
- **数据流转**: 请求接收 → 参数校验 → AI引擎调用 → 流式数据处理 → SSE推送
- **异步处理**: 使用线程池异步处理SSE事件发送
- **资源管理**: 自动管理SseEmitter生命周期
- **异常处理**: 流处理异常自动关闭连接

#### 2. 问卷业务处理流程
```java
// QuestionnaireBizServiceImpl核心业务逻辑
public QuestionnaireResponseAO getQuestionnaire(QuestionnaireRequestAO requestAO, Map<String, String> headers) {
    // 参数转换 → 引擎调用 → 结果映射 → 响应返回
}
```
- **数据流转**: 前端请求 → 参数映射 → 引擎处理 → 结果转换 → 响应返回
- **条件判断**: 根据问卷索引和用户状态返回不同题目
- **异常处理**: 引擎调用失败时的降级处理

#### 3. 知识簇选择处理流程
```java
// KnowledgeClusterSelectController核心逻辑
public CommonResponse<KnowledgeClusterLlmResource> knowledgeClusterSelect(XxjLlmRequest xxjLlmRequest) {
    // 参数校验 → 引擎调用 → 结果封装
}
```
- **数据流转**: 请求参数 → AI引擎分析 → 知识簇推荐 → 结果返回
- **算法集成**: 集成AI引擎的知识图谱分析能力

### 关键算法说明

#### 1. SSE流式数据处理算法
- **业务场景**: 实时AI响应流式推送
- **算法实现**: 基于SseEmitter的事件驱动机制
- **优化点**: 异步处理提高并发能力，自动超时管理

#### 2. 线程池调度算法
```java
// ThreadPoolConfig线程池配置
new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 60L, TimeUnit.SECONDS,
    new LinkedBlockingQueue<>(1000), customThreadFactory, CallerRunsPolicy);
```
- **业务场景**: SSE事件异步发送
- **算法实现**: 自定义线程池配置，I/O密集型优化
- **优化点**: 合理的核心线程数和队列大小配置

#### 3. 参数校验算法
- **业务场景**: 统一的请求参数验证
- **算法实现**: 基于注解的声明式校验
- **优化点**: 早期参数验证减少无效处理

## 主要对外接口

### 接口类型说明
- **RESTful API**: 标准的HTTP REST接口
- **SSE流式接口**: Server-Sent Events实时推送接口
- **Dubbo RPC接口**: 分布式服务调用接口
- **监控接口**: 健康检查和指标监控接口

### 接口详细信息

| 模块 | 接口路径 | 方法 | 功能描述 | 响应类型 | 特殊说明 |
|------|---------|------|---------|---------|---------|
| 问卷管理 | `/api/v1/questionnaire` | POST | 获取问卷题目 | JSON | 同步接口 |
| 问卷管理 | `/api/v1/questionnaire/answer` | POST | 存储用户回答 | JSON | 同步接口 |
| 问卷管理 | `/api/v1/questionnaire/explain` | POST | 问卷结果解释 | JSON | 同步接口 |
| 学习规划 | `/api/v1/plan/sse/learn` | POST | 学习规划生成 | SSE流 | 流式接口 |
| 知识簇选择 | `/api/v1/plan/knowledge/cluster` | POST | 知识簇选择 | JSON | 同步接口 |
| Dubbo服务 | `/api/v1/dubbo/queryQuestionnaire` | POST | 查询问卷结果 | JSON | RPC接口 |
| Dubbo服务 | `/api/v1/dubbo/queryPlan` | POST | 查询学习规划 | JSON | RPC接口 |
| Dubbo服务 | `/api/v1/dubbo/deletePlan` | POST | 删除学习规划 | JSON | RPC接口 |

### SSE流式接口特殊说明

#### 学习规划SSE接口
```javascript
// 前端SSE连接示例
const eventSource = new EventSource('/api/v1/plan/sse/learn', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'x-trace-id': 'trace-123'
    },
    body: JSON.stringify(requestData)
});

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    // 处理流式响应数据
};
```

#### 响应数据格式
```json
{
    "code": "000000",
    "message": "success",
    "traceId": "trace-123",
    "data": {
        "content": "学习规划内容",
        "progress": 50,
        "isComplete": false
    }
}
```

## 系统配置

### 运行环境要求
- **JDK版本**: JDK 8+
- **操作系统**: 支持Windows、Linux、macOS
- **内存要求**: 最小2GB，推荐4GB+
- **CPU要求**: 多核CPU，推荐4核+
- **网络要求**: 支持HTTP/HTTPS协议

### 配置文件说明

#### 主要配置参数
```properties
# 服务基本配置
spring.application.name=ai-teacher-service
server.port=32399
server.servlet.context-path=/ai-teacher-service

# SSE线程池配置
banxue.sse.core.pool.size=10
banxue.sse.max.pool.size=50

# 白名单配置
auth.whitelist.urls[0]=/ai-teacher-service/actuator/prometheus
auth.whitelist.urls[1]=/ai-teacher-service/actuator/health

# 监控配置
management.endpoints.web.exposure.include=*
management.metrics.export.prometheus.enabled=true

# Dubbo配置
dubbo.service-port=35880
application.dubbo.zookeeper.address=${IP}:2181
```

#### 数据库配置
```properties
# MongoDB配置
spring.data.mongodb.uri=********************************:port/database

# 图数据库配置
skylab.data.api.graph.hosts=***********:9669
skylab.data.api.graph.username=root
skylab.data.api.graph.password=nebula

# Elasticsearch配置
zion.es-host=***********:9200,***********:9200,***********:9200
zion.es-user-name=elastic
```

### 启动和部署

#### 本地启动
```bash
# 编译打包
mvn clean package

# 启动应用
java -jar ai-teacher-service-1.0.2-SNAPSHOT.jar

# 或使用Spring Boot Maven插件
mvn spring-boot:run
```

#### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY ai-teacher-service-1.0.2-SNAPSHOT.jar app.jar
EXPOSE 32399
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 快速开始

### 环境准备
1. 安装JDK 8+
2. 安装Maven 3.6+
3. 配置数据库连接
4. 准备AI引擎环境

### 项目启动
1. **克隆项目**
```bash
git clone <repository-url>
cd ai-teacher/ai-teacher-service
```

2. **配置文件修改**
```properties
# 修改application.properties中的数据库连接
spring.data.mongodb.uri=mongodb://localhost:27017/ai-teacher
```

3. **启动应用**
```bash
mvn spring-boot:run
```

4. **验证启动**
```bash
# 检查健康状态
curl http://localhost:32399/ai-teacher-service/actuator/health

# 查看API文档
http://localhost:32399/ai-teacher-service/swagger-ui.html
```

### 接口测试

#### 问卷获取接口测试
```bash
curl -X POST http://localhost:32399/ai-teacher-service/api/v1/questionnaire \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test-123" \
  -d '{
    "userId": "user123",
    "index": 1
  }'
```

#### SSE学习规划接口测试
```bash
curl -X POST http://localhost:32399/ai-teacher-service/api/v1/plan/sse/learn \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -H "x-trace-id: test-456" \
  -d '{
    "userId": "user123",
    "subject": "数学",
    "grade": "初一"
  }'
```

## 开发指南

### 代码结构
```
ai-teacher-service/
├── src/main/java/
│   └── com/iflytek/ebgai/ai/teacher/
│       ├── AiTeacherServiceApplication.java  # 主启动类
│       ├── config/                          # 配置类
│       ├── controller/                      # 控制器层
│       ├── service/                         # 服务层
│       ├── entity/                          # 实体类
│       └── common/                          # 公共组件
├── src/main/resources/
│   ├── application.properties               # 主配置文件
│   ├── spring-dubbo.xml                    # Dubbo配置
│   └── logback-spring.xml                  # 日志配置
└── pom.xml                                 # Maven配置
```

### 开发规范
- **控制器设计**: 遵循RESTful设计原则
- **参数校验**: 使用统一的参数校验服务
- **异常处理**: 使用全局异常处理器
- **日志记录**: 统一的日志格式和链路追踪
- **监控集成**: 在关键方法上添加监控注解
- **代码风格**: 遵循阿里巴巴Java开发手册

### 测试指南
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn test -Dtest=*IntegrationTest

# 生成测试报告
mvn surefire-report:report
```

### 性能优化建议
- **线程池调优**: 根据业务特点调整SSE线程池参数
- **连接池优化**: 合理配置数据库连接池
- **缓存策略**: 对热点数据进行缓存
- **异步处理**: 使用异步处理提高响应速度
- **监控告警**: 建立完善的监控和告警机制
