#------------------------------------------------------------------------------------------------------
# Basic Application Settings
#------------------------------------------------------------------------------------------------------
spring.application.name=ai-teacher
server.port=32388
server.servlet.context-path=/ai-teacher
#------------------------------------------------------------------------------------------------------
# System Parameters
#------------------------------------------------------------------------------------------------------
IP=***********
skynet.api.swagger2.enabled=false
#------------------------------------------------------------------------------------------------------
# Auth Configuration
#------------------------------------------------------------------------------------------------------
recommend.auth.url=https://pre-assistant.eduaiplat.com/auth/v3/token_check
recommend.auth.token.url=https://pre-assistant.eduaiplat.com/auth/v3/token
recommend.auth.enable=true
auth.whitelist.urls[0]=/ai-teacher/actuator/prometheus
auth.whitelist.urls[1]=/ai-teacher/actuator/health
auth.whitelist.urls[2]=/ai-teacher/actuator/info
#------------------------------------------------------------------------------------------------------
# Encoding Settings
#------------------------------------------------------------------------------------------------------
server.tomcat.uri-encoding=UTF-8
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
#------------------------------------------------------------------------------------------------------
# AI Agent Configuration
#------------------------------------------------------------------------------------------------------
chat.app.id=xxjcpx-xcjzx
chat.app.agent.id=682557525cf2002c9fb76e1d
chat.sse.url=https://ai-thor-prelt.ceshiservice.cn/ai-thor-dispatcher/api/v1/workflow/chat
chat.connect.timeout=5000
chat.socket.timeout=30000
chat.http.retry.time=2
banxue.engine.deepseekr1.workflowid=682554b25cf2002c9fb76e1a
banxue.engine.deepseekrv3.workflowid=682554ee5cf2002c9fb76e1c
banxue.engine.shortcutdata.shortcuttype=0
#banxue.engine.body.agentId = 682557525cf2002c9fb76e1d
banxue.engine.body.debugmode=true
#------------------------------------------------------------------------------------------------------
# Logging Configuration
#------------------------------------------------------------------------------------------------------
logging.level.ROOT=INFO
logging.level.com.iflytek.skyline=INFO
logging.level.com.iflytek.skylab=INFO
logging.level.com.iflytek.hy=INFO
logging.level.com.iflytek.rec.teacher=INFO
logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{TRACE_ID}]){cyan}%clr(:){faint} %m%n
#------------------------------------------------------------------------------------------------------
# MongoDB Configuration
#------------------------------------------------------------------------------------------------------
spring.data.mongodb.uri=*************************************************************************************************************************
#------------------------------------------------------------------------------------------------------
# Graph Configuration
#------------------------------------------------------------------------------------------------------
skylab.data.api.graph.hosts=***********:9669
skylab.data.api.graph.username=root
skylab.data.api.graph.password=nebula
skylab.data.api.graph.cacheEnabled=false
skylab.data.api.graph.localCacheEnabled=false
skylab.data.api.graph.cacheVersion=20250613_001
skylab.data.api.graph.path=${SKYNET_PLUGIN_HOME}/nebulaData_20250605_001.zip
skylab.data.api.graph.nodeProps.UNIT=name
skylab.data.api.graph.nodeProps.CHECK_POINT=name
skylab.data.api.graph.nodeProps.REVIEW_POINT=areaSupport
skylab.data.api.graph.nodeProps.ANCHOR_POINT=anchorPointType,difficulty,name,clusterAttribute,centerPoints,extPoints,evaluations,examPoint,phaseCode,realLastLevelRelationCatas,subjectCode,tracePoints
skylab.data.api.graph.cacheFiles=
skylab.data.api.graph.maxConnSize=1000
#------------------------------------------------------------------------------------------------------
# Elasticsearch Configuration
#------------------------------------------------------------------------------------------------------
zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.es-host=***********:9200,***********:9200,***********:9200
zion.es-user-name=elastic
#zion.es-password=bx90ZOw1IZbx8fWCIo64
zion.query-data-base=es
zion.dict-refresh-period-seconds=10
zion.cache-ttl=PT3M
#------------------------------------------------------------------------------------------------------
# Management Configuration
#------------------------------------------------------------------------------------------------------
management.metrics.export.prometheus.enabled=true
# ??????endpoint
management.endpoints.web.exposure.include=*
#------------------------------------------------------------------------------------------------------
# Skyline Brave Configuration
#------------------------------------------------------------------------------------------------------
skyline.brave.enabled=true
skyline.brave.trace-debug-enabled=true
skyline.brave.file-enabled=true
skyline.brave.aop-enabled=true
skyline.brave.kafka-enabled=false
skyline.brave.allowSubjectCodes=00,01,02,03,04,05,06,13
#------------------------------------------------------------------------------------------------------
# Skylab Configuration
#------------------------------------------------------------------------------------------------------
skylab.data.api.mastery.useDeleteFlag=false
#------------------------------------------------------------------------------------------------------
# SSE Configuration
#------------------------------------------------------------------------------------------------------
# sse thread pool size setting
banxue.sse.core.pool.size=32
banxue.sse.max.pool.size=80
banxue.sse.session.timeout=120000
#------------------------------------------------------------------------------------------------------
# Retry Configuration
#------------------------------------------------------------------------------------------------------
# retry count
banxue.retry.count=2
#------------------------------------------------------------------------------------------------------
# Engine Prompt Configuration
#------------------------------------------------------------------------------------------------------
engine.prompt.class_chat=D:/tmp/promt/class_chat.txt
engine.prompt.cluster_choice=D:/tmp/promt/cluster_choice.txt
engine.prompt.portrait_infer=D:/tmp/promt/portrait_infer.txt
engine.prompt.study_plan=D:/tmp/promt/study_plan.txt
engine.knowledge.qa=D:/tmp/qa/anchor_knowledge_classchatqa.txt