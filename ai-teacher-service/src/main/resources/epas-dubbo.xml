<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <!--epasConfig bean管理-->
    <bean id="epasConfig" class="com.iflytek.edu.epas.dubbo.config.EpasConfig">
        <!-- 在平台申请的appKey -->
        <!--题库内测-->
        <property name="appKey" value="${application.epas.appKey}"/>
        <property name="appSecret" value="${application.epas.appSecret}"/>
        <!-- 地址服务url 配置中心下拉使用addrServerUrl,本地调试使用registerUrl-->
        <property name="addrServerUrl" value="${application.epas.addrServerUrl}"/>
    </bean>

</beans>