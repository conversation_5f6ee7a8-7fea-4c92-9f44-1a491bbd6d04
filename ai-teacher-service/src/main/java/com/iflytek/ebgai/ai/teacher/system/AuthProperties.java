package com.iflytek.ebgai.ai.teacher.system;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @data 2024/10/22
 */
@Data
@Configuration
public class AuthProperties {
    /**
     * token鉴权开关，默认开启鉴权
     */
    @Value("${recommend.auth.enable}")
    private boolean enable = true;
    /**
     * token鉴权地址
     */
    @Value("${recommend.auth.url}")
    private String authUrl;

    @Value("${recommend.auth.token.url}")
    private String tokenUrl;

}
