package com.iflytek.ebgai.ai.teacher.entity.response.share;

import lombok.Data;

import java.util.List;

/**
 * 问卷内容信息实体
 */
@Data
public class NaireInfo {
    /**
     * 在问卷中的索引 在问卷中固定顺序 和入参一样roundRecNum
     */
    private int index;

    /**
     * 问卷维度,枚举值：
     * USER_RANK("成绩排名","USER_RANK"),
     * WORK_STATE("作业感受","WORK_STATE"),
     * STUDY_TARGET("学习目标","STUDY_TARGET"),
     * CORE_LITERACY("学习素养","CORE_LITERACY")
     */
    private String dimension;

    /**
     * 本轮id 一轮问卷流程内唯一
     */
    private String roundId;

    /**
     * 题干信息
     */
    private String questionContent;

    /**
     * 选项信息
     */
    private List<Option> options;

    /**
     * 单选 = true or 多选 = false 默认true
     */
    private boolean isRadio = true;

    /**
     * 最多选几个答案 默认=1
     */
    private int choiceNum = 1;
}