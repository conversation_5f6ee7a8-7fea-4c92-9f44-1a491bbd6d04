package com.iflytek.ebgai.ai.teacher.entity.questionnaire;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:huizhang68
 * @Date:2025/5/30
 */
@Data
@ApiModel(description = "问卷解释响应实体")
public class QuestionnaireExplainResponseAO implements Serializable {
    @ApiModelProperty(value = "用户层级，枚举值：conventional 基础用户、highScoreAdvanced 进阶用户、thinkingExpansion拔高用户", required = true)
    private String userLevel;

    @ApiModelProperty(value = "本轮id 一轮问卷流程内唯一，可以和会话id共用", required = true)
    private String roundId;
    @ApiModelProperty(value = "总结详情", required = true)
    private List<SummaryAO> summaries;

    @Data
    @ApiModel(description = "问卷解释响应实体-总结详情")
    public static class SummaryAO {
        /**
         * 总结标题 比如：基础补漏型、重点突破考点、时间安排 等
         */
        @ApiModelProperty(value = "总结标题 比如：基础补漏型、重点突破考点、时间安排 等", required = true)
        private String headline;
        /**
         * 总结内容展示位
         */
        @ApiModelProperty(value = "总结内容展示位", required = true)
        private int order;
        /**
         * 总结内容 ，按照顺序排列
         */
        @ApiModelProperty(value = "总结内容 ，按照顺序排列", required = true)
        private List<String> contents;
    }
}
