package com.iflytek.ebgai.ai.teacher.controller.plan;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.builder.DialogueLlmResourceBuilder;
import com.iflytek.ebgai.ai.teacher.builder.LlmRequestBuilder;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.entity.request.share.InferBody;
import com.iflytek.ebgai.ai.teacher.entity.request.share.SceneInfo;
import com.iflytek.ebgai.ai.teacher.entity.request.share.SessionInfo;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.entity.response.plan.DialogueLlmResource;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.factory.LlmType;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.rec.teacher.interfaces.param.LlmRequest;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;

/**
 * 课前对话控制器
 *
 * <AUTHOR>
 * @version 1.2
 * @Date: 2025/6/4
 * @Description: 重构为同步响应模式
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/plan")
public class PreClassDialogueController extends BasePlanController {
    private final TraceUtils traceUtils;

    public PreClassDialogueController(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    @ServiceRequestMetrics(desc = "课前对话")
    @ApiOperation(value = "课前对话", notes = "课前对话（同步响应）")
    @PostMapping(value = "/dialogue", produces = MediaType.APPLICATION_JSON_VALUE)
    @SkylineTraceStarter(typePrefix = "dialogue#", isSync4Request = false)
    public CommonResponse<DialogueLlmResource> dialogue(@RequestBody XxjLlmRequest xxjLlmRequest, HttpServletRequest request) throws IOException {
        //参数校验
        checkInputParameters(xxjLlmRequest, request);
        final String traceId = HttpUtils.getTraceId(request);
        // 1. 构建LLM请求
        Map<String, String> headers = HttpUtils.getHeadersMap(request);
        LlmRequest llmRequest = buildLlmRequest(xxjLlmRequest, headers);
        log.info("[TraceID:{}] 课前对话请求参数:{}", traceId, JSON.toJSONString(llmRequest));
        traceUtils.record("dialogueEngineRequest", llmRequest);
        int times = 0;
        CommonResponse<DialogueLlmResource> result = null;
        while (times < retryCount) {
            log.info("[TraceID:{}] 课前对话重试第{}次", traceId, times);
            times++;
            if (times == 1) {
                result = tryGetDialogueLlmResourceCommonResponse(llmRequest, traceId);
                if (RetCode.SUCCESS.getCode().equals(result.getCode())) {
                    break;
                }
            } else {
                if (RetCode.CONTENT_CHECK_FAIL.getCode().equals(result.getCode())) {
                    log.info("[TraceID:{}] 内容审核失败，调用引擎课前对话接口,第{}次", traceId, times);
                    xxjLlmRequest.getInferBody().setContent("未作答");
                    llmRequest = buildLlmRequest(xxjLlmRequest, headers);
                }
                if (RetCode.SUCCESS.getCode().equals(result.getCode())) {
                    break;
                }
            }


        }
        return result;
    }

    private CommonResponse<DialogueLlmResource> tryGetDialogueLlmResourceCommonResponse(LlmRequest llmRequest, String traceId) {
        try {
            return getDialogueLlmResourceCommonResponse(llmRequest, traceId);
        } catch (EngineException e) {
            traceUtils.recordErr("dialogueEngineException", e);
            log.error("[TraceID:{}] 引擎调用异常: {}", traceId, e.getMessage(), e);
            return new CommonResponse<>(String.valueOf(e.getErrorCode()), e.getMessage(), traceId);

        } catch (Error error) {
            traceUtils.recordErr("dialogueEngineError", error);
            log.error("[TraceID:{}] 课前对话系统错误: {}", traceId, error.getMessage(), error);
            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), error.getMessage(), traceId);

        } catch (Exception e) {
            traceUtils.recordErr("dialogueException", e);
            log.error("[TraceID:{}] 课前对话处理异常: {}", traceId, e.getMessage(), e);
            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), e.getMessage(), traceId);
        }
    }

    @NotNull
    private CommonResponse<DialogueLlmResource> getDialogueLlmResourceCommonResponse(LlmRequest llmRequest, String traceId) {
        long startTime = System.currentTimeMillis();
        // 2. 获取LLM资源（同步阻塞获取第一个资源）
        LlmResource resource = engineFacade.conductPreClassDialogue(llmRequest, LlmType.CLASS_CHAT).next().block();
        long cost = System.currentTimeMillis() - startTime;
        traceUtils.record("dialogueEngineCost", Maps.of("cost", cost, "traceId", traceId));
        log.info("[TraceID:{}] 课前对话引擎接口耗时: {} ms", traceId, cost);
        traceUtils.record("dialogueEngineResponse", resource);

        if (resource == null) {
            log.warn("[TraceID:{}] 课前对话未获取到有效资源", traceId);
            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), "未获取到有效资源", traceId);
        }

        // 3. 处理资源（更新掌握度/存储对话结果）
        processResource(resource, traceId);

        // 4. 构建并返回响应
        return buildResponse(resource, traceId);
    }

    /**
     * 构建LLM请求
     */
    private LlmRequest buildLlmRequest(XxjLlmRequest xxjLlmRequest, Map<String, String> headers) {
        return LlmRequestBuilder.create().withXxjLlmRequest(xxjLlmRequest).withHeaders(headers).withBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode()).withFunctionCode(BizConstants.FunctionCode.LEARNINGPLAN_CHAT.getCode()).withBizCode(BizConstants.BIZ_CODE_DEFAULT).withDebugMode(debugMode).withAgentId(agentId).withShortcutType(shortcuttype).withWorkflowId(deepseekrV3Workflowid).withGraphVersion(graphVersion).build();
    }

    /**
     * 处理LLM资源
     */
    private void processResource(LlmResource resource, String traceId) {

        try {
            if (resource.isUpdateMastery()) {
                log.info("[TraceID:{}] 更新掌握度-课前对话调用引擎返回:{}", traceId, JSON.toJSONString(resource));
                masterServiceFacade.updateMasterData(resource, traceId);
            }

            if (resource.isStorage()) {
                log.info("[TraceID:{}] 存储课前对话调用引擎返回:{}", traceId, JSON.toJSONString(resource));
                storageInfoServiceFacade.insert(resource, traceId);
            }
        } catch (Exception e) {
            traceUtils.recordErr("dialogueDBOpException", e);
            log.error("[TraceID:{}] 资源处理异常: {}", traceId, e.getMessage(), e);
        }
    }

    /**
     * 构建响应对象
     */
    private CommonResponse<DialogueLlmResource> buildResponse(LlmResource resource, String traceId) {
        long start = System.currentTimeMillis();
        DialogueLlmResource dialogueResource = DialogueLlmResourceBuilder.build(resource);
        log.info("[TraceID:{}] 课前对话返回参数给前端数据组装耗时：{}", traceId, System.currentTimeMillis() - start);
        return new CommonResponse<>(RetCode.SUCCESS.getCode(), RetCode.SUCCESS.getDesc(), traceId, dialogueResource);
    }

    private void checkInputParameters(XxjLlmRequest xxjLlmRequest, HttpServletRequest request) throws IOException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = checkHeaders(request);
        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();

        if (xxjLlmRequest.getSceneInfo() == null) {
            missingParams.add("sceneInfo");
        } else {
            SceneInfo sceneInfo = xxjLlmRequest.getSceneInfo();
            if (sceneInfo.getSubjectCode() == null || sceneInfo.getSubjectCode().isEmpty()) {
                missingParams.add("sceneInfo.subjectCode");
            }
            if (sceneInfo.getPhaseCode() == null || sceneInfo.getPhaseCode().isEmpty()) {
                missingParams.add("sceneInfo.phaseCode");
            }
        }

        if (xxjLlmRequest.getSessionInfo() == null) {
            missingParams.add("sessionInfo");
        } else {
            SessionInfo sessionInfo = xxjLlmRequest.getSessionInfo();
            if (sessionInfo.getRoundRecNum() <= 0) {
                missingParams.add("sessionInfo.roundRecNum 必须大于0");
            }
            if (sessionInfo.getRoundId() == null || sessionInfo.getRoundId().isEmpty()) {
                missingParams.add("sessionInfo.roundId");
            }
        }

        if (xxjLlmRequest.getInferBody() == null) {
            missingParams.add("inferBody");
        } else {
            InferBody inferBody = xxjLlmRequest.getInferBody();
            if (inferBody.getCatalogId() == null || inferBody.getCatalogId().isEmpty()) {
                missingParams.add("inferBody.catalogId");
            }
            if (inferBody.getChatId() == null || inferBody.getChatId().isEmpty()) {
                missingParams.add("inferBody.chatId");
            }
        }

        // 3. 如果有缺失参数则发送错误响应
        if (!missingHeaders.isEmpty() || !missingParams.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("参数校验失败: ");

            if (!missingHeaders.isEmpty()) {
                errorMsg.append("缺失请求头 - ").append(String.join(", ", missingHeaders));
            }

            if (!missingParams.isEmpty()) {
                if (!missingHeaders.isEmpty()) {
                    errorMsg.append("; ");
                }
                errorMsg.append("缺失请求参数 - ").append(String.join(", ", missingParams));
            }

            errorInfo = errorMsg.toString();

        }
        if (StringUtils.isNotEmpty(errorInfo)) {
            log.error("[TraceID:{}] {}", HttpUtils.getTraceId(request), errorInfo);
            throw new ParamValidateException(RetCode.INPUT_PARAMETERS_MISSING.getCode(), errorInfo, request.getHeader("x-trace-id"));
        }
    }
}