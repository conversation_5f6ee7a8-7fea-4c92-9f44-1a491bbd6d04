package com.iflytek.ebgai.ai.teacher.service.plan;

import com.alibaba.fastjson.JSON;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.Summary;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.SummaryInfo;
import com.iflytek.ebgai.ai.teacher.dataapi.service.StorageInfoService;
import com.iflytek.rec.teacher.domain.agent.response.StorageInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 问卷总结+规划总结数据服务
 *
 * <AUTHOR>
 * @version 1.0
 * @ClassName StorageInfoServiceFacadeImpl
 * @Date: 2025/5/28 10:10
 * @Description:
 */
@Slf4j
@Service
public class StorageInfoServiceFacadeImpl implements StorageInfoServiceFacade {

    @Autowired
    private StorageInfoService storageInfoService;

    @Override
    public void insert(LlmResource llmResource, String traceId) {
        log.info("[TraceID:{}] 开始调用dataapi记录课前对话总结结果", traceId);
        long start = System.currentTimeMillis();
        StorageInfo storageInfo = llmResource.getStorageInfo();
        Assert.notNull(storageInfo, "课前对话存储信息不能为空!");
        StorageInfoRequest storageInfoRequest = new StorageInfoRequest();
        storageInfoRequest.setUserId(storageInfo.getUserId()).setCatalogId(storageInfo.getCatalogId()).setFunctionCode(storageInfo.getFunctionCode());
        List<StorageInfoData> storageInfoDataList = storageInfoService.query(storageInfoRequest);

        StorageInfoData storageInfoData = new StorageInfoData().setUserId(storageInfo.getUserId())                      // 设置用户ID
                .setUserLevel(storageInfo.getUserLevel())         // 设置用户层级
                .setTraceId(traceId)                // 设置跟踪ID
                .setBizAction(storageInfo.getBizAction()) // 设置业务方功能
                .setFunctionCode(storageInfo.getFunctionCode())       // 设置引擎功能
                .setRoundId(storageInfo.getRoundId())                   // 设置本轮ID
                .setQuery(storageInfo.getQuery())          // 设置用户输入
                .setOriContent(storageInfo.getOriContent()) // 设置原始模型输出
                .setCatalogId(storageInfo.getCatalogId()).setSummaryInfo(new SummaryInfo()).setUpdateTime(storageInfo.getUpdateTime()); // 设置更新时间（当前时间戳）

        if (CollectionUtils.isEmpty(storageInfoDataList)) {
            //2. 新增
            storageInfoService.insert(storageInfoData);
        } else {
            //更新操作循环更新，考虑兼容
            for (StorageInfoData storageInfoDataOld : storageInfoDataList) {
                //2. 更新
                BeanUtils.copyProperties(storageInfoData, storageInfoDataOld);
                storageInfoService.update(storageInfoDataOld);
            }
        }


        log.info("[TraceID:{}] 结束调用dataapi记录课前对话总结结果,耗时：{}", traceId, System.currentTimeMillis() - start);
    }

}
