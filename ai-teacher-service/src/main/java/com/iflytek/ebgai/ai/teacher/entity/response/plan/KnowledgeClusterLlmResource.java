package com.iflytek.ebgai.ai.teacher.entity.response.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class KnowledgeClusterLlmResource implements Serializable {
    @JsonProperty("isFluxEnd")
    private Boolean isFluxEnd;
    private Intention intention;

    private KnowledgeClusterLlmResource(Builder builder) {
        this.isFluxEnd = builder.isFluxEnd;
        this.intention = builder.intention;
    }

    public static class Builder {
        private Boolean isFluxEnd;
        private Intention intention;

        public Builder intention(Intention intention) {
            this.intention = intention;
            return this;
        }

        public Builder isFluxEnd(Boolean isFluxEnd) {
            this.isFluxEnd = isFluxEnd;
            return this;
        }

        public KnowledgeClusterLlmResource build() {
            return new KnowledgeClusterLlmResource(this);
        }
    }

    @Data
    public static class Intention {
        private List<CommonLlmResource.NodeInfo> nodeInfos;

        private Intention(Builder builder) {
            this.nodeInfos = builder.nodeInfos;
        }

        public static class Builder {
            private List<CommonLlmResource.NodeInfo> nodeInfos;

            public Builder nodeInfos(List<CommonLlmResource.NodeInfo> nodeInfos) {
                this.nodeInfos = nodeInfos;
                return this;
            }

            public Intention build() {
                return new Intention(this);
            }
        }
    }

    public static class NodeInfo {
        private final int order;
        private final String nodeId;
        private final String nodeName;
        private final String nodeType;
        private final String nodeAttribute;
        private final String learndBehavior;
        private final int learndTimes;
        private String showState = "MAINTAIN"; // 默认值
        private final Double masterScore;
        private final String catalogId;

        private NodeInfo(Builder builder) {
            this.order = builder.order;
            this.nodeId = builder.nodeId;
            this.nodeName = builder.nodeName;
            this.nodeType = builder.nodeType;
            this.nodeAttribute = builder.nodeAttribute;
            this.learndBehavior = builder.learndBehavior;
            this.learndTimes = builder.learndTimes;
            this.showState = builder.showState;
            this.masterScore = builder.masterScore;
            this.catalogId = builder.catalogId;
        }

        public static class Builder {
            private int order; // 必须
            private String nodeId; // 必须
            private String nodeName; // 必须
            private String nodeType; // 必须
            private String nodeAttribute; // 必须
            private String learndBehavior; // 必须
            private int learndTimes; // 必须
            private String showState = "MAINTAIN"; // 必须，带默认值
            private Double masterScore; // 可选
            private String catalogId; // 必须


            public Builder order(int order) {
                this.order = order;
                return this;
            }

            public Builder nodeId(String nodeId) {
                this.nodeId = nodeId;
                return this;
            }

            public Builder nodeName(String nodeName) {
                this.nodeName = nodeName;
                return this;
            }

            public Builder nodeType(String nodeType) {
                this.nodeType = nodeType;
                return this;
            }

            public Builder nodeAttribute(String nodeAttribute) {
                this.nodeAttribute = nodeAttribute;
                return this;
            }

            public Builder learndBehavior(String learndBehavior) {
                this.learndBehavior = learndBehavior;
                return this;
            }

            public Builder learndTimes(int learndTimes) {
                this.learndTimes = learndTimes;
                return this;
            }

            public Builder showState(String showState) {
                this.showState = showState;
                return this;
            }

            public Builder masterScore(Double masterScore) {
                this.masterScore = masterScore;
                return this;
            }

            public Builder catalogId(String catalogId) {
                this.catalogId = catalogId;
                return this;
            }

            public NodeInfo build() {
                return new NodeInfo(this);
            }
        }
    }

    // 省略getter方法
}