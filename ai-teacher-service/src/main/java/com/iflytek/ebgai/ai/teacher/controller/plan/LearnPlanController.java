package com.iflytek.ebgai.ai.teacher.controller.plan;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.builder.LearnPlanLlmResourceBuilder;
import com.iflytek.ebgai.ai.teacher.builder.LlmRequestBuilder;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.entity.request.share.InferBody;
import com.iflytek.ebgai.ai.teacher.entity.request.share.SceneInfo;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.entity.response.plan.LearnPlanLlmResource;
import com.iflytek.ebgai.ai.teacher.factory.LlmType;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.rec.teacher.interfaces.param.LlmRequest;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;

/**
 * 学习规划控制器
 *
 * <p>核心功能：</p>
 * <ul>
 *   <li>提供基于SSE(Server-Sent Events)的学习规划接口</li>
 *   <li>处理学习规划请求参数校验和转换</li>
 *   <li>调用AI引擎生成个性化学习规划</li>
 *   <li>实现流式响应处理和学习结果存储</li>
 *   <li>集成监控和链路追踪能力</li>
 * </ul>
 *
 * <p>业务场景：</p>
 * 用于生成学生个性化学习路径规划，根据学科、学段、知识点等输入条件，
 * 通过AI引擎分析生成适合学生的学习内容和进度安排。
 *
 * <p>技术实现：</p>
 * 采用响应式编程模型处理流式响应，结合TraceUtils实现全链路监控，
 * 使用线程池异步处理SSE事件发送。
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/v1/plan", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
public class LearnPlanController extends BasePlanController {
    @Autowired
    private ThreadPoolExecutor sseSenderExecutor;
    private final TraceUtils traceUtils;

    public LearnPlanController(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    /**
     * 学习规划主接口
     *
     * <p>功能说明：</p>
     * 1. 接收学习规划请求，生成SSE(Server-Sent Events)响应流
     * 2. 执行参数校验和请求转换
     * 3. 调用AI引擎生成学习规划
     * 4. 处理流式响应和异常情况
     *
     * @param xxjLlmRequest 学习规划请求体，包含：
     *                      - sceneInfo: 场景信息(学科、学段等)
     *                      - inferBody: 推理参数(知识点ID等)
     * @param request       HTTP请求对象，用于获取请求头等信息
     * @return SseEmitter SSE事件发射器，用于流式返回学习规划结果
     * @throws IOException 当SSE通信发生错误时抛出
     *
     *                     <p>处理流程：</p>
     *                     1. 参数校验 → 2. 构建引擎请求 → 3. 调用引擎接口 → 4. 处理流式响应
     *
     *                     <p>监控指标：</p>
     *                     - 记录请求参数错误(learnPlanRequestParamError)
     *                     - 记录引擎请求(learnPlanEngineRequest)
     *                     - 记录引擎异常(learnPlanEngineException)
     *                     - 记录系统错误(learnPlanEngineError)
     */
    @ServiceRequestMetrics(desc = "学习规划")
    @ApiOperation(value = "学习规划", notes = "学习规划")
    @PostMapping(value = "/sse/learn", produces = MediaType.TEXT_EVENT_STREAM_VALUE + ";charset=UTF-8")
    @SkylineTraceStarter(typePrefix = "learnPlan#", isSync4Request = false)
    public SseEmitter learnPlan(@RequestBody XxjLlmRequest xxjLlmRequest, HttpServletRequest request) throws IOException {
        SseEmitter emitter = new SseEmitter(timeOut);
        final String traceId = HttpUtils.getTraceId(request);
        //参数校验
        String errorInfo = checkInputParameters(xxjLlmRequest, request);
        if (StringUtils.isNotEmpty(errorInfo)) {
            CommonResponse<String> errorReponse = handleParameterError(errorInfo, request.getHeader("x-trace-id"));
            if (emitter != null) {
                emitter.send(errorReponse);
                Throwable throwable = new ParamValidateException(errorReponse.getCode(), errorReponse.getMessage(), traceId);
                emitter.completeWithError(throwable);
            }
            traceUtils.recordErr("learnPlanRequestParamError", errorReponse);
            return emitter;
        }


        try {
            Map<String, String> headers = HttpUtils.getHeadersMap(request);
            LlmRequest llmRequest = buildLlmRequest(xxjLlmRequest, headers);
            traceUtils.record("learnPlanEngineRequest", llmRequest);
            log.info("[TraceID:{}] 学习规划请求开始:{}", traceId, JSON.toJSONString(llmRequest));
            processResourceStream(emitter, llmRequest, traceId);
        } catch (EngineException ex) {
            traceUtils.recordErr("learnPlanEngineException", ex);
            log.error("[TraceID:{}] 学习规划引擎接口调用异常：[Code:{}] - {}", traceId, ex.getErrorCode(), ex.getMessage(), ex);
            CommonResponse errorResponse = new CommonResponse<>(String.valueOf(ex.getErrorCode()), ex.getMessage(), traceId);
            emitter.send(errorResponse);
            emitter.completeWithError(ex);
        } catch (Error error) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            error.printStackTrace(pw);  // 将堆栈写入 PrintWriter
            String fullStackTrace = sw.toString();
            traceUtils.recordErr("learnPlanEngineError", fullStackTrace);
            log.error("[TraceID:{}] 请求学习规划引擎接口错误:{}", traceId, fullStackTrace);
            CommonResponse<String> errorReponse = handleError(error, traceId);
            emitter.send(errorReponse);
            emitter.completeWithError(error);
        } catch (Exception e) {
            traceUtils.recordErr("learnPlanException", e);
            log.error("[TraceID:{}] 学习规划请求异常:{}", traceId, e);
            sendExceptionResponse(emitter, traceId, e);
        }


        return emitter;
    }

    private String checkInputParameters(XxjLlmRequest xxjLlmRequest, HttpServletRequest request) throws IOException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = checkHeaders(request);
        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();

        if (xxjLlmRequest.getSceneInfo() == null) {
            missingParams.add("sceneInfo");
        } else {
            SceneInfo sceneInfo = xxjLlmRequest.getSceneInfo();
            if (sceneInfo.getSubjectCode() == null || sceneInfo.getSubjectCode().isEmpty()) {
                missingParams.add("sceneInfo.subjectCode");
            }
            if (sceneInfo.getPhaseCode() == null || sceneInfo.getPhaseCode().isEmpty()) {
                missingParams.add("sceneInfo.phaseCode");
            }
        }

        if (xxjLlmRequest.getInferBody() == null) {
            missingParams.add("inferBody");
        } else {
            InferBody inferBody = xxjLlmRequest.getInferBody();
            if (inferBody.getCatalogId() == null || inferBody.getCatalogId().isEmpty()) {
                missingParams.add("inferBody.catalogId");
            }
            if (inferBody.getChatId() == null || inferBody.getChatId().isEmpty()) {
                missingParams.add("inferBody.chatId");
            }
        }

        // 3. 如果有缺失参数则发送错误响应
        if (!missingHeaders.isEmpty() || !missingParams.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("参数校验失败: ");

            if (!missingHeaders.isEmpty()) {
                errorMsg.append("缺失请求头 - ").append(String.join(", ", missingHeaders));
            }

            if (!missingParams.isEmpty()) {
                if (!missingHeaders.isEmpty()) {
                    errorMsg.append("; ");
                }
                errorMsg.append("缺失请求参数 - ").append(String.join(", ", missingParams));
            }

            errorInfo = errorMsg.toString();

            return errorInfo;
        }
        return errorInfo;
    }

    private LlmRequest buildLlmRequest(XxjLlmRequest xxjLlmRequest, Map<String, String> headers) {
        return LlmRequestBuilder.create().withXxjLlmRequest(xxjLlmRequest).withHeaders(headers).withBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode()).withFunctionCode(BizConstants.FunctionCode.LEARNINGPLAN_PLAN.getCode()).withBizCode(BizConstants.BIZ_CODE_DEFAULT).withDebugMode(debugMode).withAgentId(agentId).withShortcutType(shortcuttype).withWorkflowId(deepseekr1Workflowid).withGraphVersion(graphVersion).build();
    }


    private void processResourceStream(SseEmitter emitter, LlmRequest llmRequest, String traceId) {
        Flux<LlmResource> resourceFlux = engineFacade.createStudyPlan4Stream(llmRequest, LlmType.LEARN_PLAN).subscribeOn(Schedulers.boundedElastic()).doOnError(e -> log.error("[TraceID:{}] 学习规划调用引擎接口异常", traceId, e));

        Disposable subscription = resourceFlux.doOnNext(resource -> handleStorageResource(resource, traceId)).map(resource -> buildSuccessResponse(resource, traceId)).subscribe(response -> sendSseEvent(emitter, response, traceId), error -> handleStreamError(emitter, error, traceId), () -> completeSseEmitter(emitter, traceId));

        setupEmitterCallbacks(emitter, subscription, traceId);
    }

    private void handleStorageResource(LlmResource resource, String traceId) {
        log.debug("[TraceID:{}] 调用学习规划引擎返回:{}", traceId, JSON.toJSONString(resource));
        if (resource.isStorage()) {
            try {
                //记录学习规划最后一帧响应
                traceUtils.record("learnPlanLastResponse", resource);
                planResultServiceFacade.savePlanResult(resource, traceId);
                log.info("[TraceID:{}] 规划结果存储成功", traceId);
            } catch (Exception e) {
                traceUtils.recordErr("learnPlanDBOpExceptionException", e);
                log.error("[TraceID:{}] 规划结果存储失败", traceId, e);
            }
        }
    }

    private CommonResponse<LearnPlanLlmResource> buildSuccessResponse(LlmResource resource, String traceId) {
        LearnPlanLlmResource learnPlanResource = LearnPlanLlmResourceBuilder.build(resource);
        CommonResponse<LearnPlanLlmResource> response = new CommonResponse<>();
        response.setTraceId(traceId);
        response.setData(learnPlanResource);
        return response;
    }

    private void sendSseEvent(SseEmitter emitter, CommonResponse<?> response, String traceId) {
        sseSenderExecutor.execute(() -> {
            try {
                synchronized (emitter) {
                    String json = JSON.toJSONString(response);
                    log.debug("[TraceID:{}] 发送SSE事件: {}", traceId, json);
                    emitter.send(json);
                }
            } catch (IllegalStateException e) {
                log.warn("[TraceID:{}] SSE连接已关闭，无法发送事件: {}", traceId, e.getMessage());
            } catch (IOException e) {
                log.error("[TraceID:{}] SSE发送失败", traceId, e);
                handleSendError(emitter, e);
            }
        });
    }

    private void handleStreamError(SseEmitter emitter, Throwable error, String traceId) {
        log.error("[TraceID:{}] 流处理错误", traceId, error);
        CommonResponse<String> errorResponse = buildErrorResponse(error, traceId);
        sendExceptionResponse(emitter, errorResponse, traceId);
        emitter.complete();
    }

    private CommonResponse<String> buildErrorResponse(Throwable error, String traceId) {
        String errorCode = RetCode.AI_TEACHER_ERROR.getCode();
        String errorMessage = RetCode.AI_TEACHER_ERROR.getDesc();

        if (error instanceof EngineException) {
            EngineException engineEx = (EngineException) error;
            errorCode = String.valueOf(engineEx.getErrorCode());
            errorMessage = engineEx.getMessage();
        }

        return new CommonResponse<>(errorCode, errorMessage, traceId);
    }

    private void completeSseEmitter(SseEmitter emitter, String traceId) {
        try {
            log.info("[TraceID:{}] SSE流处理完成", traceId);
            emitter.complete();
        } catch (Exception e) {
            log.error("[TraceID:{}] SSE完成操作失败", traceId, e);
        }
    }

    private void sendExceptionResponse(SseEmitter emitter, CommonResponse<?> errorResponse, String traceId) {
        try {
            String errorJson = JSON.toJSONString(errorResponse);
            emitter.send(errorJson, MediaType.APPLICATION_JSON);
        } catch (IOException e) {
            log.error("[TraceID:{}] 错误响应发送失败", traceId, e);
        }
    }

    private void sendExceptionResponse(SseEmitter emitter, String traceId, Exception exception) {
        String errorCode = RetCode.AI_TEACHER_ERROR.getCode();
        String errorMessage = RetCode.AI_TEACHER_ERROR.getDesc();
        if (exception instanceof EngineException) {
            EngineException engineEx = (EngineException) exception;
            errorCode = String.valueOf(engineEx.getErrorCode());
            errorMessage = engineEx.getMessage();
        }
        CommonResponse<String> errorResponse = new CommonResponse<>(errorCode, errorMessage, traceId);
        sendExceptionResponse(emitter, errorResponse, traceId);
        emitter.complete();
    }

    private void setupEmitterCallbacks(SseEmitter emitter, Disposable subscription, String traceId) {
        // 超时处理
        emitter.onTimeout(() -> {
            log.warn("[TraceID:{}] SSE超时", traceId);
            subscription.dispose();
            emitter.complete();
        });

        // 错误处理
        emitter.onError(e -> {
            log.error("[TraceID:{}] SSE错误: {}", traceId, e.getMessage());
            subscription.dispose();
        });

        // 完成处理
        emitter.onCompletion(() -> {
            log.info("[TraceID:{}] SSE客户端断开", traceId);
            subscription.dispose();
        });
    }

    private void handleSendError(SseEmitter emitter, IOException e) {
        try {
            emitter.completeWithError(e);
        } catch (IllegalStateException ex) {
            log.error("发送消息失败:{}", ex.getMessage());
            // 忽略已关闭的emitter
        }
    }
}