package com.iflytek.ebgai.ai.teacher.service.questionnaire;

import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.DeleteLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferRequest;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireAnswerRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;

import javax.servlet.http.HttpServletRequest;

/**
 * 问卷前端接口参数校验服务
 *
 * @Author:huizhang68
 * @Date:2025/5/28
 */
public interface QuestionnaireCheckParamService {

    void checkInputParameters(QuestionnaireRequestAO requestAO, HttpServletRequest request) throws ParamValidateException;

    void checkInputParameters(QuestionnaireAnswerRequestAO requestAO, HttpServletRequest request) throws ParamValidateException;

    void checkInputParameters(XxjLlmRequest requestAO, HttpServletRequest request) throws ParamValidateException;

    void checkInputParameters(QueryQuestionnaireInferRequest requestAO) throws ParamValidateException;

    void checkInputParameters(QueryLearningPlanRequest requestAO) throws ParamValidateException;

    void checkInputParameters(DeleteLearningPlanRequest requestAO) throws ParamValidateException;

}
