package com.iflytek.ebgai.ai.teacher.service.plan;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

public interface StorageInfoServiceFacade {
    /**
     * 问卷总结+规划总结表存储
     *
     * @param llmResource
     * @param traceId
     */
    void insert(LlmResource llmResource, String traceId);
}
