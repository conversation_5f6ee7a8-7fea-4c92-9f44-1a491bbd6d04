package com.iflytek.ebgai.ai.teacher.util;


import lombok.extern.slf4j.Slf4j;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Http请求工具类
 *
 * <AUTHOR>
 * @data 2024/10/24
 */
@Slf4j
public class HttpUtils {
    private static final String TRACE_ID_HEADER = "x-trace-id";

    public static final String CHARSET = "UTF-8";

    private static final int HTTP_READ_TIMEOUT = 15000;

    private static final int HTTP_CONNECT_TIMEOUT = 15000;


    public static String sendGet(String url) {
        return sendGet(url, HTTP_READ_TIMEOUT, HTTP_CONNECT_TIMEOUT);
    }

    public static String sendGet(String url, int readTimeOut, int connectTimeOut) {
        StringBuilder result = new StringBuilder();

        HttpURLConnection conn = null;

        try {
            URL uri = new URL(url);
            conn = (HttpURLConnection) uri.openConnection();
            conn.setRequestMethod("GET");
            // 设置超时时间
            conn.setReadTimeout(readTimeOut);
            conn.setConnectTimeout(connectTimeOut);
            conn.connect();

            int responseCode = conn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        result.append(line);
                    }
                }
            } else {
                throw new IOException("HTTP GET request failed with response code: " + responseCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
        return result.toString();
    }


    // 获取单值 Header Map
    public static Map<String, String> getHeadersMap(HttpServletRequest request) {
        Map<String, String> headers = new LinkedHashMap<>();
        Collections.list(request.getHeaderNames()).forEach(headerName -> headers.put(headerName, request.getHeader(headerName)));
        return headers;
    }


    public static String getTraceId(HttpServletRequest request) {
        return request.getHeader(TRACE_ID_HEADER);
    }
}