package com.iflytek.ebgai.ai.teacher.dubbo.mapper;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.StorageInfo;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.Summary;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.SummaryInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 废弃，待删除
 *
 * @Author:huizhang68
 * @Date:2025/5/24
 */
public class StorageInfoMapping {

    private volatile static StorageInfoMapping instance;

    private StorageInfoMapping() {
    }

    public static StorageInfoMapping getInstance() {
        if (instance == null) {
            synchronized (StorageInfoMapping.class) {
                if (instance == null) {
                    instance = new StorageInfoMapping();
                }
            }
        }
        return instance;
    }

    public StorageInfo convert(StorageInfoData source) {
        if (source == null) {
            return null;
        }
        StorageInfo target = new StorageInfo();
        target.setUserId(source.getUserId());
        target.setUserLevel(source.getUserLevel());
        target.setTraceId(source.getTraceId());
        target.setCatalogId(source.getCatalogId());
        target.setRoundId(source.getRoundId());
        target.setQuery(source.getQuery());
        target.setOriContent(source.getOriContent());
        target.setUpdateTime(source.getUpdateTime());
        SummaryInfo summaryInfo = convert(source.getSummaryInfo());
        target.setSummaryInfo(summaryInfo);
        return target;
    }

    private SummaryInfo convert(com.iflytek.ebgai.ai.teacher.dataapi.entity.SummaryInfo source) {
        SummaryInfo target = new SummaryInfo();
        List<Summary> targets = new ArrayList<>();
        for (com.iflytek.ebgai.ai.teacher.dataapi.entity.Summary item : source.getSummaries()) {
            Summary one = new Summary();
            one.setHeadline(item.getType());
            one.setOrder(item.getOrder());
            one.setContents(item.getContents());
            targets.add(one);
        }
        target.setSummaries(targets);
        target.setSummaryType(source.getSummaryType());
        return target;
    }
}
