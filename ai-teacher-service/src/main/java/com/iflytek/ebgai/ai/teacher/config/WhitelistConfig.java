package com.iflytek.ebgai.ai.teacher.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 白名单配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "auth.whitelist")
public class WhitelistConfig {
    /**
     * 白名单URL列表
     */
    private List<String> urls;
} 