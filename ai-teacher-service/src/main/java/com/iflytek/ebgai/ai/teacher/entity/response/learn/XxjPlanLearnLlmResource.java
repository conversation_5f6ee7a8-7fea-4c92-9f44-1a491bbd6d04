package com.iflytek.ebgai.ai.teacher.entity.response.learn;

import com.iflytek.ebgai.ai.teacher.entity.response.share.Intention;
import com.iflytek.ebgai.ai.teacher.entity.response.share.StorageInfo;
import lombok.Data;

@Data
public class XxjPlanLearnLlmResource {
    /**
     * 是否入库存储，默认false
     */
    private boolean isStorage;

    /**
     * 是否终止，终止后流式输出结束
     */
    private boolean isEnd;

    /**
     * 流式是否终止，终止后流式输出结束
     */
    private boolean isFluxEnd;

    /**
     * 是否动效
     */
    private boolean isDynamic;

    /**
     * 是否刷新点掌握度
     */
    private Boolean isUpdateMastery;

    /**
     * 原始大模型解析结果
     */
    private Intention intention;

    /**
     * 大模型原始输出存储信息
     */
    private StorageInfo storageInfo;
}
