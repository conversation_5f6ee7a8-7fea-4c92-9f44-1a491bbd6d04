package com.iflytek.ebgai.ai.teacher.dubbo.mapper;

import cn.hutool.core.bean.BeanUtil;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultData;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.PlanResultDataAO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author:huizhang68
 * @Date:2025/5/26
 */
public class PlanResultMapping {

    private volatile static PlanResultMapping instance;

    private PlanResultMapping() {
    }

    public static PlanResultMapping getInstance() {
        if (instance == null) {
            synchronized (PlanResultMapping.class) {
                if (instance == null) {
                    instance = new PlanResultMapping();
                }
            }
        }
        return instance;
    }

    public List<PlanResultDataAO> convert(List<PlanResultData> sourceList) {
        List<PlanResultDataAO> targets = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        for (PlanResultData source : sourceList) {
            PlanResultDataAO target = convert(source);
            if (target != null) {
                targets.add(target);
            }
        }
        return targets;
    }

    public PlanResultDataAO convert(PlanResultData source) {
        if (source == null) {
            return null;
        }
        PlanResultDataAO target = new PlanResultDataAO();
        BeanUtil.copyProperties(source, target);
        return target;
    }


}
