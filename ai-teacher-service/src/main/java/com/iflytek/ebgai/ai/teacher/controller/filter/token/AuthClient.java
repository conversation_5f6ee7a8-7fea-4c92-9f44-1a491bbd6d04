package com.iflytek.ebgai.ai.teacher.controller.filter.token;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.common.exception.BizException;
import com.iflytek.ebgai.ai.teacher.system.AuthProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

/**
 *
 * <AUTHOR> @data
 * @desc Authorization鉴权
 */
@Slf4j
@Data
public class AuthClient {
    private String tokenGetUrl = "/auth/v3/token";
    private String tokenCheckUrl = "/auth/v3/token_check";
    private RestTemplate restTemplate;
    private final boolean authEnable;

    public AuthClient(@Autowired RestTemplate restTemplate, AuthProperties properties) {
        Assert.notNull(restTemplate, "restTemplate 不能为空");
        Assert.hasLength(properties.getAuthUrl(), "authUrl 不能为空");
//        this.tokenGet = authUrl + tokenGet;
        this.restTemplate = restTemplate;
        this.tokenCheckUrl = properties.getAuthUrl();
        this.tokenGetUrl = properties.getTokenUrl();
        this.authEnable = properties.isEnable();
    }

    /**
     * 获取token，参考文档 http://tek.changyan.cn/plat-home?categoryId=887384026107609088
     */
    public String getTokenV3(String appId, String appKey) {
        String timeStamp = String.valueOf(System.currentTimeMillis());
        AuthBase base = new AuthBase();
        base.setAppId(appId);
        base.setTimestamp(timeStamp);
        AuthRequest request = new AuthRequest();
        request.setBase(base);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        //获得Authorization
        String sk = DigestUtils.md5Hex(appKey + timeStamp);
        String requestBodyStr = JSONObject.toJSONString(request);
        String sign = DigestUtils.md5Hex(sk + requestBodyStr);
        headers.set(HttpHeaders.AUTHORIZATION, sign);
        HttpEntity<String> entity = new HttpEntity<>(requestBodyStr, headers);
        ResponseEntity<String> response = restTemplate.exchange(tokenGetUrl, HttpMethod.POST, entity, String.class);

        if (response.getStatusCodeValue() == HttpStatus.OK.value() && response.getBody() != null) {
            AuthResponse authResponse = JSONObject.parseObject(response.getBody(), AuthResponse.class);
            if (authResponse.checkGetSuccess()) {
                return authResponse.getAccessToken();
            }
            log.error("token error:{}", response.getBody());
        }
        throw new BizException("获取token失败");
    }

    /**
     * token鉴权，参考代码 https://code.iflytek.com/osc/_source/EPD_TPD_AIP/ai-auth/-/code/
     */
    public void tokenCheckV3(String appId, String token) {
        //关闭鉴权，不处理
        if (!authEnable) {
            return;
        }

        Assert.hasLength(appId, "鉴权appId不能为空");
        Assert.hasLength(token, "鉴权token不能为空");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appId", appId);
        headers.set(HttpHeaders.AUTHORIZATION, token);
        HttpEntity<Object> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(tokenCheckUrl, HttpMethod.GET, entity, String.class);

        if (response.getStatusCodeValue() == HttpStatus.OK.value() && response.getBody() != null) {
            AuthResponse authResponse = JSONObject.parseObject(response.getBody(), AuthResponse.class);
            //鉴权成功
            if (authResponse.checkCheckSuccess()) {
                return;
            }
            log.error("鉴权接口：token check error:{}", response.getBody());
        }
        throw new BizException(RetCode.AUTH_ERROR.getCode(), "鉴权接口调用失败");
    }
}
