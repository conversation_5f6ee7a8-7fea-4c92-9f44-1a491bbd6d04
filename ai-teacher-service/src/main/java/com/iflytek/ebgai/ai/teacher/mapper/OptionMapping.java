package com.iflytek.ebgai.ai.teacher.mapper;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.Option;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireOptionAO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author:huizhang68
 * @Date:2025/5/29
 */
public class OptionMapping {
    private volatile static OptionMapping instance;

    private OptionMapping() {
    }

    public static OptionMapping getInstance() {
        if (instance == null) {
            synchronized (OptionMapping.class) {
                if (instance == null) {
                    instance = new OptionMapping();
                }
            }
        }
        return instance;
    }

    public List<Option> convert(List<QuestionnaireOptionAO> sourceList) {
        List<Option> targets = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        for (QuestionnaireOptionAO source : sourceList) {
            Option target = convert(source);
            if (target != null) {
                targets.add(target);
            }
        }
        return targets;
    }

    public Option convert(QuestionnaireOptionAO source) {
        if (source == null) {
            return null;
        }
        Option target = new Option();
        target.setOrder(source.getOrder());
        target.setContent(source.getContent());
        target.setRejects(source.getRejects());
        return target;
    }

}
