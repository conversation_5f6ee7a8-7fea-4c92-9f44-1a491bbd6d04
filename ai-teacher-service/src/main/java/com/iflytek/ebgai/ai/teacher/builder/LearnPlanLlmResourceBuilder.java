package com.iflytek.ebgai.ai.teacher.builder;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.entity.response.plan.CommonLlmResource;
import com.iflytek.ebgai.ai.teacher.entity.response.plan.KnowledgeClusterLlmResource;
import com.iflytek.ebgai.ai.teacher.entity.response.plan.LearnPlanLlmResource;
import com.iflytek.rec.teacher.domain.agent.response.Intention;
import com.iflytek.rec.teacher.domain.agent.response.Summary;
import com.iflytek.rec.teacher.domain.agent.response.SummaryInfo;
import com.iflytek.rec.teacher.domain.pojo.NodeInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName LearnPlanLlmResourceBuilder
 * @Date: 2025/6/2 15:31
 * @Description:
 */
@Slf4j
public class LearnPlanLlmResourceBuilder {
    public static LearnPlanLlmResource build(LlmResource llmResource) {
        Intention origIntention = llmResource.getIntention();
        if (null == origIntention) {
            log.error("返回参数不合理,Intention为空:{}", origIntention);
            throw new RuntimeException("返回参数不合理,Intention空");
        }
        List<NodeInfo> origNodeInfos = origIntention.getNodeInfos();
        if (CollectionUtils.isEmpty(origNodeInfos) && (llmResource.isEnd() || llmResource.isDynamic())) {
            log.warn("返回参数 List<NodeInfo>为空:{}", origNodeInfos);
        }

        //获得规划总结
        CommonLlmResource.SummaryInfo summaryInfo = getSummaryInfo(llmResource, origIntention);

        // 创建节点列表
        List<CommonLlmResource.NodeInfo> nodeInfos = getNodeInfos(origNodeInfos);


        // 创建Intention
        LearnPlanLlmResource.Intention intention = new LearnPlanLlmResource.Intention.Builder().results(origIntention.getResults()).fluxResults(origIntention.getFluxResults()).summaryInfo(summaryInfo).nodeInfos(nodeInfos).build();

        // 创建主对象
        LearnPlanLlmResource resource = new LearnPlanLlmResource.Builder().isEnd(llmResource.isEnd()).isFluxEnd(llmResource.isFluxEnd()).isDynamic(llmResource.isDynamic()).intention(intention).build();

        return resource;
    }

    @NotNull
    private static List<CommonLlmResource.NodeInfo> getNodeInfos(List<NodeInfo> origNodeInfos) {
        List<CommonLlmResource.NodeInfo> nodeInfos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(origNodeInfos)) {
            for (NodeInfo nodeInfo : origNodeInfos) {
                CommonLlmResource.NodeInfo node = new CommonLlmResource.NodeInfo.Builder().nodeId(nodeInfo.getNodeId()).nodeType(nodeInfo.getNodeType()).nodeAttribute(nodeInfo.getNodeAttribute()).nodeName("nodeName").learndBehavior(nodeInfo.getLearndBehavior()).learndTimes(nodeInfo.getLearndTimes()).catalogId(nodeInfo.getCatalogId()).order(nodeInfo.getOrder()).showState(nodeInfo.getShowState()).masterScore(nodeInfo.getMasterScore()).build();
                nodeInfos.add(node);
            }
        }
        return nodeInfos;
    }

    /**
     * 获得规划总结
     *
     * @param llmResource
     * @param origIntention
     * @return
     */
    @Nullable
    private static CommonLlmResource.SummaryInfo getSummaryInfo(LlmResource llmResource, Intention origIntention) {
        //设置规划学习最后的总结
        CommonLlmResource.SummaryInfo summaryInfo = null;
        SummaryInfo origSummaryInfo = origIntention.getSummaryInfo();
        if (llmResource.isEnd() && null != origSummaryInfo) {
            log.info("[TraceID:{}] 学习规划调用结束:{}", llmResource.getStorageInfo().getTraceId(), JSON.toJSONString(origSummaryInfo));
            List<Summary> origSummaries = origSummaryInfo.getSummaries();
            List<CommonLlmResource.SummaryInfo.Summary> summaryList = new ArrayList<>();
            for (Summary origSummary : origSummaries) {
                CommonLlmResource.SummaryInfo.Summary summary = new CommonLlmResource.SummaryInfo.Summary.Builder().contents(origSummary.getContents()).headline(origSummary.getHeadline()).order(origSummary.getOrder()).build();
                summaryList.add(summary);
            }
            summaryInfo = new CommonLlmResource.SummaryInfo.Builder().summaries(summaryList).summaryType(origSummaryInfo.getSummaryType()).build();
        }
        return summaryInfo;
    }
}
