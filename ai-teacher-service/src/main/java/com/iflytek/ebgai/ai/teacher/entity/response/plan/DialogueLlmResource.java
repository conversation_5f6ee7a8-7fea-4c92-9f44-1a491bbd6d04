package com.iflytek.ebgai.ai.teacher.entity.response.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;

@Data
public class DialogueLlmResource implements Serializable {

    @JsonProperty("isEnd")
    private boolean isEnd;
    @JsonProperty("isUpdateMastery")
    private boolean isUpdateMastery;
    private Intention intention;

    private DialogueLlmResource(Builder builder) {
        this.isEnd = builder.isEnd;
        this.intention = builder.intention;
        this.isUpdateMastery = builder.isUpdateMastery;
    }

    // 建造者模式
    public static class Builder {
        private boolean isEnd = false; // 默认false
        private Intention intention;
        private boolean isUpdateMastery;

        public Builder isEnd(boolean isEnd) {
            this.isEnd = isEnd;
            return this;
        }

        public Builder isUpdateMastery(boolean isUpdateMastery) {
            this.isUpdateMastery = isUpdateMastery;
            return this;
        }

        public Builder intention(Intention intention) {
            this.intention = intention;
            return this;
        }

        public DialogueLlmResource build() {
            return new DialogueLlmResource(this);
        }
    }

    // Intention内部类
    @Data
    public static class Intention {
        private int step;
        private int allSteps;
        private CommonLlmResource.NaireInfo naireInfo;
        private List<CommonLlmResource.NodeInfo> nodeInfos;

        private Intention(Builder builder) {
            this.step = builder.step;
            this.allSteps = builder.allSteps;
            this.naireInfo = builder.naireInfo;
            this.nodeInfos = builder.nodeInfos;
        }

        public static class Builder {
            private int step;
            private int allSteps = 5; // 默认5
            private CommonLlmResource.NaireInfo naireInfo;
            private List<CommonLlmResource.NodeInfo> nodeInfos;

            public Builder step(int step) {
                this.step = step;
                return this;
            }

            public Builder allSteps(int allSteps) {
                this.allSteps = allSteps;
                return this;
            }

            public Builder naireInfo(CommonLlmResource.NaireInfo naireInfo) {
                this.naireInfo = naireInfo;
                return this;
            }

            public Builder nodeInfos(List<CommonLlmResource.NodeInfo> nodeInfos) {
                this.nodeInfos = nodeInfos;
                return this;
            }

            public Intention build() {
                return new Intention(this);
            }
        }
    }
}