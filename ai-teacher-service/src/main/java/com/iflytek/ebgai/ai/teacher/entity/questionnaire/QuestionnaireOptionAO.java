package com.iflytek.ebgai.ai.teacher.entity.questionnaire;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 问题选项
 *
 * @Author:huizhang68
 * @Date:2025/5/29
 */
@Data
public class QuestionnaireOptionAO {

    @ApiModelProperty(value = "问题选项序号 按照1 2 3 4顺序对应A B C D展示给用户", required = true)
    private Integer order;

    @ApiModelProperty(value = "问题选项内容", required = true)
    private String content;

    /**
     * 与该选项互斥的选项 比如:当前选项=5,rejects=1,2,3,4  ,选择5后1,2,3,4要置灰
     * 默认为空
     */
    @ApiModelProperty(value = "与该选项互斥的选项order", required = true)
    private List<Integer> rejects;
}
