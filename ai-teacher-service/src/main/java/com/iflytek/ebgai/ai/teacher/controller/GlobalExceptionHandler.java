package com.iflytek.ebgai.ai.teacher.controller;


import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.common.exception.BaseException;
import com.iflytek.ebgai.ai.teacher.context.ContextUtil;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 文件名: GlobalExceptionHandler
 * 创建者: boliu
 * 创建时间:2024/12/17 15:47
 * 描述:
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(ParamValidateException.class)
    public void handleParamValidateException(ParamValidateException ex, HttpServletResponse response) throws IOException {
        // 重置响应（关键步骤）
        response.reset();

        // 设置正确的响应类型和状态
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_OK); // 强制设为200
        response.setCharacterEncoding("utf-8");

        // 安全处理：只返回业务相关的错误信息，过滤掉任何可能的系统信息
        String safeMessage = RetCode.INPUT_PARAMETERS_MISSING.getDesc();

        // 直接写入JSON响应
        String json = String.format("{\"code\":\"%s\",\"message\":\"%s\",\"traceId\":\"%s\",\"data\":\"null\"}", ex.getCode(), safeMessage, ex.getTraceId());

        response.getWriter().write(json);
        response.getWriter().flush();
    }

    @ExceptionHandler(Exception.class)
    public CommonResponse handleAllExceptions(Exception ex) {
        if (ex instanceof BaseException) {
            RetCode retCode = RetCode.getByCode(((BaseException) ex).getCode());
            if (null != retCode) {
                return new CommonResponse(retCode.getCode(), ex.getMessage(), ContextUtil.getTraceId());
            }
        }

        return new CommonResponse(RetCode.UNKNOWN_ERROR.getCode(), ex.getMessage(), ContextUtil.getTraceId());

    }


}
