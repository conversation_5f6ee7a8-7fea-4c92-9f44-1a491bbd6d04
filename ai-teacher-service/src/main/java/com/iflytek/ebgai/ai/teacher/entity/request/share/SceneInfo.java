package com.iflytek.ebgai.ai.teacher.entity.request.share;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import lombok.Data;

@Data
public class SceneInfo {
    @NotBlank(message = "学科代码不能为空")
    private String subjectCode;

    @NotBlank(message = "学段编码不能为空")
    private String phaseCode;

    private String bookCode;

    private String areaCode;

    @NotBlank(message = "用户ID不能为空")
    private String userId;

    private String userName;

    @Pattern(regexp = "SYNC_TUTORING", message = "学习场景只允许传SYNC_TUTORING")
    private String studyCode = "SYNC_TUTORING";

    private String pressCode;
    @NotBlank(message = "图谱版本不能为空")
    private String graphVersion;
}