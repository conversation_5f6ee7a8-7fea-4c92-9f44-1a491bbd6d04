package com.iflytek.ebgai.ai.teacher.service.plan;

import com.iflytek.ebgai.ai.teacher.entity.plan.confirmajust.PlanResult;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;

import java.util.List;


public interface PlanResultServiceFacade {

    /**
     * 规划结果表存储
     *
     * @param llmResource
     */
    void savePlanResult(LlmResource llmResource, String traceId);

    void batchUpdatePlanResult(List<PlanResult> planResults, String traceId);
}
