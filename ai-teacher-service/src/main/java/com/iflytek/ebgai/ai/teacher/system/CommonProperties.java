package com.iflytek.ebgai.ai.teacher.system;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@Getter
@RefreshScope
public class CommonProperties {

    @Value("${tlb.enable:true}")
    private boolean tlbEnable;

    @Value("${tlb.serviceTag:}")
    private String serverTag;

    @Value("${engine.http_url:}")
    private String abilityHttpUrl;

    @Value("${engine.ws_url:}")
    private String abilityWebsocketUrl;

}
