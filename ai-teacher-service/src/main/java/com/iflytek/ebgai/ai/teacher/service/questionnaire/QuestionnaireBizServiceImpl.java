package com.iflytek.ebgai.ai.teacher.service.questionnaire;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.builder.LlmRequestBuilder;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.consts.HeaderConstants;
import com.iflytek.ebgai.ai.teacher.consts.SessionInfoConstants;
import com.iflytek.ebgai.ai.teacher.dataapi.AITeacherDataHub;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireAnswerData;
import com.iflytek.ebgai.ai.teacher.dataapi.service.QuestionnaireAnswerService;
import com.iflytek.ebgai.ai.teacher.dataapi.service.UserPortraitService;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireAnswerRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireExplainResponseAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireResponseAO;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.entity.request.share.InferBody;
import com.iflytek.ebgai.ai.teacher.factory.LlmType;
import com.iflytek.ebgai.ai.teacher.mapper.*;
import com.iflytek.ebgai.ai.teacher.service.AbstractEngineFacade;
import com.iflytek.ebgai.ai.teacher.service.plan.StorageInfoServiceFacade;
import com.iflytek.rec.teacher.interfaces.param.*;
import com.iflytek.skyline.brave.TraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 问卷前端接口服务
 *
 * @Author:huizhang68
 * @Date:2025/5/28
 */
@Slf4j
@Service
public class QuestionnaireBizServiceImpl implements QuestionnaireBizService {

    @Autowired
    private AbstractEngineFacade engineFacade;
    @Autowired
    private QuestionnaireAnswerService answerService;
    @Autowired
    private UserPortraitService userPortraitService;

    /**
     * Type为工作流时，指定工作流ID
     */
    @Value("${banxue.engine.deepseekrv3.workflowid:682554ee5cf2002c9fb76e1c}")
    private String deepseekrV3Workflowid;

    /**
     * 区分快捷指令类型 0-工作流；1-工具；2-知识库
     */
    @Value("${banxue.engine.shortcutdata.shortcuttype:0}")
    private Integer shortcuttype;

    /**
     * 智能体ID
     */
    @Value("${chat.app.agent.id:682557525cf2002c9fb76e1d}")
    private String agentId;

    /**
     * 调试标记位，标记是否测试场景，影响返回内容，和数据回流
     */
    @Value("${banxue.engine.body.debugmode:false}")
    private Boolean debugMode;

    /**
     * 图谱版本（数据层定义）服务透传
     */
    @Value("${skylab.data.api.graph.cacheVersion:20250606_001}")
    private String graphVersion;

    private final TraceUtils traceUtils;

    public QuestionnaireBizServiceImpl(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    @Override
    public QuestionnaireResponseAO getQuestionnaire(QuestionnaireRequestAO requestAO, Map<String, String> headers) throws Exception {
        final String traceId = headers.get(HeaderConstants.X_TRACE_ID);
        QuestionnaireRequest questionnaireRequest = QuestionnaireRequestMapping.getInstance().convert(requestAO, headers, graphVersion);
        log.info("[TraceID:{}] [GET_QUESTIONNAIRE][ENGINE][REQUEST] {}", traceId, JSON.toJSONString(questionnaireRequest));
        traceUtils.record("questionnaireEngineRequest", questionnaireRequest);
        QuestionnaireResponse egResp = engineFacade.questionnaire(questionnaireRequest);
        log.info("[TraceID:{}] [GET_QUESTIONNAIRE][ENGINE][RESPONSE] {}", traceId, JSON.toJSONString(egResp));
        return QuestionnaireResponseAOMapping.getInstance().convert(egResp);
    }

    @Override
    public void answer(QuestionnaireAnswerRequestAO requestAO, Map<String, String> headers) throws Exception {
        final String traceId = headers.get(HeaderConstants.X_TRACE_ID);
        QuestionnaireAnswerData answerData = QuestionnaireAnswerDataMapping.getInstance().convert(requestAO, headers);
        answerService.insert(answerData);
        if (null == requestAO.getIsEnd() || !requestAO.getIsEnd()) {
            return;
        }
        // 用户画像计算
        UserPortraitRequest engineRequest = UserPortraitRequestMapping.getInstance().convert(requestAO, headers, graphVersion);
        traceUtils.record("userPortraitEngineRequest", engineRequest);
        log.info("[TraceID:{}] [ANSWER][ENGINE][REQUEST] {}", traceId, JSON.toJSONString(engineRequest));
        UserPortraitResponse engineResponse = engineFacade.userPortrait(engineRequest);
        log.info("[TraceID:{}] [ANSWER][ENGINE][RESPONSE] {}", traceId, JSON.toJSONString(engineResponse));
        if (engineResponse == null) {
            log.error("[TraceID:{}] [ANSWER][ENGINE][REQUEST] is empty", traceId);
            return;
        }
        //先按用户ID查询，如果存该用户的历史画像则删除
        com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitRequest userPortraitRequest = new com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitRequest().setUserId(engineRequest.getSceneInfo().getUserId());
        List<UserPortraitData> existsUserPortraitData = userPortraitService.query(userPortraitRequest);
        if (CollectionUtils.isNotEmpty(existsUserPortraitData)) {
            String userId = engineRequest.getSceneInfo().getUserId();
            log.info("[TraceID:{}] 删除用户 {} 的历史画像", traceId, userId);
            userPortraitService.delete(userId);
        }
        // 用户画像存储
        UserPortraitData entityData = UserPortraitDataMapping.getInstance().convert(engineResponse, traceId);
        userPortraitService.insert(entityData);
        log.info("[TraceID:{}] [ANSWER][END] {}", traceId, JSON.toJSONString(requestAO));
    }


    @Override
    public CommonResponse<QuestionnaireExplainResponseAO> questionnaireExplain(XxjLlmRequest xxjLlmRequest, Map<String, String> headers) {
        if (SessionInfoConstants.LoadDBEnable.LOAD_DB == xxjLlmRequest.getSessionInfo().getLoadDBEnable()) {
            log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][LOAD_DB][START]", headers.get(HeaderConstants.X_TRACE_ID));
            CommonResponse<QuestionnaireExplainResponseAO> response = questionnaireExplainWithDB(headers);
            log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][LOAD_DB][RETURN] {}", headers.get(HeaderConstants.X_TRACE_ID), JSON.toJSONString(response));
            return response;
        }
        log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][ENGINE]", headers.get(HeaderConstants.X_TRACE_ID));
        CommonResponse<QuestionnaireExplainResponseAO> engineResponse = questionnaireExplainWithEngine(xxjLlmRequest, headers);
        log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][ENGINE][RETURN] {}", headers.get(HeaderConstants.X_TRACE_ID), JSON.toJSONString(engineResponse));
        return engineResponse;
    }

    private CommonResponse<QuestionnaireExplainResponseAO> questionnaireExplainWithDB(Map<String, String> headers) {
        CommonResponse<QuestionnaireExplainResponseAO> response = null;
        final String traceId = headers.get(HeaderConstants.X_TRACE_ID);
        try {
            StorageInfoRequest dataApiReq = new StorageInfoRequest();
            dataApiReq.setTraceId(traceId);
            dataApiReq.setUserId(headers.get(HeaderConstants.X_USER_ID));
            dataApiReq.setFunctionCode(BizConstants.FunctionCode.QUSNAIRE_EXPLAIN.getCode());
            log.debug("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][LOAD_DB][QUERY]{}", traceId, JSON.toJSONString(dataApiReq));
            StorageInfoData dataApiResp = AITeacherDataHub.getStorageInfoService().queryLatest(dataApiReq);
            log.debug("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][LOAD_DB][QUERY_OUT]", traceId);
            if (Objects.isNull(dataApiResp)) {
                log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][LOAD_DB][EMPTY]", traceId);
                response = new CommonResponse<>(RetCode.QUESTIONNAIRE_DB_QUERY_EMPTY.getCode(), RetCode.QUESTIONNAIRE_DB_QUERY_EMPTY.getDesc(), headers.get(HeaderConstants.X_TRACE_ID));
                return response;
            }
            response = new CommonResponse<>();
            QuestionnaireExplainResponseAO data = QuestionnaireExplainResponseAOMapping.getInstance().convert(dataApiResp);
            response.setData(data);
            response.setTraceId(traceId);
            return response;
        } catch (Exception e) {
            log.error("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][LOAD_DB][ERROR] {} ", traceId, e.getMessage(), e);
            response = new CommonResponse<>(RetCode.QUESTIONNAIRE_DB_QUERY_ERROR.getCode(), RetCode.QUESTIONNAIRE_DB_QUERY_ERROR.getDesc(), traceId);
            return response;
        }
    }

    private CommonResponse<QuestionnaireExplainResponseAO> questionnaireExplainWithEngine(XxjLlmRequest xxjLlmRequestAO, Map<String, String> headers) {
        CommonResponse<QuestionnaireExplainResponseAO> response = null;
        final String traceId = headers.get(HeaderConstants.X_TRACE_ID);
        try {
            LlmRequest llmRequest = buildQuestionnaireExplainLlmRequest(xxjLlmRequestAO, headers);
            log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][ENGINE][START] {}", traceId, JSON.toJSONString(llmRequest));
            traceUtils.record("explainQuestionnaireEngineRequest", llmRequest);
            Flux<LlmResource> sseFlux = engineFacade.explainQuestionnaire(llmRequest, LlmType.PORTRAIT_INFER);
            Mono<List<LlmResource>> list = sseFlux.collectList();
            List<LlmResource> llmList = list.block();
            if (CollectionUtils.isEmpty(llmList)) {
                response = new CommonResponse<>(RetCode.QUESTIONNAIRE_ENGINE_QUERY_EMPTY.getCode(), RetCode.QUESTIONNAIRE_ENGINE_QUERY_EMPTY.getDesc(), traceId);
                log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][ENGINE][EMPTY]", traceId);
                return response;
            }
            LlmResource item = llmList.get(0);
            log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][ENGINE][SUCCESS] {}", traceId, JSON.toJSONString(item));
            if (item.isEnd() || item.isFluxEnd()) {
                StorageInfoData storageInfoData = QuestionnaireStorageInfoDataMapping.getInstance().convert(item, headers);
                // 问卷解释存储
                AITeacherDataHub.getStorageInfoService().insert(storageInfoData);
                response = new CommonResponse<>();
                QuestionnaireExplainResponseAO data = QuestionnaireExplainResponseAOMapping.getInstance().convert(item);
                response.setData(data);
                response.setTraceId(headers.get(HeaderConstants.X_TRACE_ID));
            }
        } catch (Exception e) {
            log.error("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][ENGINE][ERROR] {} ", traceId, e.getMessage(), e);
            response = new CommonResponse<>(RetCode.QUESTIONNAIRE_ENGINE_QUERY_ERROR.getCode(), RetCode.QUESTIONNAIRE_ENGINE_QUERY_ERROR.getDesc(), traceId);
        }
        return response;
    }

    private LlmRequest buildQuestionnaireExplainLlmRequest(XxjLlmRequest xxjLlmRequestAO, Map<String, String> headers) {
        XxjLlmRequest llmDTO = new XxjLlmRequest();
        com.iflytek.ebgai.ai.teacher.entity.request.share.SceneInfo sceneInfoDTO = new com.iflytek.ebgai.ai.teacher.entity.request.share.SceneInfo();
        com.iflytek.ebgai.ai.teacher.entity.request.share.SceneInfo sceneInfoAO = xxjLlmRequestAO.getSceneInfo();
        BeanUtil.copyProperties(sceneInfoAO, sceneInfoDTO);
        sceneInfoDTO.setGraphVersion(graphVersion);
        llmDTO.setSceneInfo(sceneInfoDTO);
        com.iflytek.ebgai.ai.teacher.entity.request.share.SessionInfo sessionInfoDTO = new com.iflytek.ebgai.ai.teacher.entity.request.share.SessionInfo();
        com.iflytek.ebgai.ai.teacher.entity.request.share.SessionInfo sessionInfoAO = xxjLlmRequestAO.getSessionInfo();
        BeanUtil.copyProperties(sessionInfoAO, sessionInfoDTO);
        if (StringUtils.isEmpty(sessionInfoDTO.getTraceId())) {
            sessionInfoDTO.setTraceId(headers.get(HeaderConstants.X_TRACE_ID));
        }
        llmDTO.setSessionInfo(sessionInfoDTO);
        InferBody inferBodyDTO = new InferBody();
        InferBody inferBodyAO = xxjLlmRequestAO.getInferBody();
        BeanUtil.copyProperties(inferBodyAO, inferBodyDTO);
        llmDTO.setInferBody(inferBodyDTO);

        return LlmRequestBuilder.create().withXxjLlmRequest(llmDTO).withWorkflowId(deepseekrV3Workflowid).withHeaders(headers).withShortcutType(shortcuttype).withAgentId(agentId).withDebugMode(debugMode).withFunctionCode(BizConstants.FunctionCode.QUSNAIRE_EXPLAIN.getCode()).build();
    }
}
