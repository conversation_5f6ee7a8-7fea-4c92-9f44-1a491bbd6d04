package com.iflytek.ebgai.ai.teacher.entity.request.share;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.iflytek.ebgai.ai.teacher.consts.SessionInfoConstants;
import lombok.Data;

@Data
public class SessionInfo {
    @NotBlank(message = "traceId不能为空")
    private String traceId;

    @NotNull(message = "roundRecNum不能为空")
    @Min(value = 1, message = "roundRecNum最小值为1")
    private Integer roundRecNum;
    /**
     * 本轮id 一轮问卷流程内唯一,同问卷-接口1
     */
    private String roundId;
    /**
     * 是否查询db
     * 场景：问卷解释
     * 1：查询db；0：查询引擎模型
     * 默认：0
     *
     * @see SessionInfoConstants.LoadDBEnable
     */
    @NotBlank(message = "loadDBEnable不能为空")
    private Integer loadDBEnable = 0;
}