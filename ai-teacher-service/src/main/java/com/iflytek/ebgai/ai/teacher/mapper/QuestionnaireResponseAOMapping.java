package com.iflytek.ebgai.ai.teacher.mapper;


import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireOptionAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireResponseAO;
import com.iflytek.rec.teacher.domain.model.NaireInfo;
import com.iflytek.rec.teacher.domain.pojo.Option;
import com.iflytek.rec.teacher.interfaces.param.QuestionnaireResponse;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Auther: huizhang68
 * @date: 2025/6/19 21:14
 */
public class QuestionnaireResponseAOMapping {

    private volatile static QuestionnaireResponseAOMapping instance;

    private QuestionnaireResponseAOMapping() {
    }

    public static QuestionnaireResponseAOMapping getInstance() {
        if (instance == null) {
            synchronized (QuestionnaireResponseAOMapping.class) {
                if (instance == null) {
                    instance = new QuestionnaireResponseAOMapping();
                }
            }
        }
        return instance;
    }

    public QuestionnaireResponseAO convert(QuestionnaireResponse egResp) {
        if (egResp == null) {
            return null;
        }
        QuestionnaireResponseAO target = new QuestionnaireResponseAO();
        target.setEnd(egResp.isEnd());
        target.setStep(egResp.getStep());
        target.setAllSteps(egResp.getAllSteps());
        if (egResp.getNaireInfo() != null) {
            List<QuestionnaireOptionAO> options = convert(egResp.getNaireInfo().getOptions());
            QuestionnaireResponseAO.NaireInfo naireInfoAO = convert(egResp.getNaireInfo(), options);
            target.setNaireInfo(naireInfoAO);
        }
        return target;
    }

    private List<QuestionnaireOptionAO> convert(List<Option> sources) {
        if (CollectionUtils.isEmpty(sources)) {
            return Collections.emptyList();
        }
        List<QuestionnaireOptionAO> targets = new ArrayList<>();
        for (Option item : sources) {
            QuestionnaireOptionAO target = convert(item);
            if (target != null) {
                targets.add(target);
            }
        }
        return targets;
    }

    private QuestionnaireOptionAO convert(Option source) {
        if (source == null) {
            return null;
        }
        QuestionnaireOptionAO target = new QuestionnaireOptionAO();
        target.setOrder(source.getOrder());
        target.setContent(source.getContent());
        target.setRejects(source.getRejects());
        return target;
    }

    private QuestionnaireResponseAO.NaireInfo convert(NaireInfo egInfo, List<QuestionnaireOptionAO> options) {
        QuestionnaireResponseAO.NaireInfo target = new QuestionnaireResponseAO.NaireInfo();
        target.setIndex(egInfo.getIndex());
        target.setDimension(egInfo.getDimension());
        target.setRoundId(egInfo.getRoundId());
        target.setQuestionContent(egInfo.getQuestionContent());
        target.setOptions(options);
        target.setRadio(egInfo.isRadio());
        target.setChoiceNum(egInfo.getChoiceNum());
        return target;

    }
}
