package com.iflytek.ebgai.ai.teacher.entity.response.share;// NodeInfo.java

import lombok.Data;

/**
 * 知识点信息
 */
@Data
public class NodeInfo {
    /**
     * 规划排序
     */
    private Integer order;

    /**
     * 点ID
     */
    private String nodeId;

    /**
     * 点名称
     */
    private String nodeName;

    /**
     * 点类型（ANCHOR_POINT）
     */
    private String nodeType;

    /**
     * 点属性（CENTRAL_POINT, EXTENSION_POINT, OTHERS）
     */
    private String nodeAttribute;

    /**
     * 学习行为（LEARN, PRACTICE, OTHERS）
     */
    private String learndBehavior;

    /**
     * 学习时长（分钟）
     */
    private Integer learndTimes;

    /**
     * 显示状态（LIGHT, DARK, MAINTAIN）
     */
    private String showState = "MAINTAIN";

    /**
     * 画像得分(N)
     */
    private Double masterScore;

    /**
     * 目录ID
     */
    private String catalogId;
}