package com.iflytek.ebgai.ai.teacher.system;

import com.iflytek.ebgai.ai.teacher.config.WhitelistConfig;
import com.iflytek.ebgai.ai.teacher.context.ContextFilter;
import com.iflytek.ebgai.ai.teacher.controller.filter.token.AuthClient;
import com.iflytek.ebgai.ai.teacher.controller.filter.token.TokenAuthFilter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @data 2024/10/22
 */
@Configuration
public class WebConfig {
    @Bean(name = "authRestTemplate")
    public RestTemplate authRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        //单位为ms
        factory.setReadTimeout(6000);
        factory.setConnectTimeout(2000);
        RestTemplate restTemplate = new RestTemplate(factory);
        // 支持中文编码
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }

    @Bean
    public AuthClient authClient(@Qualifier("authRestTemplate") RestTemplate restTemplate, AuthProperties authConfig) {
        return new AuthClient(restTemplate, authConfig);
    }

    @Bean
    public FilterRegistrationBean<TokenAuthFilter> tokenAuthFilter(AuthClient authClient, WhitelistConfig whitelistConfig) {
        FilterRegistrationBean<TokenAuthFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new TokenAuthFilter(authClient, whitelistConfig.getUrls()));
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(1);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<ContextFilter> contextFilter() {
        FilterRegistrationBean<ContextFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ContextFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(2);
        return registrationBean;
    }
}
