package com.iflytek.ebgai.ai.teacher.entity.request;

import com.iflytek.ebgai.ai.teacher.entity.request.share.InferBody;
import com.iflytek.ebgai.ai.teacher.entity.request.share.SceneInfo;
import com.iflytek.ebgai.ai.teacher.entity.request.share.SessionInfo;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class XxjLlmRequest implements Serializable {
    @Valid
    @NotNull(message = "sceneInfo不能为空")
    private SceneInfo sceneInfo;

    //    @Valid
    private SessionInfo sessionInfo;

    @Valid
    @NotNull(message = "inferBody不能为空")
    private InferBody inferBody;
}