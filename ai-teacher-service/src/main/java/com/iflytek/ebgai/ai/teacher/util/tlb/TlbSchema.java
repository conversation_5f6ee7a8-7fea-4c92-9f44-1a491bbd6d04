package com.iflytek.ebgai.ai.teacher.util.tlb;

import lombok.Getter;

/**
 * <AUTHOR>
 * @data 2024/10/24
 * @desc tlb请求协议枚举
 */

@Getter
public enum TlbSchema {

    HTTP("http", "http://%s%s"), HTTPS("https", "https://%s%s"), WS("ws", "ws://%s%s"), WSS("wss", "wss://%s%s"),
    ;

    String name;
    String pattern;

    TlbSchema(String name, String pattern) {
        this.name = name;
        this.pattern = pattern;
    }

}
