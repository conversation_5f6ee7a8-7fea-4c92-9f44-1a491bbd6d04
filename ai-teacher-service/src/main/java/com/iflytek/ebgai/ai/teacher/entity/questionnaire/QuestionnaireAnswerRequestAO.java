package com.iflytek.ebgai.ai.teacher.entity.questionnaire;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户回答存储请求实体
 *
 * @Author:huizhang68
 * @Date:2025/5/29
 */
@Data
@Builder
@ApiModel(description = "用户回答存储请求实体")
public class QuestionnaireAnswerRequestAO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "场景信息不能为空")
    @ApiModelProperty(value = "场景信息", required = true)
    private QuestionnaireRequestAO.SceneInfoAO sceneInfo;
    @ApiModelProperty(value = "问卷是否建议终止 拿到终止标识不展示当前问卷 默认false", required = true)
    private Boolean isEnd = false;
    @NotNull(message = "问卷内容和回答不能为空")
    @ApiModelProperty(value = "问卷内容和回答", required = true)
    private AnswerInfoAO answerInfo;

    /**
     * 问卷内容和回答
     */
    @Data
    @Builder
    public static class AnswerInfoAO {

        @ApiModelProperty(value = "当前请求第几个问卷。已推问卷数=roundRecNum-1 从1开始", required = true)
        private Integer roundRecNum;

        @ApiModelProperty(value = "本轮id 一轮问卷流程内唯一", required = true)
        private String roundId;

        @ApiModelProperty(value = "在问卷中的索引 在问卷中固定顺序 和入参一样roundRecNum", required = true)
        private Integer index;

        @ApiModelProperty(value = "问卷维度", required = true, notes = "枚举值：USER_RANK(成绩排名), WORK_STATE(作业感受), STUDY_TARGET(学习目标), CORE_LITERACY(学习素养)")
        private String dimension;

        @ApiModelProperty(value = "选项信息", required = true)
        private List<QuestionnaireOptionAO> options;


    }
}
