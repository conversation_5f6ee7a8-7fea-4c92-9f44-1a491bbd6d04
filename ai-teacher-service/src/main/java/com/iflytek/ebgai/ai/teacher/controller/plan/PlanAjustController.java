package com.iflytek.ebgai.ai.teacher.controller.plan;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import com.iflytek.rec.teacher.domain.pojo.PlanAjust;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.rec.teacher.interfaces.param.PlanAjustRequest;
import com.iflytek.rec.teacher.interfaces.param.PlanAjustResponse;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/plan", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
public class PlanAjustController extends BasePlanController {

    private static final String TRACE_ID_HEADER = "x-trace-id";
    private static final String USER_ID_HEADER = "x-user-id";
    private static final String USER_NAME_HEADER = "x-user-name";
    private final TraceUtils traceUtils;

    public PlanAjustController(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    @ServiceRequestMetrics(desc = "规划调整")
    @PostMapping(value = "/ajust", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    @ApiOperation("规划调整")
    @SkylineTraceStarter(typePrefix = "planAjust#", isSync4Request = false)
    public CommonResponse<?> planAjust(@RequestBody PlanAjustRequest planAjustRequest, HttpServletRequest request) throws IOException {
        //参数校验
        checkInputParameters(planAjustRequest, request);
        final String traceId = HttpUtils.getTraceId(request);
        log.info("[TraceID:{}] 规划调整请求学习机入参:{}", traceId, JSON.toJSONString(planAjustRequest));
        // 1. 填充场景信息
        final Map<String, String> headers = HttpUtils.getHeadersMap(request);
        enrichSceneInfo(planAjustRequest.getSceneInfo(), headers);

        // 2. 处理会话信息
        planAjustRequest.setSessionInfo(buildSessionInfo(planAjustRequest.getSessionInfo(), traceId));

        try {
            traceUtils.record("planAjustEngineRequest", planAjustRequest);
            log.info("[TraceID:{}] 规划调整请求开始: {}", traceId, JSON.toJSONString(planAjustRequest));
            long startTime = System.currentTimeMillis();
            PlanAjustResponse responseData = engineFacade.planAjust(planAjustRequest);
            long cost = System.currentTimeMillis() - startTime;
            traceUtils.record("planAjustEngineCost", Maps.of("cost", cost, "traceId", traceId));
            log.info("[TraceID:{}] 课前对话引擎接口耗时: {} ms", traceId, cost);
            traceUtils.record("planAjustEngineResponse", responseData);
            return buildSuccessResponse(responseData, traceId);
        } catch (EngineException ex) {
            traceUtils.recordErr("planAjustEngineException", ex);
            log.error("[TraceID:{}] 引擎接口调用异常：[Code:{}] - {}", traceId, ex.getErrorCode(), ex.getMessage(), ex);
            return buildEngineErrorResponse(ex, traceId);
        } catch (Error error) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            error.printStackTrace(pw);  // 将堆栈写入 PrintWriter
            String fullStackTrace = sw.toString();
            traceUtils.recordErr("planAjustEngineError", fullStackTrace);
            log.error("[TraceID:{}] 引擎接口调用错误：{}", traceId, fullStackTrace);
            return handleError(error, traceId);
        } catch (Exception ex) {
            traceUtils.recordErr("planAjustException", ex);
            log.error("[TraceID:{}] 规划调整异常：{}", traceId, ex);
            return buildSystemErrorResponse(ex, traceId);
        }
    }

    /**
     * 填充场景信息
     */
    private void enrichSceneInfo(SceneInfo sceneInfo, Map<String, String> headers) {
        if (sceneInfo == null) {
            log.error("[TraceID:{}] 调用规划调整引擎接口参数不正确:{}", sceneInfo);
            return;
        } else {
            sceneInfo.setUserId(headers.get(USER_ID_HEADER));
            sceneInfo.setUserName(headers.get(USER_NAME_HEADER));
            sceneInfo.setBizCode(BizConstants.BIZ_CODE_DEFAULT);
            sceneInfo.setBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode());
            sceneInfo.setFunctionCode(BizConstants.FunctionCode.LEARNINGPLAN_PLAN.getCode());
            sceneInfo.setStudyCode(BizConstants.StudyCodes.SYNC_TUTORING.getCode());
            sceneInfo.setGraphVersion(graphVersion);
        }


    }

    /**
     * 构建会话信息
     */
    private SessionInfo buildSessionInfo(SessionInfo sessionInfo, String traceId) {
        if (sessionInfo == null) {
            sessionInfo = new SessionInfo();
        }
        sessionInfo.setTraceId(traceId);
        return sessionInfo;
    }

    /**
     * 构建成功响应
     */
    private CommonResponse<PlanAjustResponse> buildSuccessResponse(PlanAjustResponse data, String traceId) {
        CommonResponse<PlanAjustResponse> response = new CommonResponse<>();
        response.setTraceId(traceId);
        response.setData(data);
        return response;
    }

    /**
     * 构建引擎异常响应
     */
    private CommonResponse<PlanAjustResponse> buildEngineErrorResponse(EngineException ex, String traceId) {
        return new CommonResponse<>(String.valueOf(ex.getErrorCode()), ex.getMessage(), traceId);
    }

    /**
     * 构建系统异常响应
     */
    private CommonResponse<PlanAjustResponse> buildSystemErrorResponse(Exception ex, String traceId) {
        return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(),  // 假设RetCode中有ERROR常量
                "系统内部错误: " + ex.getMessage(), traceId);
    }

    private void checkInputParameters(PlanAjustRequest planAjustRequest, HttpServletRequest request) throws IOException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = checkHeaders(request);
        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();

        if (planAjustRequest.getSceneInfo() == null) {
            missingParams.add("sceneInfo");
        } else {
            SceneInfo sceneInfo = planAjustRequest.getSceneInfo();
            if (sceneInfo.getSubjectCode() == null || sceneInfo.getSubjectCode().isEmpty()) {
                missingParams.add("sceneInfo.subjectCode");
            }
            if (sceneInfo.getPhaseCode() == null || sceneInfo.getPhaseCode().isEmpty()) {
                missingParams.add("sceneInfo.phaseCode");
            }
            if (sceneInfo.getBookCode() == null || sceneInfo.getBookCode().isEmpty()) {
                missingParams.add("sceneInfo.bookCode");
            }
            if (sceneInfo.getPressCode() == null || sceneInfo.getPressCode().isEmpty()) {
                missingParams.add("sceneInfo.pressCode");
            }
        }

        if (StringUtils.isEmpty(planAjustRequest.getCatalogId())) {
            missingParams.add("catalogId");
        }

        if (planAjustRequest.getPlanAjust() == null) {
            missingParams.add("planAjust");
        } else {
            PlanAjust planAjust = planAjustRequest.getPlanAjust();
            if (planAjust.getDiffAjust() == null || planAjust.getDiffAjust().isEmpty()) {
                missingParams.add("planAjust.diffAjust");
            }
            if (planAjust.getTimeAjust() == null || planAjust.getTimeAjust().isEmpty()) {
                missingParams.add("planAjust.timeAjust");
            }
            if (planAjust.getPlanTimes() < 0) {
                missingParams.add("planAjust.planTimes");
            }
        }

        // 3. 如果有缺失参数则发送错误响应
        if (!missingHeaders.isEmpty() || !missingParams.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("参数校验失败: ");

            if (!missingHeaders.isEmpty()) {
                errorMsg.append("缺失请求头 - ").append(String.join(", ", missingHeaders));
            }

            if (!missingParams.isEmpty()) {
                if (!missingHeaders.isEmpty()) {
                    errorMsg.append("; ");
                }
                errorMsg.append("缺失请求参数 - ").append(String.join(", ", missingParams));
            }

            errorInfo = errorMsg.toString();

        }
        if (StringUtils.isNotEmpty(errorInfo)) {
            log.error("[TraceID:{}] {}", HttpUtils.getTraceId(request), errorInfo);
            throw new ParamValidateException(RetCode.INPUT_PARAMETERS_MISSING.getCode(), errorInfo, request.getHeader("x-trace-id"));
        }
    }
}