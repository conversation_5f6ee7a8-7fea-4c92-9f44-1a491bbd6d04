package com.iflytek.ebgai.ai.teacher.controller;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.consts.HeaderConstants;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireAnswerRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireExplainResponseAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireResponseAO;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.service.questionnaire.QuestionnaireBizService;
import com.iflytek.ebgai.ai.teacher.service.questionnaire.QuestionnaireCheckParamService;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @Author:huizhang68
 * @Date:2025/5/28
 */
@Slf4j
@Api(tags = "问卷相关接口")
@RestController
@RequestMapping(value = "/api/v1/questionnaire", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
public class QuestionnaireController {

    @Autowired
    private QuestionnaireBizService service;
    @Autowired
    private QuestionnaireCheckParamService checkParamService;

    private final TraceUtils traceUtils;

    public QuestionnaireController(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    @ServiceRequestMetrics(desc = "获取问卷")
    @ApiOperation("获取问卷")
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    @SkylineTraceStarter(typePrefix = "getQuestionnaire#", isSync4Request = false)
    public CommonResponse<QuestionnaireResponseAO> getQuestionnaire(@RequestBody QuestionnaireRequestAO requestAO, HttpServletRequest request) {
        Map<String, String> headers = HttpUtils.getHeadersMap(request);
        final String traceId = headers.get(HeaderConstants.X_TRACE_ID);
        log.info("[TraceID:{}] [GET_QUESTIONNAIRE][REQUEST] {}", traceId, JSON.toJSONString(requestAO));
        checkParamService.checkInputParameters(requestAO, request);
        try {
            QuestionnaireResponseAO ao = service.getQuestionnaire(requestAO, headers);
            CommonResponse<QuestionnaireResponseAO> response = new CommonResponse<QuestionnaireResponseAO>().success(traceId);
            response.setData(ao);
            log.info("[TraceID:{}] [GET_QUESTIONNAIRE][RESPONSE] {}", traceId, JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            if (e instanceof EngineException) {
                EngineException ex = (EngineException) e;
                CommonResponse<QuestionnaireResponseAO> response = new CommonResponse<>(String.valueOf(ex.getErrorCode()), ex.getMessage(), traceId);
                log.error("[TraceID:{}] [GET_QUESTIONNAIRE][ENGINE_ERROR] Code: {}, Message: {},ex:{}", traceId, ex.getErrorCode(), ex.getMessage(), ex);
                traceUtils.recordErr("getQuestionnaireEngineError", ex);
                return response;
            } else {
                log.error("[TraceID:{}] [GET_QUESTIONNAIRE][SYSTEM_ERROR]", traceId, e);
                return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), e.getMessage(), traceId);
            }
        }
    }

    @ServiceRequestMetrics(desc = "用户回答存储")
    @ApiOperation("用户回答存储")
    @PostMapping(value = "/answer", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    @SkylineTraceStarter(typePrefix = "answer#", isSync4Request = false)
    public CommonResponse<String> answer(@RequestBody QuestionnaireAnswerRequestAO requestAO, HttpServletRequest request) {
        Map<String, String> headers = HttpUtils.getHeadersMap(request);
        final String traceId = headers.get(HeaderConstants.X_TRACE_ID);
        log.info("[TraceID:{}] [ANSWER][REQUEST] {}", traceId, JSON.toJSONString(requestAO));
        checkParamService.checkInputParameters(requestAO, request);
        try {
            service.answer(requestAO, headers);
            CommonResponse<String> response = new CommonResponse<String>().success(traceId);
            log.info("[TraceID:{}] [ANSWER][END]", traceId);
            return response;
        } catch (Exception e) {
            if (e instanceof EngineException) {
                EngineException ex = (EngineException) e;
                CommonResponse<String> response = new CommonResponse<>(String.valueOf(ex.getErrorCode()), ex.getMessage(), traceId);
                log.error("[TraceID:{}] [ANSWER][ENGINE_ERROR] Code: {}, Message: {}", traceId, ex.getErrorCode(), ex.getMessage());
                return response;
            } else {
                log.error("[TraceID:{}] [ANSWER][SYSTEM_ERROR] {} ", traceId, e.getMessage(), e);
                return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), e.getMessage(), traceId);
            }
        }
    }

    @ServiceRequestMetrics(desc = "问卷解释")
    @ApiOperation(value = "问卷解释", notes = "问卷解释(http)")
    @PostMapping(value = "/explain", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    @SkylineTraceStarter(typePrefix = "questionnaireExplain#", isSync4Request = false)
    public CommonResponse<QuestionnaireExplainResponseAO> questionnaireExplain(@RequestBody XxjLlmRequest requestAO, HttpServletRequest request) {
        Map<String, String> headers = HttpUtils.getHeadersMap(request);
        log.info("[TraceID:{}] [QUESTIONNAIRE_EXPLAIN][REQUEST] {}", headers.get(HeaderConstants.X_TRACE_ID), JSON.toJSONString(requestAO));
        checkParamService.checkInputParameters(requestAO, request);
        return service.questionnaireExplain(requestAO, headers);
    }
}
