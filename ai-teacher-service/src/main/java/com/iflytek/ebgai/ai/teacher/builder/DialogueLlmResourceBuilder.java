package com.iflytek.ebgai.ai.teacher.builder;

import com.iflytek.ebgai.ai.teacher.entity.response.plan.CommonLlmResource;
import com.iflytek.ebgai.ai.teacher.entity.response.plan.DialogueLlmResource;
import com.iflytek.rec.teacher.domain.agent.response.Intention;
import com.iflytek.rec.teacher.domain.agent.response.StorageInfo;
import com.iflytek.rec.teacher.domain.model.NaireInfo;
import com.iflytek.rec.teacher.domain.pojo.NodeInfo;
import com.iflytek.rec.teacher.domain.pojo.Option;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DialogueLlmResourceBuilder
 * @Date: 2025/6/2 13:37
 * @Description:
 */
@Slf4j
public class DialogueLlmResourceBuilder {

    public static DialogueLlmResource build(LlmResource llmResource) {
        Intention origIntention = llmResource.getIntention();
        if (null == origIntention) {
            log.error("返回参数不合理,Intention为空:{}", origIntention);
            throw new RuntimeException("返回参数不合理,Intention空");
        }

        //1. 创建选项信息
        List<CommonLlmResource.NaireInfo.Option> options = buildOptions(llmResource);


        // 2. 创建NaireInfo问卷信息对象
        CommonLlmResource.NaireInfo naireInfo = buildNaireInfo(llmResource, options);

        // 3. 创建创建掌握度信息
        List<CommonLlmResource.NodeInfo> nodeInfos = buildNodeInfos(llmResource, origIntention);

        // 4. 创建Intention意图对象
        DialogueLlmResource.Intention intention = buildIntention(origIntention, naireInfo, nodeInfos);

        // 4. 创建DialogueLlmResource主对象
        DialogueLlmResource resource = buildDialogueLlmResource(llmResource, intention);

        return resource;
    }

    private static DialogueLlmResource buildDialogueLlmResource(LlmResource llmResource, DialogueLlmResource.Intention intention) {
        DialogueLlmResource resource = new DialogueLlmResource.Builder().isEnd(llmResource.isEnd())  // 设置为终止状态
                .intention(intention)  // 设置意图对象
                .isUpdateMastery(llmResource.isUpdateMastery())//添加掌握度标识返回
                .build();
        return resource;
    }

    /**
     * 创建意图信息
     *
     * @param origIntention
     * @param naireInfo
     * @param nodeInfos
     * @return
     */
    private static DialogueLlmResource.Intention buildIntention(Intention origIntention, CommonLlmResource.NaireInfo naireInfo, List<CommonLlmResource.NodeInfo> nodeInfos) {
        DialogueLlmResource.Intention intention = new DialogueLlmResource.Intention.Builder().step(origIntention.getStep())  // 当前已完成N步
                .allSteps(origIntention.getAllSteps())  // 总共有5步
                .naireInfo(naireInfo)  // 设置问卷信息
                .nodeInfos(nodeInfos) //设置掌握度信息
                .build();
        return intention;
    }

    /**
     * 创建掌握度节点信息
     *
     * @param llmResource
     * @param origIntention
     * @return
     */
    @NotNull
    private static List<CommonLlmResource.NodeInfo> buildNodeInfos(LlmResource llmResource, Intention origIntention) {
        List<CommonLlmResource.NodeInfo> nodeInfos = new ArrayList<>();
        if (llmResource.isUpdateMastery() && !CollectionUtils.isEmpty(origIntention.getNodeInfos())) {
            List<NodeInfo> origNodeInfoList = origIntention.getNodeInfos();
            for (NodeInfo origNodeInfo : origNodeInfoList) {
                CommonLlmResource.NodeInfo nodeInfo = new CommonLlmResource.NodeInfo.Builder().order(origNodeInfo.getOrder()).nodeId(origNodeInfo.getNodeId()).nodeType(origNodeInfo.getNodeType()).nodeAttribute(origNodeInfo.getNodeAttribute()).learndBehavior(origNodeInfo.getLearndBehavior()).learndTimes(origNodeInfo.getLearndTimes()).showState(origNodeInfo.getShowState()).masterScore(origNodeInfo.getMasterScore()).catalogId(origNodeInfo.getCatalogId()).build();
                nodeInfos.add(nodeInfo);
            }
        }
        return nodeInfos;
    }

    /**
     * 创建问卷信息
     *
     * @param llmResource
     * @param options
     * @return
     */
    private static CommonLlmResource.NaireInfo buildNaireInfo(LlmResource llmResource, List<CommonLlmResource.NaireInfo.Option> options) {
        NaireInfo origNaireInfo = llmResource.getIntention().getNaireInfo();
        StorageInfo storageInfo = llmResource.getStorageInfo();
        CommonLlmResource.NaireInfo naireInfo = new CommonLlmResource.NaireInfo.Builder().index(Optional.ofNullable(origNaireInfo).map(NaireInfo::getIndex).orElse(null)).dimension(Optional.ofNullable(origNaireInfo).map(NaireInfo::getDimension).orElse(null)).roundId(Optional.ofNullable(storageInfo).map(StorageInfo::getRoundId).orElse(null)).questionContent(Optional.ofNullable(origNaireInfo).map(NaireInfo::getQuestionContent).orElse(null)).addOptions(options)  // 确保options已初始化或处理空值
                .build();
        return naireInfo;
    }

    /**
     * 创建选项信息
     *
     * @param llmResource
     * @return
     */
    @NotNull
    private static List<CommonLlmResource.NaireInfo.Option> buildOptions(LlmResource llmResource) {
        List<CommonLlmResource.NaireInfo.Option> options = new ArrayList<>();
        List<Option> origOptions = Optional.ofNullable(llmResource).map(LlmResource::getIntention).map(Intention::getNaireInfo).map(NaireInfo::getOptions).orElse(Collections.emptyList());
        if (!CollectionUtils.isEmpty(origOptions)) {
            for (Option option : origOptions) {
                CommonLlmResource.NaireInfo.Option myOption = new CommonLlmResource.NaireInfo.Option.Builder().order(option.getOrder()).content(option.getContent()).build();
                options.add(myOption);
            }
        }
        return options;
    }
}
