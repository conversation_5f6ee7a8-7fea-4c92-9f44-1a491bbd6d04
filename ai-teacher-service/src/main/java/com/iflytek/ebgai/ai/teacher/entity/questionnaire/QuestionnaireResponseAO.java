package com.iflytek.ebgai.ai.teacher.entity.questionnaire;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 问卷响应实体
 */
@Data
@ApiModel(description = "问卷响应实体")
public class QuestionnaireResponseAO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "问卷内容", required = true)
    private NaireInfo naireInfo;

    @ApiModelProperty(value = "问卷是否建议终止 拿到终止标识不展示当前问卷 默认false", required = true)
    private boolean isEnd = false;

    @ApiModelProperty(value = "问卷进展，问卷已完成数，从1开始", required = true)
    private Integer step;

    @ApiModelProperty(value = "问卷总数：默认=5", required = true)
    private Integer allSteps;

    /**
     * 问卷内容信息
     */
    @Data
    public static class NaireInfo {

        @ApiModelProperty(value = "在问卷中的索引 在问卷中固定顺序 和入参一样roundRecNum", required = true)
        private Integer index;

        @ApiModelProperty(value = "问卷维度", required = true, notes = "枚举值：USER_RANK(成绩排名), WORK_STATE(作业感受), STUDY_TARGET(学习目标), CORE_LITERACY(学习素养)")
        private String dimension;

        @ApiModelProperty(value = "本轮id 一轮问卷流程内唯一", required = true)
        private String roundId;

        @ApiModelProperty(value = "题干信息", required = true)
        private String questionContent;

        @ApiModelProperty(value = "选项信息", required = true)
        private List<QuestionnaireOptionAO> options;

        @ApiModelProperty(value = "单选 = true or 多选 =false 默认true", required = true)
        private boolean isRadio = true;

        @ApiModelProperty(value = "最多选几个答案 默认=1", required = true)
        private Integer choiceNum = 1;
    }

} 