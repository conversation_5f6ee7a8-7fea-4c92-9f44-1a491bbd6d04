package com.iflytek.ebgai.ai.teacher.mapper;

import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.consts.HeaderConstants;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.questionnaire.QuestionnaireAnswerData;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireAnswerRequestAO;

import java.util.Map;

/**
 * @Author:huizhang68
 * @Date:2025/5/28
 */
public class QuestionnaireAnswerDataMapping {

    private volatile static QuestionnaireAnswerDataMapping instance;

    private QuestionnaireAnswerDataMapping() {
    }

    public static QuestionnaireAnswerDataMapping getInstance() {
        if (instance == null) {
            synchronized (QuestionnaireAnswerDataMapping.class) {
                if (instance == null) {
                    instance = new QuestionnaireAnswerDataMapping();
                }
            }
        }
        return instance;
    }

    public QuestionnaireAnswerData convert(QuestionnaireAnswerRequestAO requestAO, Map<String, String> headers) {
        QuestionnaireAnswerData answerData = new QuestionnaireAnswerData();
        answerData.setTraceId(headers.get(HeaderConstants.X_TRACE_ID));
        answerData.setUserId(headers.get(HeaderConstants.X_USER_ID));
        QuestionnaireAnswerRequestAO.AnswerInfoAO answerInfoAO = requestAO.getAnswerInfo();
        answerData.setRoundId(answerInfoAO.getRoundId());
        answerData.setBizAction(BizConstants.BizAction.AI_TUTORING_TEACHER_QUSNAIRE.getCode());
        answerData.setIndex(answerInfoAO.getIndex());
        answerData.setDimension(answerInfoAO.getDimension());
        answerData.setOptions(OptionMapping.getInstance().convert(answerInfoAO.getOptions()));

        return answerData;
    }
}
