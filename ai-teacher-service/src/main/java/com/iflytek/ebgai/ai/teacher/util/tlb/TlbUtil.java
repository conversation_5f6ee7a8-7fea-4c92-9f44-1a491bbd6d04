package com.iflytek.ebgai.ai.teacher.util.tlb;


import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.common.exception.BizException;
import com.iflytek.ebgai.ai.teacher.system.CommonProperties;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.tlb.TlbClientSelector;
import skynet.boot.tlb.domain.ServerIPEndpoint;
import skynet.boot.tlb.domain.Tag;

import javax.annotation.Resource;

@Slf4j
/*@Component
@EnableSkynetTlbClient*/ public class TlbUtil {

    @Resource
    TlbClientSelector tlbClientSelector;

    /**
     * lb重试3次
     */
    private static final int RETRY_TIME = 3;

    @Resource
    CommonProperties commonConfig;

    public String getAbilityBestServer(TlbSchema schema, String serviceName, String path) {
        return getAbilityBestServer(schema, path, serviceName, new Tag());
    }

    public String getAbilityBestServer(TlbSchema schema, String path, String serviceName, Tag tag) {
        if (commonConfig.isTlbEnable()) {
            return getBestServer(schema, serviceName, path, tag);
        }

        if (schema == TlbSchema.HTTP) {
            return commonConfig.getAbilityHttpUrl() + path;
        } else if (schema == TlbSchema.WS) {
            return commonConfig.getAbilityWebsocketUrl() + path;
        } else {
            throw new BizException(RetCode.ENGINE_ERROR.getCode(), "能力服务仅支持http/ws协议");
        }
    }


    public String getBestServer(TlbSchema schema, String serviceName, String path, Tag tag) {
        String endpoint = getEndpoint(serviceName, tag);
        return String.format(schema.getPattern(), endpoint, path);
    }

    public String getEndpoint(String serviceName, Tag tag) {
        ServerIPEndpoint bestServer = tlbClientSelector.getBestServer(serviceName, tag);
        if (bestServer == null) {
            throw new BizException(RetCode.ENGINE_ERROR.getCode(), "server not found " + ":" + serviceName);
        }
        return bestServer.getIPEndpoint();
    }

    /**
     * 获取服务地址，支持引擎路数不够 延迟重试3次
     *
     * @param schema
     * @param serviceName
     * @param path
     * @return
     */
    public String getEndPointByRetry(TlbSchema schema, String serviceName, String path) {
        int failCount = 0;
        String endpoint = "";
        boolean done = false;
        while (!done && failCount < RETRY_TIME) {
            try {
                endpoint = getAbilityBestServer(schema, serviceName, path);
                done = true;
            } catch (Exception tlbex) {
                log.error(tlbex.getMessage(), tlbex);
                failCount++;
                if (failCount < RETRY_TIME) {
                    log.info("获取服务失败,服务名称：{}", serviceName);
                    try {
                        Thread.sleep(500);
                    } catch (Exception e) {
                        throw new BizException("fail to try again get best server");
                    }
                }
            }
        }
        return endpoint;
    }
}
