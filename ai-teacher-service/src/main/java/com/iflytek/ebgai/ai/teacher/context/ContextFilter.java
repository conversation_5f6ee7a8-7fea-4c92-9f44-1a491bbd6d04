package com.iflytek.ebgai.ai.teacher.context;

import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @data 2024/10/22
 */
public class ContextFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        CachedBodyHttpServletRequestWrapper cachedBodyHttpServletRequestWrapper = new CachedBodyHttpServletRequestWrapper(request);
        String logId = HttpUtils.getTraceId(request);
        ContextUtil.init(logId, request.getRequestURI());
        try {
            filterChain.doFilter(cachedBodyHttpServletRequestWrapper, response);
        } finally {
            ContextUtil.remove();
        }
    }
}
