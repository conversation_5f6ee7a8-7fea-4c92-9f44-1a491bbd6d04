package com.iflytek.ebgai.ai.teacher.util;

import java.util.Map;
import java.util.Optional;

public class MapUtils {
    // 获取 String 类型值
    public static String getString(Map<String, String> headers, String key) {
        return Optional.ofNullable(headers.get(key)).filter(obj -> obj instanceof String).map(obj -> (String) obj).orElse(null);
    }

    // 获取嵌套 Map 中的 String 值
    public static String getNestedString(Map<String, Object> map, String outerKey, String innerKey) {
        return Optional.ofNullable(map.get(outerKey)).filter(obj -> obj instanceof Map).map(obj -> (Map<String, Object>) obj).map(innerMap -> innerMap.get(innerKey)).filter(obj -> obj instanceof String).map(obj -> (String) obj).orElse(null);
    }
}
