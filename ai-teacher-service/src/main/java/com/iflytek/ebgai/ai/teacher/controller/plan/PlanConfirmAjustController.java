package com.iflytek.ebgai.ai.teacher.controller.plan;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.entity.plan.confirmajust.ConfirmAjuestParam;
import com.iflytek.ebgai.ai.teacher.entity.plan.confirmajust.PlanResult;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 规划确认调整
 *
 * <AUTHOR>
 * @version 1.0
 * @ClassName LearnPlanController
 * @Date: 2025/6/4 18:19
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/v1/plan", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
public class PlanConfirmAjustController extends BasePlanController {

    private static final String USER_ID_HEADER = "x-user-id";
    private final TraceUtils traceUtils;

    public PlanConfirmAjustController(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    @ServiceRequestMetrics(desc = "确认调整")
    @PostMapping(value = "/confirmAjust", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    @ApiOperation("确认调整")
    @SkylineTraceStarter(typePrefix = "confirmAjust#", isSync4Request = false)
    public CommonResponse<String> confirmAjust(@RequestBody @Valid ConfirmAjuestParam confirmAjuestParam, HttpServletRequest request) throws IOException {
        List<PlanResult> planResults = confirmAjuestParam.getPlanResult();
        //参数校验
        checkInputParameters(planResults, request);
        Map<String, String> headers = HttpUtils.getHeadersMap(request);
        String traceId = HttpUtils.getTraceId(request);

        try {
            String userId = headers.get(USER_ID_HEADER);
            setUserInfo(planResults, userId);
            traceUtils.record("confirmAjustRequest", planResults);
            planResultServiceFacade.batchUpdatePlanResult(planResults, traceId);
            CommonResponse<String> response = new CommonResponse(RetCode.SUCCESS.getCode(), RetCode.SUCCESS.getDesc(), traceId);
            return response;
        } catch (Exception e) {
            traceUtils.recordErr("confirmAjustException", e);
            CommonResponse<String> response = new CommonResponse(RetCode.AI_TEACHER_ERROR.getCode(), e.getMessage(), traceId);
            log.error("[TraceID:{}] 推荐平台内部服务错误：{}", traceId, e.getMessage());
            return response;
        }
    }

    private void setUserInfo(List<PlanResult> planResults, String userId) {
        for (PlanResult planResult : planResults) {
            planResult.setUserId(userId);
        }
    }

    private void checkInputParameters(List<PlanResult> planResults, HttpServletRequest request) throws IOException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = checkHeaders(request);
        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();

        if (CollectionUtils.isEmpty(planResults)) {
            missingParams.add("planResults");
        } else {
            for (PlanResult planResult : planResults) {
                if (planResult.getOrder() <= 0) {
                    missingParams.add("order 必须大于0");
                }
                if (planResult.getNodeId() == null || planResult.getNodeId().isEmpty()) {
                    missingParams.add("nodeId");
                }
                if (planResult.getNodeType() == null || "".equals(planResult.getNodeType().name())) {
                    missingParams.add("nodeType");
                }
                if (planResult.getNodeAttribute() == null || "".equals(planResult.getNodeAttribute().name())) {
                    missingParams.add("nodeAttribute");
                }
                if (planResult.getLearndBehavior() == null || "".equals(planResult.getLearndBehavior().name())) {
                    missingParams.add("learndBehavior");
                }
                if (planResult.getLearndTimes() <= 0) {
                    missingParams.add("learndTimes 必须大于0");
                }
                if (planResult.getCatalogId() == null || planResult.getCatalogId().isEmpty()) {
                    missingParams.add("catalogId");
                }

            }

        }

        // 3. 如果有缺失参数则发送错误响应
        if (!missingHeaders.isEmpty() || !missingParams.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("参数校验失败: ");

            if (!missingHeaders.isEmpty()) {
                errorMsg.append("缺失请求头 - ").append(String.join(", ", missingHeaders));
            }

            if (!missingParams.isEmpty()) {
                if (!missingHeaders.isEmpty()) {
                    errorMsg.append("; ");
                }
                errorMsg.append("缺失请求参数 - ").append(String.join(", ", missingParams));
            }

            errorInfo = errorMsg.toString();

        }
        if (StringUtils.isNotEmpty(errorInfo)) {
            log.error("[TraceID:{}] {}", HttpUtils.getTraceId(request), errorInfo);
            throw new ParamValidateException(RetCode.INPUT_PARAMETERS_MISSING.getCode(), errorInfo, request.getHeader("x-trace-id"));
        }
    }
}
