package com.iflytek.ebgai.ai.teacher.context;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * <AUTHOR>
 * @data 2024/10/23
 */
public class ContextUtil {
    private static final ThreadLocal<Context> LOCAL = new ThreadLocal<Context>();

    public static void init() {
        LOCAL.set(Context.builder().traceId(String.valueOf(UUID.randomUUID())).build());
    }

    public static void init(String traceId, String requestUrl) {
        MDC.put("TRACE_ID", traceId);
        MDC.put("REQUEST_URL", requestUrl);
    }

    public static void init(Context context) {
        LOCAL.set(context);
    }

    public static Context get() {
        return LOCAL.get();
    }

    public static String getTraceId() {
        return MDC.get("TRACE_ID");
    }

    public static String getRequestUrl() {
        return MDC.get("REQUEST_URL");
    }

    public static void remove() {
        LOCAL.remove();
        MDC.clear();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class Context {
        private String traceId;
    }
}
