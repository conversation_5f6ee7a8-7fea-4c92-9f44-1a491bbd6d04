package com.iflytek.ebgai.ai.teacher.entity.response.dialogue;

import com.iflytek.ebgai.ai.teacher.entity.response.share.Intention;
import com.iflytek.ebgai.ai.teacher.entity.response.share.StorageInfo;
import lombok.Data;

/**
 * 对话LLM资源出参主对象
 */
@Data
public class XxjDialogueLlmResource {
    /**
     * 是否入库存储，默认false
     */
    private Boolean isStorage;

    /**
     * 是否终止流式输出，默认false
     */
    private Boolean isEnd;

    /**
     * 是否动效，默认false
     */
    private Boolean isDynamic;

    /**
     * 是否刷新掌握度，默认false
     */
    private Boolean isUpdateMastery;

    /**
     * 结构化意图数据
     */
    private Intention intention;

    /**
     * 存储信息（非必填）
     */
    private StorageInfo storageInfo;
}