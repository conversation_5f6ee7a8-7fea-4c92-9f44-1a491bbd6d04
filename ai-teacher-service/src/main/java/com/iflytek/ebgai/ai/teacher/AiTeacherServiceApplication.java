package com.iflytek.ebgai.ai.teacher;

import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.annotation.EnableFeatureAPI;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import skynet.boot.AppUtils;
import skynet.boot.annotation.EnableSkynetLogging;
import skynet.boot.annotation.EnableSkynetMetrics;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableSkynetMetrics
@EnableSkynetLogging
@EnableSkylineBrave
@SpringBootApplication(exclude = RedisAutoConfiguration.class)
@ComponentScan(basePackages = {"com.iflytek.ebgai.ai.teacher.*", "com.iflytek.ebgai.ai.teacher.common"})
@EnableDataHub
@EnableFeatureAPI
public class AiTeacherServiceApplication {
    public static void main(String[] args) {
        AppUtils.run(AiTeacherServiceApplication.class, args);
        log.info("================AI伴学服务启动成功============================");

    }

}
