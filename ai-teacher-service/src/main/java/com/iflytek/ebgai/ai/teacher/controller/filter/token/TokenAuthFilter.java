package com.iflytek.ebgai.ai.teacher.controller.filter.token;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.common.constant.CommonConstant;
import com.iflytek.ebgai.ai.teacher.common.exception.BizException;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class TokenAuthFilter extends OncePerRequestFilter {
    private AuthClient authClient;
    private List<String> whitelistUrls;

    public TokenAuthFilter(AuthClient authClient, List<String> whitelistUrls) {
        this.authClient = authClient;
        this.whitelistUrls = whitelistUrls;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        HttpServletRequest requestWrapper = request;

        // 设置白名单
        String requestURI = request.getRequestURI();
        if (whitelistUrls != null && whitelistUrls.contains(requestURI)) {
            filterChain.doFilter(requestWrapper, response);
            return;
        }

        try {
            Map<String, String> headers = HttpUtils.getHeadersMap(request);
            String appId = headers.get(CommonConstant.APP_ID);
            String token = headers.get(CommonConstant.AUTHORIZATION);

            authClient.tokenCheckV3(appId, token);
        } catch (BizException be) {
            log.error("鉴权失败", be);
            CommonResponse result = new CommonResponse(be.getCode(), be.getMessage());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding("UTF-8");
            response.getOutputStream().write(JSONObject.toJSONString(result).getBytes(StandardCharsets.UTF_8));
            return;
        } catch (Exception e) {
            log.error("鉴权失败", e);
            CommonResponse result = new CommonResponse(RetCode.AUTH_ERROR.getCode(), RetCode.AUTH_ERROR.getDesc());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding("UTF-8");
            response.getOutputStream().write(JSONObject.toJSONString(result).getBytes(StandardCharsets.UTF_8));
            return;
        }
        filterChain.doFilter(requestWrapper, response);
    }

}
