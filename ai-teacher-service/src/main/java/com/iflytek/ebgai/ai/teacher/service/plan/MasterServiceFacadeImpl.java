package com.iflytek.ebgai.ai.teacher.service.plan;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.rec.teacher.domain.pojo.NodeInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.GlobalMasteryItem;
import com.iflytek.skylab.core.dataapi.data.MasterData;
import com.iflytek.skylab.core.dataapi.data.MasterItem;
import com.iflytek.skylab.core.dataapi.data.MasterQuery;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skyline.brave.TraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MasterServiceFacadeImpl
 * @Date: 2025/6/3 11:23
 * @Description:
 */
@Slf4j
@Service
public class MasterServiceFacadeImpl implements MasterServiceFacade {

    private final TraceUtils traceUtils;
    @Value("${skylab.data.api.graph.cacheVersion}")
    protected String graphVersion;

    public MasterServiceFacadeImpl(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    @Override
    public void updateMasterData(LlmResource llmResource, String traceId) {
        log.info("[TraceID:{}] 开始调用dataapi更新掌握度，", traceId);
        long start = System.currentTimeMillis();
        String userId = llmResource.getStorageInfo().getUserId();
        Assert.notNull(llmResource.getIntention(), "[更新掌握度]意图信息不能为空!");
        Assert.notEmpty(llmResource.getIntention().getNodeInfos(), "[更新掌握度]节点信息不能为空!");
        List<String> nodeIds = llmResource.getIntention().getNodeInfos().stream().map(NodeInfo::getNodeId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nodeIds)) {
            log.error("[TraceID:{}] 更新掌握度错误，锚点信息为空：{}", traceId, nodeIds);
            return;
        }
        //更新 先查询：用户+nodeIds
        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId(userId);
        masterQuery.setNodeIdList(nodeIds);
        List<UserMasteryRecord> existedUserMasteryRecords = DataHub.getMasterService().queryMasterDataBatch(traceId, masterQuery);

        //构建掌握度更新或新增数据
        MasterData masterData = new MasterData().setUserId(userId);
        List<MasterItem> items = getMasterItem(llmResource, existedUserMasteryRecords);
        masterData.setItems(items);
        traceUtils.record("updateMasterDataAndGetStart", masterData);
        DataHub.getMasterService().updateMasterDataAndGet(traceId, masterData);
        traceUtils.record("updateMasterDataAndGetEnd", "[TraceID:" + traceId + "]" + "更新掌握度结束");
        log.info("[TraceID:{}] 结束调用dataapi更新掌握度,耗时：{}", traceId, System.currentTimeMillis() - start);
    }

    private List<MasterItem> getMasterItem(LlmResource llmResource, List<UserMasteryRecord> existedUserMasteryRecords) {
        // 获取已存在的锚点ID集合
        Set<String> existingNodeIds = existedUserMasteryRecords.stream().map(UserMasteryRecord::getNodeId).collect(Collectors.toCollection(HashSet::new));

        // 获得引擎返回的需要更新或新增的掌握度信息
        List<NodeInfo> nodeInfos = llmResource.getIntention().getNodeInfos();
        List<MasterItem> masterItems = new ArrayList<>(nodeInfos.size());

        // 遍历伴学引擎接口返回的节点信息：
        for (NodeInfo nodeInfo : nodeInfos) {
            //1. 构建更新或新增的掌握度Item
            MasterItem masterItem = buildBaseMasterItem(nodeInfo);

            // 2. 新增掌握度创建对应的全局掌握度信息
            if (!existingNodeIds.contains(nodeInfo.getNodeId())) {
                log.info("新锚点需要设置GlobalMastery, 锚点ID：{}", nodeInfo.getNodeId());
                handleNewAnchorPoint(masterItem);
            }

            masterItems.add(masterItem);
        }
        return masterItems;
    }

    /**
     * 构建更新或新增掌握度信息
     *
     * @param nodeInfo
     * @return
     */
    private MasterItem buildBaseMasterItem(NodeInfo nodeInfo) {
        MasterItem item = new MasterItem();
        //融合掌握度
        item.setAlgoFusion(nodeInfo.getFusionMasterScore());
        //预测掌握度
        item.setAlgoPredict(nodeInfo.getPredictMasterScore());
        //真实掌握度
        item.setAlgoReal(nodeInfo.getRealMasterScore());
        item.setFusion(nodeInfo.getFusionMasterScore());
        item.setPredict(nodeInfo.getPredictMasterScore());
        item.setReal(nodeInfo.getRealMasterScore());
        item.setCatalogId(nodeInfo.getCatalogId());
        item.setNodeId(nodeInfo.getNodeId());
        item.setBizCode(BizCodeEnum.ZSY_XXJ);
        item.setMasteryType("REAL");//真实画像
        item.setStudyCode(StudyCodeEnum.SYNC_OS);
        item.setMasteryScore(nodeInfo.getFusionMasterScore());
        item.setShouldFlag(false);
        item.setGraphVersion(graphVersion);
        NodeTypeEnum nodeTypeEnum = NodeTypeEnum.valueOf(nodeInfo.getNodeType());
        item.setNodeType(nodeTypeEnum);
        item.setCatalogType(nodeInfo.getCatalogType());
        item.setCreateTime(Instant.now());
        return item;
    }

    /**
     * 新增的掌握度，需要创建对应的全局掌握度信息（历史存在的掌握度不走此逻辑）
     *
     * @param masterItem
     */
    private void handleNewAnchorPoint(MasterItem masterItem) {
        GlobalMasteryItem global = new GlobalMasteryItem();
        global.setMasterScore(masterItem.getMasteryScore());
        global.setReal(masterItem.getAlgoReal());    // 修正字段引用
        global.setPredict(masterItem.getAlgoPredict());
        global.setFusion(masterItem.getAlgoFusion());
        global.setAssociativePoint(new HashMap<>());
        JSONObject globalJsonObject = (JSONObject) JSON.toJSON(global);
        masterItem.setGlobalMastery(globalJsonObject);  // 使用更安全的JSON方法
    }
}
