package com.iflytek.ebgai.ai.teacher.common.trace;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.iflytek.ebgai.ai.teacher.context.ContextUtil;
import org.apache.commons.lang3.StringUtils;

public class TraceIdConvert extends ClassicConverter {
    @Override
    public String convert(ILoggingEvent iLoggingEvent) {
        return StringUtils.isBlank(ContextUtil.getTraceId()) ? "" : ContextUtil.getTraceId();
    }
}
