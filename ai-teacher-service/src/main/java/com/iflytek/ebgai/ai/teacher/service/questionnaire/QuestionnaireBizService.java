package com.iflytek.ebgai.ai.teacher.service.questionnaire;

import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireAnswerRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireExplainResponseAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireResponseAO;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;

import java.util.Map;

/**
 * 问卷前端接口服务
 *
 * @Author:huizhang68
 * @Date:2025/5/28
 */
public interface QuestionnaireBizService {

    QuestionnaireResponseAO getQuestionnaire(QuestionnaireRequestAO requestAO, Map<String, String> headers) throws Exception;

    void answer(QuestionnaireAnswerRequestAO requestAO, Map<String, String> headers) throws Exception;

    CommonResponse<QuestionnaireExplainResponseAO> questionnaireExplain(XxjLlmRequest xxjLlmRequest, Map<String, String> headers);
}
