package com.iflytek.ebgai.ai.teacher.service.plan;

import com.alibaba.fastjson.JSON;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.plan.PlanResultRequest;
import com.iflytek.ebgai.ai.teacher.dataapi.service.PlanResultService;
import com.iflytek.ebgai.ai.teacher.entity.plan.confirmajust.PlanResult;
import com.iflytek.rec.teacher.domain.agent.response.StorageInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;


import java.util.List;
import java.util.stream.Collectors;

/**
 * 规划结果数据服务
 *
 * <AUTHOR>
 * @version 1.0
 * @ClassName PlanResultServiceFacadeImpl
 * @Date: 2025/5/27 17:30
 * @Description:
 */
@Slf4j
@Service
public class PlanResultServiceFacadeImpl implements PlanResultServiceFacade {
    @Autowired
    private PlanResultService planResultService;

    @Override
    public void savePlanResult(LlmResource llmResource, String traceId) {
        log.info("[TraceID:{}] 开始调用dataapi记录学习规划", traceId);
        long start = System.currentTimeMillis();
        StorageInfo storageInfo = llmResource.getStorageInfo();
        Assert.notNull(storageInfo, "[规划结果]学习规划信息不能为空!");
        Assert.notNull(llmResource.getIntention(), "[规划结果]意图信息不能为空!");
        Assert.notEmpty(llmResource.getIntention().getNodeInfos(), "[规划结果]节点信息不能为空!");
        List<com.iflytek.rec.teacher.domain.pojo.NodeInfo> origNodeInfos = llmResource.getIntention().getNodeInfos();
        PlanResultRequest planResultRequest = new PlanResultRequest().setUserId(storageInfo.getUserId()).setCatalogId(storageInfo.getCatalogId()).setBizAction(storageInfo.getBizAction());
        List<PlanResultData> planResultDataList = planResultService.query(planResultRequest);
        String userId = storageInfo.getUserId();
        String catalogId = storageInfo.getCatalogId();
        if (!CollectionUtils.isEmpty(planResultDataList)) {
            log.info("[TraceID:{}] 用户:{}在章节:{}下存在规划节点,需删除：{}", traceId, userId, catalogId, JSON.toJSONString(planResultDataList));
            //同一个用户的同一个章节目录只能保留一份规划结果
            PlanResultRequest deletePlanResultRequest = new PlanResultRequest().setUserId(storageInfo.getUserId()).setCatalogId(storageInfo.getCatalogId()).setBizAction(storageInfo.getBizAction());
            planResultService.delete(deletePlanResultRequest);
        }

        if (!CollectionUtils.isEmpty(origNodeInfos)) {
            log.info("[TraceID:{}] 学习规划需要存储的规划节点：{}", traceId, JSON.toJSONString(origNodeInfos));
            for (com.iflytek.rec.teacher.domain.pojo.NodeInfo origNodeInfo : origNodeInfos) {
                //2. 封装规划结果数据
                PlanResultData planResultData = new PlanResultData().setUserId(storageInfo.getUserId()).setCatalogId(storageInfo.getCatalogId()).setStudyCode(BizConstants.StudyCodes.SYNC_TUTORING.getCode()).setNodeAttribute(origNodeInfo.getNodeAttribute()).setLearndBehavior(origNodeInfo.getLearndBehavior()).setTraceId(traceId).setLearndTimes(origNodeInfo.getLearndTimes()).setOrder(origNodeInfo.getOrder()).setNodeId(origNodeInfo.getNodeId()).setNodeType(origNodeInfo.getNodeType()).setBizAction(storageInfo.getBizAction()).setUpdateTime(storageInfo.getUpdateTime());

                // 新增规划结果数据
                planResultService.insert(planResultData);
            }
        }


        log.info("[TraceID:{}] 结束调用dataapi记录学习规划结果,耗时:{}", traceId, System.currentTimeMillis() - start);
    }

    @Override
    public void batchUpdatePlanResult(List<PlanResult> planResults, String traceId) {
        log.info("[TraceID:{}] 开始调用dataapi调整规划结果");
        PlanResult pr = planResults.get(0);
        PlanResultRequest allPlanResultRequest = new PlanResultRequest().setUserId(pr.getUserId()).setCatalogId(pr.getCatalogId()).setBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode());
        List<PlanResultData> allPlanResultDataList = planResultService.query(allPlanResultRequest);
        List<String> exitsNodeIds = allPlanResultDataList.stream().map(PlanResultData::getNodeId).collect(Collectors.toList());
        List<String> nodeIds = planResults.stream().map(PlanResult::getNodeId).collect(Collectors.toList());

        //要删除 nodeIds 中的元素
        List<String> deleteNodeIds = exitsNodeIds.stream().filter(e -> !nodeIds.contains(e)).collect(Collectors.toList());
        log.info("[TraceID:{}] 要删除nodeIds中的元素:{}", traceId, JSON.toJSONString(deleteNodeIds));
        //数据库不存在需要新增 nodeIds
        List<PlanResult> insertPlanResults = planResults.stream().filter(e -> !exitsNodeIds.contains(e.getNodeId())).collect(Collectors.toList());
        log.info("[TraceID:{}] 数据库不存在需要新增元素:{}", traceId, JSON.toJSONString(insertPlanResults));
        //存在数据库中需要修改的数据
        List<PlanResult> updatePlanResults = planResults.stream().filter(e -> exitsNodeIds.contains(e.getNodeId())).collect(Collectors.toList());
        log.info("[TraceID:{}] 存在数据库中需要修改的数据:{}", traceId, JSON.toJSONString(updatePlanResults));
        // 1. 删除没有作为入参传递的规划结果
        if (!CollectionUtils.isEmpty(deleteNodeIds)) {
            for (String nodeId : deleteNodeIds) {
                PlanResultRequest deletePlanResultRequest = new PlanResultRequest().setUserId(pr.getUserId()).setCatalogId(pr.getCatalogId()).setNodeId(nodeId).setBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode());
                planResultService.delete(deletePlanResultRequest);
            }
        }
        //2. 新增规划结果
        if (!CollectionUtils.isEmpty(insertPlanResults)) {
            for (PlanResult planResult : insertPlanResults) {
                PlanResultData planResultData = new PlanResultData().setBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode()).setStudyCode(BizConstants.StudyCodes.SYNC_TUTORING.getCode()).setUserId(planResult.getUserId()).setOrder(planResult.getOrder()).setNodeId(planResult.getNodeId()).setNodeType(planResult.getNodeType().name()).setNodeAttribute(planResult.getNodeAttribute().name()).setLearndBehavior(planResult.getLearndBehavior().name()).setLearndTimes(planResult.getLearndTimes()).setCatalogId(planResult.getCatalogId()).setTraceId(traceId);
                planResultService.insert(planResultData);
            }
        }
        //3.更新已存在的规划结果
        if (!CollectionUtils.isEmpty(updatePlanResults)) {
            for (PlanResult planResult : updatePlanResults) {
                PlanResultData planResultData = new PlanResultData().setBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode()).setStudyCode(BizConstants.StudyCodes.SYNC_TUTORING.getCode()).setUserId(planResult.getUserId()).setOrder(planResult.getOrder()).setNodeId(planResult.getNodeId()).setNodeType(planResult.getNodeType().name()).setNodeAttribute(planResult.getNodeAttribute().name()).setLearndBehavior(planResult.getLearndBehavior().name()).setLearndTimes(planResult.getLearndTimes()).setCatalogId(planResult.getCatalogId()).setTraceId(traceId);
                planResultService.update(planResultData);
            }
        }

        log.info("[TraceID:{}] 结束调用dataapi调整规划结果", traceId);
    }

}
