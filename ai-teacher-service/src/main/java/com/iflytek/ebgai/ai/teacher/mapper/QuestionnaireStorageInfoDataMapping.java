package com.iflytek.ebgai.ai.teacher.mapper;

import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.consts.HeaderConstants;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.Summary;
import com.iflytek.rec.teacher.domain.agent.response.SummaryInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 问卷解释存储
 */
public class QuestionnaireStorageInfoDataMapping {

    private volatile static QuestionnaireStorageInfoDataMapping instance;

    private QuestionnaireStorageInfoDataMapping() {
    }

    public static QuestionnaireStorageInfoDataMapping getInstance() {
        if (instance == null) {
            synchronized (QuestionnaireStorageInfoDataMapping.class) {
                if (instance == null) {
                    instance = new QuestionnaireStorageInfoDataMapping();
                }
            }
        }
        return instance;
    }

    public StorageInfoData convert(LlmResource llmResource, Map<String, String> headers) {
        StorageInfoData target = new StorageInfoData();
        target.setUserId(headers.get(HeaderConstants.X_USER_ID));
        if (!Objects.isNull(llmResource.getStorageInfo())) {
            target.setUserLevel(llmResource.getStorageInfo().getUserLevel());
            target.setOriContent(llmResource.getStorageInfo().getOriContent());
            target.setRoundId(llmResource.getStorageInfo().getRoundId());
        }
        target.setTraceId(headers.get(HeaderConstants.X_TRACE_ID));
        target.setBizAction(BizConstants.BizAction.AI_TUTORING_TEACHER_QUSNAIRE.getCode());
        target.setFunctionCode(BizConstants.FunctionCode.QUSNAIRE_EXPLAIN.getCode());
        target.setUpdateTime(System.currentTimeMillis());
        com.iflytek.rec.teacher.domain.agent.response.SummaryInfo sourceSummaryInfo = llmResource.getIntention().getSummaryInfo();
        target.setSummaryInfo(convertSummaryInfo(sourceSummaryInfo));
        return target;
    }

    private com.iflytek.ebgai.ai.teacher.dataapi.entity.SummaryInfo convertSummaryInfo(SummaryInfo summaryInfo) {
        if (Objects.isNull(summaryInfo)) {
            return null;
        }
        com.iflytek.ebgai.ai.teacher.dataapi.entity.SummaryInfo target = new com.iflytek.ebgai.ai.teacher.dataapi.entity.SummaryInfo();
        target.setSummaryType(summaryInfo.getSummaryType());
        if (CollectionUtils.isEmpty(summaryInfo.getSummaries())) {
            return target;
        }
        List<Summary> summariesTarget = new ArrayList<>();
        for (com.iflytek.rec.teacher.domain.agent.response.Summary item : summaryInfo.getSummaries()) {
            com.iflytek.ebgai.ai.teacher.dataapi.entity.Summary itemTarget = new com.iflytek.ebgai.ai.teacher.dataapi.entity.Summary();
            itemTarget.setType(item.getHeadline());
            itemTarget.setOrder(item.getOrder());
            itemTarget.setContents(item.getContents());
            summariesTarget.add(itemTarget);
        }
        target.setSummaries(summariesTarget);
        return target;
    }
}
