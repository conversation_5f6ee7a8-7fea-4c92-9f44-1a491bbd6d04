package com.iflytek.ebgai.ai.teacher.mapper;

import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireExplainResponseAO;
import com.iflytek.rec.teacher.domain.agent.response.Summary;
import com.iflytek.rec.teacher.domain.agent.response.SummaryInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 问卷解释响应给前端
 *
 * @Author:huizhang68
 * @Date:2025/5/30
 */
public class QuestionnaireExplainResponseAOMapping {

    private volatile static QuestionnaireExplainResponseAOMapping instance;

    private QuestionnaireExplainResponseAOMapping() {
    }

    public static QuestionnaireExplainResponseAOMapping getInstance() {
        if (instance == null) {
            synchronized (QuestionnaireExplainResponseAOMapping.class) {
                if (instance == null) {
                    instance = new QuestionnaireExplainResponseAOMapping();
                }
            }
        }
        return instance;
    }

    public QuestionnaireExplainResponseAO convert(LlmResource source) {
        if (Objects.isNull(source)) {
            return null;
        }
        QuestionnaireExplainResponseAO target = new QuestionnaireExplainResponseAO();
        if (!Objects.isNull(source.getStorageInfo())) {
            target.setUserLevel(source.getStorageInfo().getUserLevel());
            target.setRoundId(source.getStorageInfo().getRoundId());
        }
        if (!Objects.isNull(source.getIntention())) {
            target.setSummaries(buildSummaryInfoAO(source.getIntention().getSummaryInfo()));
        }
        return target;
    }

    public List<QuestionnaireExplainResponseAO.SummaryAO> buildSummaryInfoAO(SummaryInfo source) {
        if (Objects.isNull(source) || CollectionUtils.isEmpty(source.getSummaries())) {
            return Collections.emptyList();
        }
        List<QuestionnaireExplainResponseAO.SummaryAO> targetList = new ArrayList<>();
        for (Summary item : source.getSummaries()) {
            QuestionnaireExplainResponseAO.SummaryAO itemSummaryAO = new QuestionnaireExplainResponseAO.SummaryAO();
            itemSummaryAO.setHeadline(item.getHeadline());
            itemSummaryAO.setOrder(item.getOrder());
            itemSummaryAO.setContents(item.getContents());
            targetList.add(itemSummaryAO);
        }
        return targetList;
    }


    public QuestionnaireExplainResponseAO convert(StorageInfoData source) {
        if (Objects.isNull(source)) {
            return null;
        }
        QuestionnaireExplainResponseAO target = new QuestionnaireExplainResponseAO();
        target.setUserLevel(source.getUserLevel());
        target.setRoundId(source.getRoundId());
        if (!Objects.isNull(source.getSummaryInfo()) && CollectionUtils.isNotEmpty(source.getSummaryInfo().getSummaries())) {
            target.setSummaries(buildSummaryInfoAO(source.getSummaryInfo().getSummaries()));
        }
        return target;
    }

    public List<QuestionnaireExplainResponseAO.SummaryAO> buildSummaryInfoAO(List<com.iflytek.ebgai.ai.teacher.dataapi.entity.Summary> summaries) {
        if (CollectionUtils.isEmpty(summaries)) {
            return Collections.emptyList();
        }
        List<QuestionnaireExplainResponseAO.SummaryAO> targetList = new ArrayList<>();
        for (com.iflytek.ebgai.ai.teacher.dataapi.entity.Summary source : summaries) {
            QuestionnaireExplainResponseAO.SummaryAO itemSummaryAO = new QuestionnaireExplainResponseAO.SummaryAO();
            itemSummaryAO.setHeadline(source.getType());
            itemSummaryAO.setOrder(source.getOrder());
            itemSummaryAO.setContents(source.getContents());
            targetList.add(itemSummaryAO);
        }
        return targetList;
    }

}
