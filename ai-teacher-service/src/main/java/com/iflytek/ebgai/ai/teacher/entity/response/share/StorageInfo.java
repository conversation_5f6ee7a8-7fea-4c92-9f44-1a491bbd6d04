package com.iflytek.ebgai.ai.teacher.entity.response.share;// StorageInfo.java

import lombok.Data;

/**
 * 存储信息
 */
@Data
public class StorageInfo {
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户层级（CONVENTIONAL, HIGHSCOREADVANCED, THINKINGEXPANSION）
     */
    private String userLevel;

    /**
     * 跟踪ID
     */
    private String traceId;

    /**
     * 业务方功能（AI_TUTORING_TEACHER_QUSNAIRE, AI_TUTORING_LEARNING_PLAN, OTHERS）
     */
    private String bizAction;

    /**
     * 引擎功能（QUSNAIRE_RECNAIRE, LEARNINGPLAN_PLAN 等）
     */
    private String functionCode;

    /**
     * 本轮ID
     */
    private String roundId;

    /**
     * 用户输入
     */
    private String query;

    /**
     * 原始大模型输出
     */
    private String oriContent;

    /**
     * 更新时间
     */
    private Long updatetime;
}