package com.iflytek.ebgai.ai.teacher.service.questionnaire;


import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.DeleteLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.paln.QueryLearningPlanRequest;
import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferRequest;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireAnswerRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireOptionAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.entity.request.share.InferBody;
import com.iflytek.ebgai.ai.teacher.entity.request.share.SceneInfo;
import com.iflytek.ebgai.ai.teacher.entity.request.share.SessionInfo;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.util.HttpServletRequestUtils;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Auther: huizhang68
 * @date: 2025/6/11 10:57
 */
@Slf4j
@Service
public class QuestionnaireCheckParamServiceImpl implements QuestionnaireCheckParamService {

    @Override
    public void checkInputParameters(QuestionnaireRequestAO requestAO, HttpServletRequest request) throws ParamValidateException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = HttpServletRequestUtils.checkHeaders(request);

        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();
        if (Objects.isNull(requestAO.getSceneInfo())) {
            missingParams.add("sceneInfo");
        } else {
            QuestionnaireRequestAO.SceneInfoAO sceneInfo = requestAO.getSceneInfo();
            if (StringUtils.isEmpty(sceneInfo.getSubjectCode())) {
                missingParams.add("sceneInfo.subjectCode");
            }
            if (StringUtils.isEmpty(sceneInfo.getPhaseCode())) {
                missingParams.add("sceneInfo.phaseCode");
            }
        }

        if (Objects.isNull(requestAO.getSessionInfo())) {
            missingParams.add("sessionInfo");
        } else {
            QuestionnaireRequestAO.SessionInfoAO sessionInfoAO = requestAO.getSessionInfo();
            if (null == sessionInfoAO.getRoundRecNum() || sessionInfoAO.getRoundRecNum() < 1) {
                missingParams.add("sessionInfo.roundRecNum");
            }
            if (StringUtils.isEmpty(sessionInfoAO.getRoundId())) {
                missingParams.add("sessionInfo.roundId");
            }
        }

        // 3. 如果有缺失参数则发送错误响应
        doCheck(HttpUtils.getTraceId(request), errorInfo, missingHeaders, missingParams);
    }

    @Override
    public void checkInputParameters(QuestionnaireAnswerRequestAO requestAO, HttpServletRequest request) throws ParamValidateException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = HttpServletRequestUtils.checkHeaders(request);

        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();
        if (Objects.isNull(requestAO.getSceneInfo())) {
            missingParams.add("sceneInfo");
        } else {
            QuestionnaireRequestAO.SceneInfoAO sceneInfo = requestAO.getSceneInfo();
            if (StringUtils.isEmpty(sceneInfo.getSubjectCode())) {
                missingParams.add("sceneInfo.subjectCode");
            }
            if (StringUtils.isEmpty(sceneInfo.getPhaseCode())) {
                missingParams.add("sceneInfo.phaseCode");
            }
        }

        if (Objects.isNull(requestAO.getAnswerInfo())) {
            missingParams.add("answerInfo");
        } else {
            QuestionnaireAnswerRequestAO.AnswerInfoAO answerInfo = requestAO.getAnswerInfo();
            if (null == answerInfo.getRoundRecNum() || answerInfo.getRoundRecNum() < 1) {
                missingParams.add("answerInfo.roundRecNum");
            }
            if (StringUtils.isEmpty(answerInfo.getRoundId())) {
                missingParams.add("answerInfo.roundId");
            }
            if (StringUtils.isEmpty(answerInfo.getDimension())) {
                missingParams.add("answerInfo.dimension");
            }
            if (CollectionUtils.isEmpty(answerInfo.getOptions())) {
                missingParams.add("answerInfo.options");
            } else {
                List<QuestionnaireOptionAO> options = answerInfo.getOptions();
                for (QuestionnaireOptionAO item : options) {
                    if (null == item.getOrder()) {
                        missingParams.add("answerInfo.options.order");
                        break;
                    }
                    if (StringUtils.isEmpty(item.getContent())) {
                        missingParams.add("answerInfo.options.content");
                        break;
                    }
                }
            }
        }
        // 3. 如果有缺失参数则发送错误响应
        doCheck(HttpUtils.getTraceId(request), errorInfo, missingHeaders, missingParams);
    }

    @Override
    public void checkInputParameters(XxjLlmRequest requestAO, HttpServletRequest request) throws ParamValidateException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = HttpServletRequestUtils.checkHeaders(request);
        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();
        if (Objects.isNull(requestAO.getSceneInfo())) {
            missingParams.add("sceneInfo");
        } else {
            SceneInfo sceneInfo = requestAO.getSceneInfo();
            if (StringUtils.isEmpty(sceneInfo.getSubjectCode())) {
                missingParams.add("sceneInfo.subjectCode");
            }
            if (StringUtils.isEmpty(sceneInfo.getPhaseCode())) {
                missingParams.add("sceneInfo.phaseCode");
            }
        }

        if (Objects.isNull(requestAO.getSessionInfo())) {
            missingParams.add("sessionInfo");
        } else {
            SessionInfo sessionInfo = requestAO.getSessionInfo();
            if (null == sessionInfo.getRoundRecNum() || sessionInfo.getRoundRecNum() < 1) {
                missingParams.add("sessionInfo.roundRecNum");
            }
            if (StringUtils.isEmpty(sessionInfo.getRoundId())) {
                missingParams.add("sessionInfo.roundId");
            }
        }
        if (Objects.isNull(requestAO.getInferBody())) {
            missingParams.add("inferBody");
        } else {
            InferBody inferBody = requestAO.getInferBody();
            if (StringUtils.isEmpty(inferBody.getChatId())) {
                missingParams.add("inferBody.chatId");
            }
        }
        // 3. 如果有缺失参数则发送错误响应
        doCheck(HttpUtils.getTraceId(request), errorInfo, missingHeaders, missingParams);
    }

    @Override
    public void checkInputParameters(QueryQuestionnaireInferRequest requestAO) throws ParamValidateException {
        List<String> missingParams = new ArrayList<>();
        if (StringUtils.isEmpty(requestAO.getTraceId())) {
            missingParams.add("traceId");
        }
        if (StringUtils.isEmpty(requestAO.getUserId())) {
            missingParams.add("userId");
        }
        // 3. 如果有缺失参数则发送错误响应
        doCheck(requestAO.getTraceId(), StringUtils.EMPTY, Collections.emptyList(), missingParams);
    }

    @Override
    public void checkInputParameters(QueryLearningPlanRequest requestAO) throws ParamValidateException {
        List<String> missingParams = new ArrayList<>();
        if (StringUtils.isEmpty(requestAO.getTraceId())) {
            missingParams.add("traceId");
        }
        if (StringUtils.isEmpty(requestAO.getUserId())) {
            missingParams.add("userId");
        }
        if (StringUtils.isEmpty(requestAO.getCatalogId())) {
            missingParams.add("catalogId");
        }
        if (StringUtils.isEmpty(requestAO.getStudyCode())) {
            missingParams.add("studyCode");
        }
        // 3. 如果有缺失参数则发送错误响应
        doCheck(requestAO.getTraceId(), StringUtils.EMPTY, Collections.emptyList(), missingParams);
    }

    @Override
    public void checkInputParameters(DeleteLearningPlanRequest requestAO) throws ParamValidateException {
        List<String> missingParams = new ArrayList<>();
        if (StringUtils.isEmpty(requestAO.getTraceId())) {
            missingParams.add("traceId");
        }
        if (StringUtils.isEmpty(requestAO.getUserId())) {
            missingParams.add("userId");
        }
        if (StringUtils.isEmpty(requestAO.getCatalogId())) {
            missingParams.add("catalogId");
        }
        if (StringUtils.isEmpty(requestAO.getStudyCode())) {
            missingParams.add("studyCode");
        }
        // 3. 如果有缺失参数则发送错误响应
        doCheck(requestAO.getTraceId(), StringUtils.EMPTY, Collections.emptyList(), missingParams);
    }

    /**
     * 如果有缺失参数则发送错误响应
     */
    private void doCheck(String traceId, String errorInfo, List<String> missingHeaders, List<String> missingParams) throws ParamValidateException {
        // 3. 如果有缺失参数则发送错误响应
        if (CollectionUtils.isNotEmpty(missingHeaders) || CollectionUtils.isNotEmpty(missingParams)) {
            StringBuilder errorMsg = new StringBuilder("参数校验失败: ");
            if (CollectionUtils.isNotEmpty(missingHeaders)) {
                errorMsg.append("缺失请求头 - ").append(String.join(", ", missingHeaders));
            }
            if (CollectionUtils.isNotEmpty(missingParams)) {
                if (!missingHeaders.isEmpty()) {  // 添加花括号
                    errorMsg.append("; ");
                }
                errorMsg.append("缺失请求参数 - ").append(String.join(", ", missingParams));
            }
            errorInfo = errorMsg.toString();
        }
        if (StringUtils.isNotEmpty(errorInfo)) {
            throw new ParamValidateException(RetCode.INPUT_PARAMETERS_MISSING.getCode(), errorInfo, traceId);
        }
    }

}
