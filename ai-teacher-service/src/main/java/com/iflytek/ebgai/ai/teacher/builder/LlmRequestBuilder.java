package com.iflytek.ebgai.ai.teacher.builder;

import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.util.MapUtils;
import com.iflytek.rec.teacher.domain.agent.request.Body;
import com.iflytek.rec.teacher.domain.agent.request.Header;
import com.iflytek.rec.teacher.domain.agent.request.Request;
import com.iflytek.rec.teacher.domain.agent.request.ShortcutData;
import com.iflytek.rec.teacher.domain.agent.response.InferBody;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * LLM请求构建器
 */
public class LlmRequestBuilder {
    private XxjLlmRequest xxjLlmRequest;
    private Map<String, String> headers;
    private String workflowId = "682554b25cf2002c9fb76e1a"; // 默认值
    private String workflowVersion = "";
    private Integer agentType = 1;
    private String agentId = "";
    private String versionId = "";
    private Integer contentType = 1;
    private String content = "txt-string";
    private Boolean debugMode = true;
    private Integer shortcutType = 0;
    private String bizCode = "ZSY_XXJ";
    private String bizAction;
    private String functionCode;

    private String graphVersion;

    private LlmRequestBuilder() {
    }

    public static LlmRequestBuilder create() {
        return new LlmRequestBuilder();
    }

    public LlmRequestBuilder withXxjLlmRequest(XxjLlmRequest xxjLlmRequest) {
        this.xxjLlmRequest = xxjLlmRequest;
        return this;
    }

    public LlmRequestBuilder withHeaders(Map<String, String> headers) {
        this.headers = headers;
        return this;
    }

    public LlmRequestBuilder withBizCode(String bizCode) {
        this.bizCode = bizCode;
        return this;
    }

    public LlmRequestBuilder withWorkflowId(String workflowId) {
        this.workflowId = workflowId;
        return this;
    }

    public LlmRequestBuilder withWorkflowVersion(String workflowVersion) {
        this.workflowVersion = workflowVersion;
        return this;
    }

    public LlmRequestBuilder withAgentType(Integer agentType) {
        this.agentType = agentType;
        return this;
    }

    public LlmRequestBuilder withVersionId(String versionId) {
        this.versionId = versionId;
        return this;
    }

    public LlmRequestBuilder withContentType(Integer contentType) {
        this.contentType = contentType;
        return this;
    }

    public LlmRequestBuilder withContent(String content) {
        this.content = content;
        return this;
    }

    public LlmRequestBuilder withDebugMode(Boolean debugMode) {
        this.debugMode = debugMode;
        return this;
    }

    public LlmRequestBuilder withShortcutType(Integer shortcutType) {
        this.shortcutType = shortcutType;
        return this;
    }

    public LlmRequestBuilder withAgentId(String agentId) {
        this.agentId = agentId;
        return this;
    }

    public LlmRequestBuilder withBizAction(String bizAction) {
        this.bizAction = bizAction;
        return this;
    }

    public LlmRequestBuilder withFunctionCode(String functionCode) {
        this.functionCode = functionCode;
        return this;
    }

    public LlmRequestBuilder withGraphVersion(String graphVersion) {
        this.graphVersion = graphVersion;
        return this;
    }

    public LlmRequest build() {
        if (xxjLlmRequest == null || headers == null) {
            throw new IllegalArgumentException("XxjLlmRequest and headers are required");
        }

        LlmRequest llmRequest = new LlmRequest();
        InferBody inferBody = new InferBody();
        inferBody.setCatalogId(xxjLlmRequest.getInferBody().getCatalogId());
        inferBody.setPlanIds(xxjLlmRequest.getInferBody().getPlanIds());
        Request request = new Request();
        Header header = new Header();
        Body body = new Body();
        llmRequest.setInferBody(inferBody);
        inferBody.setRequest(request);
        request.setBody(body);
        request.setHeader(header);

        //设置场景信息
        setSceneInfo(llmRequest);
        //设置回话信息
        setSeesionInfo(llmRequest);
        //设置头信息
        setHeader(header, headers);
        //设置body
        setBody(body, xxjLlmRequest);

        return llmRequest;
    }

    private void setSeesionInfo(LlmRequest llmRequest) {
        SessionInfo sessionInfo = new SessionInfo();
        if (null != xxjLlmRequest.getSessionInfo()) {
            BeanUtils.copyProperties(xxjLlmRequest.getSessionInfo(), sessionInfo);
        }
        sessionInfo.setTraceId(headers.get("x-trace-id"));
        llmRequest.setSessionInfo(sessionInfo);
    }

    private void setSceneInfo(LlmRequest llmRequest) {
        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizCode(bizCode);
        sceneInfo.setBizAction(bizAction);
        sceneInfo.setFunctionCode(functionCode);
        sceneInfo.setUserId(headers.get("x-user-id"));
        sceneInfo.setUserName(headers.get("x-user-name"));
        sceneInfo.setGraphVersion(graphVersion);
        sceneInfo.setSubjectCode(xxjLlmRequest.getSceneInfo().getSubjectCode());
        sceneInfo.setPhaseCode(xxjLlmRequest.getSceneInfo().getPhaseCode());
        sceneInfo.setBookCode(xxjLlmRequest.getSceneInfo().getBookCode());
        sceneInfo.setAreaCode(xxjLlmRequest.getSceneInfo().getAreaCode());
        sceneInfo.setStudyCode(BizConstants.StudyCodes.SYNC_TUTORING.getCode());
        sceneInfo.setPressCode(xxjLlmRequest.getSceneInfo().getPressCode());
        llmRequest.setSceneInfo(sceneInfo);
    }

    private void setBody(Body body, XxjLlmRequest xxjLlmRequest) {
        String chatId = xxjLlmRequest.getInferBody().getChatId();
        Map<String, String> customVariables = new HashMap<>();
        String queryContent = xxjLlmRequest.getInferBody().getContent();
        if (StringUtils.isNotEmpty(queryContent)) {
            customVariables.put("content", queryContent);
        }
        body.setCustomVariables(customVariables);
        body.setChatId(chatId);

        // 设置基础属性
        body.setAgentType(agentType);
        body.setVersionId(versionId);
        body.setAgentId(agentId);
        // 设置工作流信息
        ShortcutData shortcutData = new ShortcutData();
        shortcutData.setShortcutType(0);
        shortcutData.setWorkflowId(workflowId);
        shortcutData.setWorkflowVersion(workflowVersion);
        shortcutData.setShortcutType(shortcutType);
        body.setShortcutData(shortcutData);

        // 设置其他属性
        body.setContentType(contentType);
        body.setContent(content);
        body.setChatId(xxjLlmRequest.getInferBody().getChatId());
        body.setDebugMode(debugMode);
    }

    private static void setHeader(Header header, Map<String, String> headers) {
        String authorization = MapUtils.getString(headers, "authorization");
        String appId = MapUtils.getString(headers, "x-appid");
        String xDeviceId = MapUtils.getString(headers, "x-device-id");
        String xPlatform = MapUtils.getString(headers, "x-platform");
        String xTraceId = MapUtils.getString(headers, "x-trace-id");
        String xUserId = MapUtils.getString(headers, "x-user-id");
        String xForwardedFor = MapUtils.getString(headers, "x-forwarded-for");
        String xUserTag = MapUtils.getString(headers, "x-user-tag");
        header.setAuthorization(authorization);
        header.setX_APPID(appId);
        header.setX_DEVICE_ID(xDeviceId);
        header.setX_PLATFORM(xPlatform);
        header.setX_TRACE_ID(xTraceId);
        header.setX_USER_ID(xUserId);
        header.setX_FORWARDED_FOR(xForwardedFor);
        header.setX_USER_TAG(xUserTag);
    }
}
