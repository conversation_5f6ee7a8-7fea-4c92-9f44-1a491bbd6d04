package com.iflytek.ebgai.ai.teacher.mapper;

import cn.hutool.core.bean.BeanUtil;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.CoreLiteracy;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitData;
import com.iflytek.rec.teacher.domain.model.UserPortraits;
import com.iflytek.rec.teacher.interfaces.param.UserPortraitResponse;

/**
 * @Author:huizhang68
 * @Date:2025/5/28
 */
public class UserPortraitDataMapping {

    private volatile static UserPortraitDataMapping instance;

    private UserPortraitDataMapping() {
    }

    public static UserPortraitDataMapping getInstance() {
        if (instance == null) {
            synchronized (UserPortraitDataMapping.class) {
                if (instance == null) {
                    instance = new UserPortraitDataMapping();
                }
            }
        }
        return instance;
    }

    public UserPortraitData convert(UserPortraitResponse engineResponse, String traceId) {
        UserPortraits responseUserPortraits = engineResponse.getUserPortraits();
        com.iflytek.rec.teacher.domain.pojo.CoreLiteracy respCoreLiteracy = responseUserPortraits.getCoreLiteracy();

        UserPortraitData entityData = new UserPortraitData();
        entityData.setUserId(responseUserPortraits.getUserId());
        entityData.setUserName(responseUserPortraits.getUserName());
        entityData.setUserRank(responseUserPortraits.getUserRank() + "");
        entityData.setWorkState(responseUserPortraits.getWorkState());
        entityData.setStudyTarget(responseUserPortraits.getStudyTarget());

        CoreLiteracy coreLiteracyEntity = new CoreLiteracy();
        BeanUtil.copyProperties(respCoreLiteracy, coreLiteracyEntity);
        entityData.setCoreLiteracy(coreLiteracyEntity);
        entityData.setUserLevel(responseUserPortraits.getUserLevel());
        entityData.setTraceId(traceId);
        return entityData;
    }

}
