package com.iflytek.ebgai.ai.teacher.entity.response.plan;

import lombok.Data;

import java.util.List;
import java.util.ArrayList;

@Data
public class CommonLlmResource {
    // NodeInfo内部类 - 从KnowledgeClusterLlmResource和LearnPlanLlmResource中提取
    @Data
    public static class NodeInfo {
        private int order;
        private String nodeId;
        private String nodeName;
        private String nodeType;
        private String nodeAttribute;
        private String learndBehavior;
        private int learndTimes;
        private String showState = "MAINTAIN"; // 默认值
        private Double masterScore;
        private String catalogId;

        private NodeInfo(Builder builder) {
            this.order = builder.order;
            this.nodeId = builder.nodeId;
            this.nodeName = builder.nodeName;
            this.nodeType = builder.nodeType;
            this.nodeAttribute = builder.nodeAttribute;
            this.learndBehavior = builder.learndBehavior;
            this.learndTimes = builder.learndTimes;
            this.showState = builder.showState;
            this.masterScore = builder.masterScore;
            this.catalogId = builder.catalogId;
        }

        public static class Builder {
            private int order;
            private String nodeId;
            private String nodeName;
            private String nodeType;
            private String nodeAttribute;
            private String learndBehavior;
            private int learndTimes;
            private String showState = "MAINTAIN";
            private Double masterScore;
            private String catalogId;

            public Builder order(int order) {
                this.order = order;
                return this;
            }

            public Builder nodeId(String nodeId) {
                this.nodeId = nodeId;
                return this;
            }

            public Builder nodeName(String nodeName) {
                this.nodeName = nodeName;
                return this;
            }

            public Builder nodeType(String nodeType) {
                this.nodeType = nodeType;
                return this;
            }

            public Builder nodeAttribute(String nodeAttribute) {
                this.nodeAttribute = nodeAttribute;
                return this;
            }

            public Builder learndBehavior(String learndBehavior) {
                this.learndBehavior = learndBehavior;
                return this;
            }

            public Builder learndTimes(int learndTimes) {
                this.learndTimes = learndTimes;
                return this;
            }

            public Builder showState(String showState) {
                this.showState = showState;
                return this;
            }

            public Builder masterScore(Double masterScore) {
                this.masterScore = masterScore;
                return this;
            }

            public Builder catalogId(String catalogId) {
                this.catalogId = catalogId;
                return this;
            }

            public NodeInfo build() {
                return new NodeInfo(this);
            }
        }
    }

    // SummaryInfo内部类 - 从LearnPlanLlmResource中提取
    @Data
    public static class SummaryInfo {
        private List<Summary> summaries;
        private String summaryType;

        private SummaryInfo(Builder builder) {
            this.summaries = builder.summaries;
            this.summaryType = builder.summaryType;
        }

        public static class Builder {
            private List<Summary> summaries;
            private String summaryType;

            public Builder summaries(List<Summary> summaries) {
                this.summaries = summaries;
                return this;
            }

            public Builder summaryType(String summaryType) {
                this.summaryType = summaryType;
                return this;
            }

            public SummaryInfo build() {
                return new SummaryInfo(this);
            }
        }

        @Data
        public static class Summary {
            private String headline;
            private int order;
            private List<String> contents;

            private Summary(Builder builder) {
                this.headline = builder.headline;
                this.order = builder.order;
                this.contents = builder.contents;
            }

            public static class Builder {
                private String headline;
                private int order;
                private List<String> contents;

                public Builder headline(String headline) {
                    this.headline = headline;
                    return this;
                }

                public Builder order(int order) {
                    this.order = order;
                    return this;
                }

                public Builder contents(List<String> contents) {
                    this.contents = contents;
                    return this;
                }

                public Summary build() {
                    return new Summary(this);
                }
            }
        }
    }

    // NaireInfo内部类 - 从DialogueLlmResource中提取
    @Data
    public static class NaireInfo {
        private Integer index;
        private String dimension;
        private String roundId;
        private String questionContent;
        private List<Option> options;

        private NaireInfo(Builder builder) {
            this.index = builder.index;
            this.dimension = builder.dimension;
            this.roundId = builder.roundId;
            this.questionContent = builder.questionContent;
            this.options = builder.options;
        }

        public static class Builder {
            private Integer index;
            private String dimension;
            private String roundId;
            private String questionContent;
            private List<Option> options = new ArrayList<>();

            public Builder index(Integer index) {
                this.index = index;
                return this;
            }

            public Builder dimension(String dimension) {
                this.dimension = dimension;
                return this;
            }

            public Builder roundId(String roundId) {
                this.roundId = roundId;
                return this;
            }

            public Builder questionContent(String questionContent) {
                this.questionContent = questionContent;
                return this;
            }

            public Builder options(List<Option> options) {
                this.options = options;
                return this;
            }

            public Builder addOptions(List<Option> options) {
                this.options.addAll(options);
                return this;
            }

            public NaireInfo build() {
                return new NaireInfo(this);
            }
        }

        @Data
        public static class Option {
            private int order;
            private String content;

            private Option(Builder builder) {
                this.order = builder.order;
                this.content = builder.content;
            }

            public static class Builder {
                private int order;
                private String content;

                public Builder order(int order) {
                    this.order = order;
                    return this;
                }

                public Builder content(String content) {
                    this.content = content;
                    return this;
                }

                public Option build() {
                    return new Option(this);
                }
            }
        }
    }
} 