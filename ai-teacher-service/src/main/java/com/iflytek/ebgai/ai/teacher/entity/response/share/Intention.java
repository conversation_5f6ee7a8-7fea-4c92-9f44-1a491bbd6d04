package com.iflytek.ebgai.ai.teacher.entity.response.share;// Intention.java

import lombok.Data;

import java.util.List;

/**
 * 意图信息
 */
@Data
public class Intention {
    /**
     * 工作流名称
     */
    private String plugin;

    /**
     * 大模型功能名
     */
    private String llmFunction;

    /**
     * 文本化结果
     */
    private String results;

    /**
     * 流式输出 思维链
     */
    private String fluxResults;
    /**
     * 格式化总结信息
     */
    private SummaryInfo summaryInfo;

    /**
     * 点信息列表(N)
     */
    private List<NodeInfo> nodeInfos;

    /**
     * 学习规划总时间(N)
     */
    private Integer planTimes;

    /**
     * 问答进展(N)
     */
    private Integer step;

    /**
     * 问答总数(N)
     */
    private Integer allSteps;

    /**
     * 课前聊一聊内容(N)
     */
    private NaireInfo naireInfo;
}