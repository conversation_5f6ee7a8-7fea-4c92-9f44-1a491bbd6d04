package com.iflytek.ebgai.ai.teacher.entity.response.cluster;

import com.iflytek.ebgai.ai.teacher.entity.response.share.Intention;
import com.iflytek.ebgai.ai.teacher.entity.response.share.StorageInfo;
import lombok.Data;

/**
 * 聚类大模型资源出参
 */
@Data
public class XxjClusterLlmResource {
    /**
     * 是否入库存储，默认false
     */
    private Boolean isStorage;

    /**
     * 是否终止，终止后流式输出结束，需要从intention取最终结果，默认false
     */
    private Boolean isEnd;

    /**
     * 流式是否终止，终止后流式输出结束（该字段主要用于区分思维链是否结束）
     */
    private Boolean isFluxEnd;

    /**
     * 是否动效 =true时，播放nodeInfos中信息，默认false
     */
    private Boolean isDynamic;

    /**
     * 是否刷新点掌握度 =true时，结合入参构造掌握度数据结构刷库，默认false
     */
    private Boolean isUpdateMastery;

    /**
     * 原始大模型输出后解析出的格式化结果，isEnd=true时有结果
     */
    private Intention intention;

    /**
     * 大模型原始输出存储信息（isStorage=true时需要入库）
     */
    private StorageInfo storageInfo;
}