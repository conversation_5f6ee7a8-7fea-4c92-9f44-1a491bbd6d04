package com.iflytek.ebgai.ai.teacher.util;

import com.iflytek.ebgai.ai.teacher.consts.HeaderConstants;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

public class HttpServletRequestUtils {

    private static final String[] REQUIRED_HEADERS = {HeaderConstants.X_TRACE_ID, HeaderConstants.X_USER_ID, HeaderConstants.X_USER_NAME, HeaderConstants.X_DEVICE_ID, HeaderConstants.X_APPID, HeaderConstants.X_PLATFORM, HeaderConstants.X_USER_TAG};

    public static List<String> checkHeaders(HttpServletRequest request) {
        // 1. 检查必传请求头
        List<String> missingHeaders = new ArrayList<>();
        for (String header : REQUIRED_HEADERS) {
            if (request.getHeader(header) == null || request.getHeader(header).isEmpty()) {
                missingHeaders.add(header);
            }
        }
        return missingHeaders;
    }
}
