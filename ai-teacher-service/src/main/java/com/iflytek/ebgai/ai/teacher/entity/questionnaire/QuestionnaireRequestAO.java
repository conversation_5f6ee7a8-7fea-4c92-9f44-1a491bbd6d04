package com.iflytek.ebgai.ai.teacher.entity.questionnaire;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 问卷请求实体
 */
@Data
@Builder
@ApiModel(description = "问卷请求实体")
public class QuestionnaireRequestAO {

    @NotNull(message = "场景信息不能为空")
    @ApiModelProperty(value = "场景信息", required = true)
    private QuestionnaireRequestAO.SceneInfoAO sceneInfo;

    @NotNull(message = "会话信息不能为空")
    @ApiModelProperty(value = "会话信息", required = true)
    private QuestionnaireRequestAO.SessionInfoAO sessionInfo;

    /**
     * 场景信息类，用于保存场景信息、用户上下文信息
     */
    @Data
    @Builder
    public static class SceneInfoAO {
        @NotNull(message = "学科代码不能为空")
        @ApiModelProperty(value = "学科代码（学科资源中心定义）", required = true)
        private String subjectCode;

        @NotNull(message = "学段编码不能为空")
        @ApiModelProperty(value = "学段编码（学科资源中心定义）", required = true)
        private String phaseCode;

        @ApiModelProperty(value = "教材书本（学科资源中心定义）")
        private String bookCode;

        @ApiModelProperty(value = "区域编号（学科资源中心定义）")
        private String areaCode;

        @ApiModelProperty(value = "教材版本（学科资源中心定义）")
        private String pressCode;
    }

    /**
     * 推荐会话相关信息（一轮推荐）
     */
    @Data
    @Builder
    public static class SessionInfoAO {

        @NotNull(message = "问卷轮次不能为空")
        @ApiModelProperty(value = "当前请求第几个问卷。已推问卷数=roundRecNum-1 从1开始", required = true)
        private Integer roundRecNum;

        @NotNull(message = "本轮ID不能为空")
        @ApiModelProperty(value = "本轮id 一轮问卷流程内唯一", required = true)
        private String roundId;
    }
} 