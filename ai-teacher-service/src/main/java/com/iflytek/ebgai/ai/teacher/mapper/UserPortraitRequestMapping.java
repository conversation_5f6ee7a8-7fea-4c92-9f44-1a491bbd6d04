package com.iflytek.ebgai.ai.teacher.mapper;

import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.consts.HeaderConstants;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireAnswerRequestAO;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireRequestAO;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.interfaces.param.UserPortraitRequest;

import java.util.Map;

/**
 * @Author:huizhang68
 * @Date:2025/5/28
 */
public class UserPortraitRequestMapping {

    private volatile static UserPortraitRequestMapping instance;

    private UserPortraitRequestMapping() {
    }

    public static UserPortraitRequestMapping getInstance() {
        if (instance == null) {
            synchronized (UserPortraitRequestMapping.class) {
                if (instance == null) {
                    instance = new UserPortraitRequestMapping();
                }
            }
        }
        return instance;
    }

    public UserPortraitRequest convert(QuestionnaireAnswerRequestAO requestAO, Map<String, String> headers, String graphVersion) {
        UserPortraitRequest request = new UserPortraitRequest();
        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizCode(BizConstants.BIZ_CODE_DEFAULT);
        sceneInfo.setBizAction(BizConstants.BizAction.AI_TUTORING_TEACHER_QUSNAIRE.getCode());

        QuestionnaireRequestAO.SceneInfoAO sceneInfoAO = requestAO.getSceneInfo();
        sceneInfo.setSubjectCode(sceneInfoAO.getSubjectCode());
        sceneInfo.setPhaseCode(sceneInfoAO.getPhaseCode());
        sceneInfo.setBookCode(sceneInfoAO.getBookCode());
        sceneInfo.setAreaCode(sceneInfoAO.getAreaCode());
        sceneInfo.setFunctionCode(BizConstants.FunctionCode.QUSNAIRE_USERPORTRAIT.getCode());
        sceneInfo.setUserId(headers.get(HeaderConstants.X_USER_ID));
        sceneInfo.setUserName(headers.get(HeaderConstants.X_USER_NAME));
        sceneInfo.setStudyCode(BizConstants.StudyCodes.SYNC_TUTORING.getCode());
        sceneInfo.setPressCode(sceneInfoAO.getPressCode());
        sceneInfo.setGraphVersion(graphVersion);

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(headers.get(HeaderConstants.X_TRACE_ID));

        QuestionnaireAnswerRequestAO.AnswerInfoAO answerInfoAO = requestAO.getAnswerInfo();
        sessionInfo.setRoundRecNum(answerInfoAO.getRoundRecNum());
        sessionInfo.setRoundId(answerInfoAO.getRoundId());

        request.setSceneInfo(sceneInfo);
        request.setSessionInfo(sessionInfo);

        return request;
    }
}
