//package com.iflytek.ebgai.ai.teacher.dubbo.service.impl;
//
//
//import com.alibaba.fastjson2.JSON;
//import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
//import com.iflytek.ebgai.ai.teacher.dataapi.AITeacherDataHub;
//import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoData;
//import com.iflytek.ebgai.ai.teacher.dataapi.entity.StorageInfoRequest;
//import com.iflytek.ebgai.ai.teacher.dubbo.mapper.StorageInfoMapping;
//import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferRequest;
//import com.iflytek.ebgai.ai.teacher.dubbo.model.question.QueryQuestionnaireInferResponse;
//import com.iflytek.ebgai.ai.teacher.dubbo.model.question.StorageInfo;
//import com.iflytek.ebgai.ai.teacher.dubbo.service.QuestionnaireInferApiService;
//import com.iflytek.ebgai.ai.teacher.service.questionnaire.QuestionnaireCheckParamService;
//import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;
//import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
//import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Comparator;
//import java.util.List;
//import java.util.Optional;
//
/// **
// * @Author:huizhang68
// * @Date:2025/5/24
// */
//@Slf4j
//@Component("questionnaireInferApiService")
//public class QuestionnaireInferApiServiceImpl implements QuestionnaireInferApiService {
//
//    @Autowired
//    private QuestionnaireCheckParamService checkParamService;
//
//    @Override
//    @ServiceRequestMetrics(desc = "dubbo-获取问卷")
//    public CommonResponse<QueryQuestionnaireInferResponse> query(QueryQuestionnaireInferRequest requestAO) {
//        String traceId = requestAO.getTraceId();
//        log.info("[TraceID:{}] [DUBBO][QUERY_QUESTIONNAIRE][REQUEST] {}", traceId, JSON.toJSONString(requestAO));
//        checkParamService.checkInputParameters(requestAO);
//        QueryQuestionnaireInferResponse res = new QueryQuestionnaireInferResponse();
//        StorageInfoRequest dataApiReq = new StorageInfoRequest();
//        dataApiReq.setTraceId(requestAO.getTraceId());
//        dataApiReq.setUserId(requestAO.getUserId());
//        dataApiReq.setFunctionCode(BizConstants.FunctionCode.QUSNAIRE_EXPLAIN.getCode());
//        try {
//            CommonResponse<QueryQuestionnaireInferResponse> response = new CommonResponse<QueryQuestionnaireInferResponse>().success(requestAO.getTraceId());
//            List<StorageInfoData> dataApiResp = AITeacherDataHub.getStorageInfoService().query(dataApiReq);
//            if (CollectionUtils.isEmpty(dataApiResp)) {
//                log.info("[TraceID:{}] [DUBBO][QUERY_QUESTIONNAIRE][EMPTY]", traceId);
//                response.setTraceId(traceId);
//                response.setData(res);
//                return response;
//            }
//            Optional<StorageInfoData> lastRecord = dataApiResp.stream().max(Comparator.comparingLong(StorageInfoData::getUpdateTime));
//            if (lastRecord.isPresent()) {
//                StorageInfoData find = lastRecord.get();
//                StorageInfo storageInfo = StorageInfoMapping.getInstance().convert(find);
//                res.setStorageInfo(storageInfo);
//            }
//            log.info("[TraceID:{}] [DUBBO][QUERY_QUESTIONNAIRE][RESPONSE] {}", traceId, JSON.toJSONString(res));
//            response.setData(res);
//            return response;
//        } catch (Exception e) {
//            log.error("[TraceID:{}] [DUBBO][QUERY_QUESTIONNAIRE][ERROR]", traceId, e);
//            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), e.getMessage(), traceId);
//        }
//    }
//}
