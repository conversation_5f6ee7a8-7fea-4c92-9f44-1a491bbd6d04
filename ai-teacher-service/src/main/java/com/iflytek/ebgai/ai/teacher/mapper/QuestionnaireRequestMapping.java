package com.iflytek.ebgai.ai.teacher.mapper;

import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.consts.HeaderConstants;
import com.iflytek.ebgai.ai.teacher.entity.questionnaire.QuestionnaireRequestAO;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.interfaces.param.QuestionnaireRequest;

import java.util.Map;

/**
 * @Author:huizhang68
 * @Date:2025/5/28
 */
public class QuestionnaireRequestMapping {

    private volatile static QuestionnaireRequestMapping instance;

    private QuestionnaireRequestMapping() {
    }

    public static QuestionnaireRequestMapping getInstance() {
        if (instance == null) {
            synchronized (QuestionnaireRequestMapping.class) {
                if (instance == null) {
                    instance = new QuestionnaireRequestMapping();
                }
            }
        }
        return instance;
    }

    public QuestionnaireRequest convert(QuestionnaireRequestAO requestAO, Map<String, String> headers, String graphVersion) {
        QuestionnaireRequest questionnaireRequest = new QuestionnaireRequest();
        SceneInfo sceneInfo = new SceneInfo();
        QuestionnaireRequestAO.SceneInfoAO sceneInfoAO = requestAO.getSceneInfo();
        QuestionnaireRequestAO.SessionInfoAO sessionInfoAO = requestAO.getSessionInfo();
        sceneInfo.setBizAction(BizConstants.BizAction.AI_TUTORING_TEACHER_QUSNAIRE.getCode());
        sceneInfo.setSubjectCode(sceneInfoAO.getSubjectCode());
        sceneInfo.setPhaseCode(sceneInfoAO.getPhaseCode());
        sceneInfo.setBookCode(sceneInfoAO.getBookCode());
        sceneInfo.setAreaCode(sceneInfoAO.getAreaCode());
        sceneInfo.setFunctionCode(BizConstants.FunctionCode.QUSNAIRE_RECNAIRE.getCode());
        sceneInfo.setUserId(headers.get(HeaderConstants.X_USER_ID));
        sceneInfo.setUserName(headers.get(HeaderConstants.X_USER_NAME));
        sceneInfo.setPressCode(sceneInfoAO.getPressCode());
        sceneInfo.setGraphVersion(graphVersion);
        questionnaireRequest.setSceneInfo(sceneInfo);

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(headers.get(HeaderConstants.X_TRACE_ID));
        sessionInfo.setRoundRecNum(sessionInfoAO.getRoundRecNum());
        sessionInfo.setRoundId(sessionInfoAO.getRoundId());
        questionnaireRequest.setSessionInfo(sessionInfo);

        return questionnaireRequest;
    }
}
