package com.iflytek.ebgai.ai.teacher.controller.filter.token;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> @data
 * @desc 鉴权响应类
 */
@Data
public class AuthResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    private String retCode;
    private String accessToken;
    private String expiresIn;

    public boolean checkCheckSuccess() {
        return AuthConstant.CHECK_TOKEN_SUCCESS_CODE.equals(retCode);
    }

    public boolean checkGetSuccess() {
        return AuthConstant.GET_TOKEN_RET_SUCCESS_CODE.equals(retCode);
    }
}