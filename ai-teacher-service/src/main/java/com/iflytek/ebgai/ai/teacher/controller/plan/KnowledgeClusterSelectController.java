package com.iflytek.ebgai.ai.teacher.controller.plan;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.builder.KnowledgeClusterLlmResourceBuilder;
import com.iflytek.ebgai.ai.teacher.builder.LlmRequestBuilder;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.entity.request.XxjLlmRequest;
import com.iflytek.ebgai.ai.teacher.entity.request.share.InferBody;
import com.iflytek.ebgai.ai.teacher.entity.request.share.SceneInfo;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.entity.response.plan.KnowledgeClusterLlmResource;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.factory.LlmType;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.rec.teacher.interfaces.param.LlmRequest;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.scheduler.Schedulers;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;

/**
 * 知识簇智能推荐控制器
 * <p>
 * <ul>
 *   <li>负责处理知识簇智能推荐相关请求</li>
 *   <li>集成链路追踪与异常处理，提升可观测性</li>
 *   <li>线程安全，由Spring容器管理</li>
 *   <li>仅限于知识簇推荐业务场景</li>
 * </ul>
 * <p>
 * 该类通常用于学习规划中知识簇推荐的接口服务。
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/v1/plan", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
public class KnowledgeClusterSelectController extends BasePlanController {

    private static final String ERROR_MESSAGE_LOG = "知识簇选择处理失败，traceId:{}，错误信息:{}";

    private final TraceUtils traceUtils;

    public KnowledgeClusterSelectController(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    /**
     * 知识簇推荐接口
     * <p>
     * 接收请求参数并调用引擎完成知识簇智能推荐。
     *
     * @param xxjLlmRequest 知识簇推荐请求参数
     * @param request       HTTP请求对象
     * @return 推荐结果包装响应
     * @throws IOException 参数校验或处理异常
     * <AUTHOR>
     * @since 1.8
     */
    @ServiceRequestMetrics(desc = "知识簇选择")
    @ApiOperation(value = "知识簇选择", notes = "知识簇选择")
    @PostMapping(value = "/knowledge/cluster")
    @SkylineTraceStarter(typePrefix = "knowledgeClusterSelect#", isSync4Request = false)
    public CommonResponse<KnowledgeClusterLlmResource> knowledgeClusterSelect(@RequestBody XxjLlmRequest xxjLlmRequest, HttpServletRequest request) throws IOException {
        //参数校验
        checkInputParameters(xxjLlmRequest, request);
        final String traceId = HttpUtils.getTraceId(request);

        Map<String, String> headers = HttpUtils.getHeadersMap(request);

        try {
            // 1. 构建LLM请求对象
            LlmRequest llmRequest = buildLlmRequest(xxjLlmRequest, headers);
            log.info("[TraceID:{}] 知识簇选择引擎接口开始: {}", traceId, JSON.toJSONString(llmRequest));
            traceUtils.record("knowledgeClusterSelectEngineRequest", llmRequest);
            long startTime = System.currentTimeMillis();
            // 2. 同步获取单条资源响应
            LlmResource resource = engineFacade.selectKnowledgeCluster(llmRequest);
            long cost = System.currentTimeMillis() - startTime;
            traceUtils.record("knowledgeSelectEngineCost", Maps.of("cost", cost, "traceId", traceId));
            log.info("[TraceID:{}] 知识簇选择引擎接口耗时: {} ms", traceId, cost);

            if (resource == null) {
                traceUtils.recordErr("knowledgeClusterSelectEngineReponseError", "知识簇选择引擎返回空!");
                log.error("[TraceID:{}] 知识簇选择引擎返回空!", traceId);
                return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), "知识簇选择引擎返回空!", traceId);
            }
            traceUtils.record("knowledgeClusterSelectEngineResponse", resource);
            return buildSuccessResponse(resource, traceId);

        } catch (EngineException ex) {
            traceUtils.recordErr("knowledgeClusterSelectEngineException", ex);
            log.error("[TraceID:{}] 学习规划引擎接口调用异常：[Code:{}] - {}", traceId, ex.getErrorCode(), ex.getMessage(), ex);
            CommonResponse errorResponse = new CommonResponse<>(String.valueOf(ex.getErrorCode()), ex.getMessage(), traceId);
            return errorResponse;
        } catch (Throwable error) { // 捕获所有异常和错误
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            error.printStackTrace(pw);  // 将堆栈写入 PrintWriter
            String fullStackTrace = sw.toString();
            traceUtils.recordErr("knowledgeClusterSelectEngineError", fullStackTrace);
            log.error(ERROR_MESSAGE_LOG, traceId, fullStackTrace);
            return handleErrorResponse(error, traceId);
        }
    }

    // === 私有辅助方法 ===
    private LlmRequest buildLlmRequest(XxjLlmRequest xxjLlmRequest, Map<String, String> headers) {
        return LlmRequestBuilder.create().withXxjLlmRequest(xxjLlmRequest).withHeaders(headers).withBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode()).withFunctionCode(BizConstants.FunctionCode.LEARNINGPLAN_CLUSTERCHOICE.getCode()).withBizCode(BizConstants.BIZ_CODE_DEFAULT).withDebugMode(debugMode).withAgentId(agentId).withShortcutType(shortcuttype).withWorkflowId(deepseekrV3Workflowid).withGraphVersion(graphVersion).build();
    }

    private CommonResponse<KnowledgeClusterLlmResource> buildSuccessResponse(LlmResource resource, String traceId) {
        KnowledgeClusterLlmResource clusterResource = KnowledgeClusterLlmResourceBuilder.build(resource);
        log.info("[TraceID:{}] 知识簇选择处理完成: {}", traceId, JSON.toJSONString(clusterResource));
        CommonResponse<KnowledgeClusterLlmResource> response = new CommonResponse<>(RetCode.SUCCESS.getCode(), RetCode.SUCCESS.getDesc(), traceId);
        response.setData(clusterResource);
        return response;
    }

    private CommonResponse<KnowledgeClusterLlmResource> handleErrorResponse(Throwable error, String traceId) {
        CommonResponse<String> errorResponse = handleError(error, traceId);
        return new CommonResponse<>(errorResponse.getCode(), errorResponse.getMessage(), traceId);
    }

    private void checkInputParameters(XxjLlmRequest xxjLlmRequest, HttpServletRequest request) throws IOException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = checkHeaders(request);
        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();

        if (xxjLlmRequest.getSceneInfo() == null) {
            missingParams.add("sceneInfo");
        } else {
            SceneInfo sceneInfo = xxjLlmRequest.getSceneInfo();
            if (sceneInfo.getSubjectCode() == null || sceneInfo.getSubjectCode().isEmpty()) {
                missingParams.add("sceneInfo.subjectCode");
            }
            if (sceneInfo.getPhaseCode() == null || sceneInfo.getPhaseCode().isEmpty()) {
                missingParams.add("sceneInfo.phaseCode");
            }
        }

        if (xxjLlmRequest.getInferBody() == null) {
            missingParams.add("inferBody");
        } else {
            InferBody inferBody = xxjLlmRequest.getInferBody();
            if (inferBody.getCatalogId() == null || inferBody.getCatalogId().isEmpty()) {
                missingParams.add("inferBody.catalogId");
            }
            if (inferBody.getChatId() == null || inferBody.getChatId().isEmpty()) {
                missingParams.add("inferBody.chatId");
            }
        }

        // 3. 如果有缺失参数则发送错误响应
        if (!missingHeaders.isEmpty() || !missingParams.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("参数校验失败: ");

            if (!missingHeaders.isEmpty()) {
                errorMsg.append("缺失请求头 - ").append(String.join(", ", missingHeaders));
            }

            if (!missingParams.isEmpty()) {
                if (!missingHeaders.isEmpty()) {  // 添加花括号
                    errorMsg.append("; ");
                }
                errorMsg.append("缺失请求参数 - ").append(String.join(", ", missingParams));
            }

            errorInfo = errorMsg.toString();

        }
        if (StringUtils.isNotEmpty(errorInfo)) {
            log.error("[TraceID:{}] {}", HttpUtils.getTraceId(request), errorInfo);
            throw new ParamValidateException(RetCode.INPUT_PARAMETERS_MISSING.getCode(), errorInfo, request.getHeader("x-trace-id"));
        }
    }
}