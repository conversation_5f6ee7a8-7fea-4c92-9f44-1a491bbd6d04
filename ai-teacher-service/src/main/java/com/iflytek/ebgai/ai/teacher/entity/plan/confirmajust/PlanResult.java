package com.iflytek.ebgai.ai.teacher.entity.plan.confirmajust;

import javax.validation.constraints.*;

import lombok.Data;

import java.io.Serializable;

@Data
public class PlanResult implements Serializable {

    @NotBlank(message = "bizAction不能为空")
    private String bizAction;

    @NotBlank(message = "userId不能为空")
    private String userId;

    private Long updateTime;

    @NotNull(message = "order不能为空")
    @Min(value = 1, message = "order必须大于等于1")
    private Integer order;

    @NotBlank(message = "nodeId不能为空")
    private String nodeId;

    @NotNull(message = "nodeType不能为空")
    private NodeTypeEnum nodeType;

    @NotNull(message = "nodeAttribute不能为空")
    private NodeAttributeEnum nodeAttribute;

    @NotNull(message = "learndBehavior不能为空")
    private LearndBehaviorEnum learndBehavior;

    @NotNull(message = "learndTimes不能为空")
    @Min(value = 0, message = "learndTimes不能小于0")
    private Integer learndTimes;

    @NotBlank(message = "catalogId不能为空")
    private String catalogId;
}
