package com.iflytek.ebgai.ai.teacher.entity.response.explain;

import com.iflytek.ebgai.ai.teacher.entity.response.share.Intention;
import com.iflytek.ebgai.ai.teacher.entity.response.share.StorageInfo;
import lombok.Data;

/**
 * 出参主对象
 */
@Data
public class XxjExplainLlmResource {
    /**
     * 是否入库存储，默认false
     */
    private Boolean isStorage;

    /**
     * 是否终止，终止后流式输出结束，默认false
     */
    private Boolean isEnd;

    /**
     * 流式是否终止(N)
     */
    private Boolean isFluxEnd;

    /**
     * 是否动效，默认false
     */
    private Boolean isDynamic;

    /**
     * 是否刷新点掌握度(N)
     */
    private Boolean isUpdateMastery;

    /**
     * 原始大模型解析结果
     */
    private Intention intention;

    /**
     * 大模型原始输出存储信息(N)
     */
    private StorageInfo storageInfo;
}