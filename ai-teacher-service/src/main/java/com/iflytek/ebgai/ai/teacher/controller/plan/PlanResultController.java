package com.iflytek.ebgai.ai.teacher.controller.plan;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.consts.BizConstants;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.common.exception.ParamValidateException;
import com.iflytek.ebgai.ai.teacher.util.HttpUtils;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.rec.teacher.interfaces.param.PlanSearchRequest;
import com.iflytek.rec.teacher.interfaces.param.PlanSearchResponse;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.iflytek.ebgai.ai.teacher.common.annotation.ServiceRequestMetrics;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 学习规划结果查询控制器
 *
 * <AUTHOR>
 * @version 1.1
 * @Date: 2025/6/4
 * @Description: 优化请求处理流程和异常处理
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/v1/plan", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
public class PlanResultController extends BasePlanController {

    private static final String USER_ID_HEADER = "x-user-id";
    private static final String USER_NAME_HEADER = "x-user-name";
    private final TraceUtils traceUtils;

    public PlanResultController(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    @ServiceRequestMetrics(desc = "查询规划结果")
    @PostMapping("/result")
    @ApiOperation("查询规划结果")
    @SkylineTraceStarter(typePrefix = "planResult#", isSync4Request = false)
    public CommonResponse<PlanSearchResponse> planResult(@RequestBody PlanSearchRequest requestBody, HttpServletRequest servletRequest) throws IOException {
        //参数校验
        checkInputParameters(requestBody, servletRequest);
        final String traceId = HttpUtils.getTraceId(servletRequest);
        try {
            // 1. 请求参数增强
            enhanceRequest(requestBody, servletRequest, traceId);
            traceUtils.record("planResultEngineRequest", requestBody);
            log.info("[TraceID:{}] 查询规划结果请求开始: {}", traceId, JSON.toJSONString(requestBody));
            // 2. 调用引擎服务
            long startTime = System.currentTimeMillis();
            PlanSearchResponse engineResponse = engineFacade.planSearch(requestBody);
            long cost = System.currentTimeMillis() - startTime;
            traceUtils.record("planResultQueryEngineCost", Maps.of("cost", cost, "traceId", traceId));
            log.info("[TraceID:{}] 查询规划结果引擎接口耗时: {} ms", traceId, cost);
            traceUtils.record("planResultEngineResponse", engineResponse);
            // 3. 构建成功响应
            return new CommonResponse<>(RetCode.SUCCESS.getCode(), RetCode.SUCCESS.getDesc(), traceId, engineResponse);
        } catch (EngineException e) {
            traceUtils.recordErr("planResultEngineException", e);
            log.error("[TraceID:{}] 引擎服务异常 - 错误码:{} 错误信息:{}", traceId, e.getErrorCode(), e.getMessage(), e);
            return new CommonResponse<>(String.valueOf(e.getErrorCode()), e.getMessage(), traceId);
        } catch (Error error) {
            traceUtils.recordErr("planResultEngineError", error);
            log.error("[TraceID:{}] 查询规划结果系统错误: {}", traceId, error.getMessage(), error);
            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), error.getMessage(), traceId);
        } catch (Exception e) {
            traceUtils.recordErr("planResultException", e);
            log.error("[TraceID:{}] 系统内部异常: {}", traceId, e.getMessage(), e);
            return new CommonResponse<>(RetCode.AI_TEACHER_ERROR.getCode(), RetCode.AI_TEACHER_ERROR.getDesc(), traceId);
        }
    }

    /**
     * 增强请求参数
     */
    private void enhanceRequest(PlanSearchRequest request, HttpServletRequest servletRequest, String traceId) {
        // 1. 增强场景信息
        SceneInfo sceneInfo = request.getSceneInfo();
        if (sceneInfo == null) {
            sceneInfo = new SceneInfo();
            request.setSceneInfo(sceneInfo);
        }

        sceneInfo.setUserId(servletRequest.getHeader(USER_ID_HEADER));
        sceneInfo.setUserName(servletRequest.getHeader(USER_NAME_HEADER));
        sceneInfo.setBizCode(BizConstants.BIZ_CODE_DEFAULT);
        sceneInfo.setBizAction(BizConstants.BizAction.AI_TUTORING_LEARNING_PLAN.getCode());
        sceneInfo.setFunctionCode(BizConstants.FunctionCode.LEARNINGPLAN_PLAN.getCode());
        sceneInfo.setStudyCode(BizConstants.StudyCodes.SYNC_TUTORING.getCode());
        sceneInfo.setGraphVersion(graphVersion);

        // 2. 增强会话信息
        SessionInfo sessionInfo = request.getSessionInfo();
        if (sessionInfo == null) {
            sessionInfo = new SessionInfo();
            request.setSessionInfo(sessionInfo);
        }
        sessionInfo.setTraceId(traceId);
    }

    private void checkInputParameters(PlanSearchRequest requestBody, HttpServletRequest request) throws IOException {
        String errorInfo = StringUtils.EMPTY;
        List<String> missingHeaders = checkHeaders(request);
        // 2. 检查请求体参数
        List<String> missingParams = new ArrayList<>();

        if (requestBody.getSceneInfo() == null) {
            missingParams.add("sceneInfo");
        } else {
            SceneInfo sceneInfo = requestBody.getSceneInfo();
            if (sceneInfo.getSubjectCode() == null || sceneInfo.getSubjectCode().isEmpty()) {
                missingParams.add("sceneInfo.subjectCode");
            }
            if (sceneInfo.getPhaseCode() == null || sceneInfo.getPhaseCode().isEmpty()) {
                missingParams.add("sceneInfo.phaseCode");
            }
            if (sceneInfo.getBookCode() == null || sceneInfo.getBookCode().isEmpty()) {
                missingParams.add("sceneInfo.bookCode");
            }
            if (sceneInfo.getPressCode() == null || sceneInfo.getPressCode().isEmpty()) {
                missingParams.add("sceneInfo.pressCode");
            }
        }

        if (StringUtils.isEmpty(requestBody.getCatalogId())) {
            missingParams.add("catalogId");
        }


        // 3. 如果有缺失参数则发送错误响应
        if (!missingHeaders.isEmpty() || !missingParams.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("参数校验失败: ");

            if (!missingHeaders.isEmpty()) {
                errorMsg.append("缺失请求头 - ").append(String.join(", ", missingHeaders));
            }

            if (!missingParams.isEmpty()) {
                if (!missingHeaders.isEmpty()) {
                    errorMsg.append("; ");
                }
                errorMsg.append("缺失请求参数 - ").append(String.join(", ", missingParams));
            }

            errorInfo = errorMsg.toString();

        }
        if (StringUtils.isNotEmpty(errorInfo)) {
            log.error("[TraceID:{}] {}", HttpUtils.getTraceId(request), errorInfo);
            throw new ParamValidateException(RetCode.INPUT_PARAMETERS_MISSING.getCode(), errorInfo, request.getHeader("x-trace-id"));
        }
    }
}