package com.iflytek.ebgai.ai.teacher.entity.response.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LearnPlanLlmResource implements Serializable {
    @JsonProperty("isEnd")
    private boolean isEnd;
    @JsonProperty("isFluxEnd")
    private boolean isFluxEnd;
    @JsonProperty("isDynamic")
    private boolean isDynamic;
    private Intention intention;

    private LearnPlanLlmResource(Builder builder) {
        this.isEnd = builder.isEnd;
        this.isFluxEnd = builder.isFluxEnd;
        this.isDynamic = builder.isDynamic;
        this.intention = builder.intention;
    }

    public static class Builder {
        private boolean isEnd = false;
        private boolean isFluxEnd;
        private boolean isDynamic = false;
        private Intention intention;

        public Builder isEnd(boolean isEnd) {
            this.isEnd = isEnd;
            return this;
        }

        public Builder isFluxEnd(boolean isFluxEnd) {
            this.isFluxEnd = isFluxEnd;
            return this;
        }

        public Builder isDynamic(boolean isDynamic) {
            this.isDynamic = isDynamic;
            return this;
        }

        public Builder intention(Intention intention) {
            this.intention = intention;
            return this;
        }

        public LearnPlanLlmResource build() {
            return new LearnPlanLlmResource(this);
        }
    }

    @Data
    public static class Intention {
        private String results;
        private String fluxResults;
        private CommonLlmResource.SummaryInfo summaryInfo;
        private List<CommonLlmResource.NodeInfo> nodeInfos;
        private Integer planTimes;

        private Intention(Builder builder) {
            this.results = builder.results;
            this.fluxResults = builder.fluxResults;
            this.summaryInfo = builder.summaryInfo;
            this.nodeInfos = builder.nodeInfos;
            this.planTimes = builder.planTimes;
        }

        public static class Builder {
            private String results;
            private String fluxResults;
            private CommonLlmResource.SummaryInfo summaryInfo;
            private List<CommonLlmResource.NodeInfo> nodeInfos;
            private Integer planTimes;

            public Builder results(String results) {
                this.results = results;
                return this;
            }

            public Builder fluxResults(String fluxResults) {
                this.fluxResults = fluxResults;
                return this;
            }

            public Builder summaryInfo(CommonLlmResource.SummaryInfo summaryInfo) {
                this.summaryInfo = summaryInfo;
                return this;
            }

            public Builder nodeInfos(List<CommonLlmResource.NodeInfo> nodeInfos) {
                this.nodeInfos = nodeInfos;
                return this;
            }

            public Builder planTimes(Integer planTimes) {
                this.planTimes = planTimes;
                return this;
            }

            public Intention build() {
                return new Intention(this);
            }
        }
    }
}