package com.iflytek.ebgai.ai.teacher.entity.response.ajust;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.iflytek.ebgai.ai.teacher.entity.response.share.AjustInfo;
import com.iflytek.ebgai.ai.teacher.entity.response.share.NodeInfo;
import lombok.Data;

import java.util.List;

/**
 * 学习规划搜索出参
 */
@Data
public class XxjPlanSearchInfo {
    /**
     * 规划结果
     */
    private List<NodeInfo> nodeInfos;

    /**
     * 学习规划总时间，单位min
     */
    private Integer planTimes;

    /**
     * 是否可调整规划结果标识
     */
    @JsonProperty("AjustInfo") // 确保与JSON中的字段名一致
    private AjustInfo ajustInfo;
}