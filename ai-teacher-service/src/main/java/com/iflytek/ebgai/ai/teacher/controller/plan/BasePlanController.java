package com.iflytek.ebgai.ai.teacher.controller.plan;

import com.iflytek.ebgai.ai.teacher.common.entity.RetCode;
import com.iflytek.ebgai.ai.teacher.common.entity.CommonResponse;
import com.iflytek.ebgai.ai.teacher.service.AbstractEngineFacade;
import com.iflytek.ebgai.ai.teacher.service.plan.MasterServiceFacade;
import com.iflytek.ebgai.ai.teacher.service.plan.PlanResultServiceFacade;
import com.iflytek.ebgai.ai.teacher.service.plan.StorageInfoServiceFacade;
import com.iflytek.rec.teacher.exception.EngineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName BasePlanController
 * @Date: 2025/6/4 18:14
 * @Description:
 */
@Slf4j
@RestController
public class BasePlanController {
    @Autowired
    protected AbstractEngineFacade engineFacade;

    @Value("${banxue.engine.deepseekr1.workflowid}")
    protected String deepseekr1Workflowid;

    @Value("${banxue.engine.deepseekrv3.workflowid}")
    protected String deepseekrV3Workflowid;

    @Value("${banxue.engine.shortcutdata.shortcuttype:0}")
    protected Integer shortcuttype;

    @Value("${chat.app.agent.id}")
    protected String agentId;

    @Value("${banxue.engine.body.debugmode}")
    protected Boolean debugMode;

    @Value("${skylab.data.api.graph.cacheVersion}")
    protected String graphVersion;

    @Value("${banxue.sse.session.timeout}")
    protected Long timeOut;

    @Value("${banxue.retry.count:1}")
    protected Integer retryCount;

    @Autowired
    protected PlanResultServiceFacade planResultServiceFacade;

    @Autowired
    protected StorageInfoServiceFacade storageInfoServiceFacade;

    @Autowired
    protected MasterServiceFacade masterServiceFacade;


    protected CommonResponse<String> handleError(Throwable throwable, String traceId) {
        if (throwable instanceof EngineException) {
            EngineException ex = (EngineException) throwable;
            log.error("Business Error [TraceID: {}] - Code: {}, Message: {}", traceId, ex.getErrorCode(), ex.getMessage());
            return new CommonResponse<>(String.valueOf(ex.getErrorCode()), ex.getMessage(), traceId);
        } else if (throwable instanceof IOException) {
            log.error("IO Error [TraceID: {}] - {}", traceId, throwable.getMessage());
            return new CommonResponse<>("500", "数据流传输异常" + throwable.getMessage(), traceId);
        } else {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            throwable.printStackTrace(pw);  // 将堆栈写入 PrintWriter
            String fullStackTrace = sw.toString();
            log.error("System Error [TraceID: {}] - {}", traceId, fullStackTrace, throwable);

            return new CommonResponse<>("500", "系统处理异常: " + fullStackTrace, traceId);
        }
    }

    protected CommonResponse<String> handleParameterError(String errorMsg, String traceId) {
        return new CommonResponse<>(RetCode.INPUT_PARAMETERS_MISSING.getCode(), errorMsg, traceId);
    }

    protected List<String> checkHeaders(HttpServletRequest request) {

        Map<String, String> headersInfo = getAllHeaders(request);
        log.info("请求头信息: {}", headersInfo);
        // 1. 检查必传请求头
        List<String> missingHeaders = new ArrayList<>();
        String[] requiredHeaders = {"x-trace-id", "x-user-id", "x-user-name", "x-device-id", "x-appid", "x-platform", "x-user-tag"};
        for (String header : requiredHeaders) {
            if (request.getHeader(header) == null || request.getHeader(header).isEmpty()) {
                missingHeaders.add(header);
            }
        }

        return missingHeaders;
    }

    private Map<String, String> getAllHeaders(HttpServletRequest request) {
        Map<String, String> headersMap = new LinkedHashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();

        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();

            // 跳过Authorization头
            if ("authorization".equalsIgnoreCase(headerName)) {
                continue;
            }

            // 获取所有值（处理多值头）
            Enumeration<String> values = request.getHeaders(headerName);
            List<String> valueList = Collections.list(values);

            // 将多值转换为逗号分隔的字符串
            headersMap.put(headerName, String.join(", ", valueList));
        }
        return headersMap;
    }
}
