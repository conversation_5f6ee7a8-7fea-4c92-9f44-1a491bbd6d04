package com.iflytek.ebgai.ai.teacher.builder;

import com.iflytek.ebgai.ai.teacher.entity.response.plan.CommonLlmResource;
import com.iflytek.ebgai.ai.teacher.entity.response.plan.KnowledgeClusterLlmResource;
import com.iflytek.rec.teacher.domain.agent.response.Intention;
import com.iflytek.rec.teacher.domain.pojo.NodeInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName KnowledgeClusterLlmResourceBuilder
 * @Date: 2025/6/2 14:50
 * @Description:
 */
@Slf4j
public class KnowledgeClusterLlmResourceBuilder {

    public static KnowledgeClusterLlmResource build(LlmResource llmResource) {
        Intention origIntention = llmResource.getIntention();
        if (null == origIntention) {
            log.error("返回参数不合理,Intention为空:{}", origIntention);
            throw new RuntimeException("返回参数不合理,Intention空");
        }
        List<NodeInfo> origNodeInfos = origIntention.getNodeInfos();
        if (CollectionUtils.isEmpty(origNodeInfos)) {
            log.warn("返回参数 List<NodeInfo>为空:{}", origNodeInfos);
        }

        // 创建节点列表
        List<CommonLlmResource.NodeInfo> nodeInfos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(origNodeInfos)) {
            for (NodeInfo nodeInfo : origNodeInfos) {
                CommonLlmResource.NodeInfo node = new CommonLlmResource.NodeInfo.Builder().nodeId(nodeInfo.getNodeId()).nodeType(nodeInfo.getNodeType()).nodeAttribute(nodeInfo.getNodeAttribute()).nodeName("nodeName").learndBehavior(nodeInfo.getLearndBehavior()).learndTimes(nodeInfo.getLearndTimes()).catalogId(nodeInfo.getCatalogId()).order(nodeInfo.getOrder()).showState(nodeInfo.getShowState()).masterScore(nodeInfo.getMasterScore()).build();
                nodeInfos.add(node);
            }
        }


        // 创建Intention
        KnowledgeClusterLlmResource.Intention intention = new KnowledgeClusterLlmResource.Intention.Builder().nodeInfos(nodeInfos).build();

        // 创建主资源对象
        return new KnowledgeClusterLlmResource.Builder().intention(intention).isFluxEnd(llmResource.isFluxEnd()).build();
    }
}
