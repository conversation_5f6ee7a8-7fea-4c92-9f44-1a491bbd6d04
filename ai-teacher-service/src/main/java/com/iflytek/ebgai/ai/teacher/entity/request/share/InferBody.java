package com.iflytek.ebgai.ai.teacher.entity.request.share;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Data
public class InferBody {

    @NotBlank(message = "chatId不能为空")
    private String chatId;

    private String catalogId;

    private List<String> planIds;

    private String content;
}