package com.iflytek.ebgai.ai.teacher.engine;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iflytek.ebgai.ai.teacher.factory.LlmType;
import com.iflytek.ebgai.ai.teacher.service.AbstractEngineFacade;
import com.iflytek.rec.teacher.domain.agent.request.Body;
import com.iflytek.rec.teacher.domain.agent.request.Header;
import com.iflytek.rec.teacher.domain.agent.request.Request;
import com.iflytek.rec.teacher.domain.agent.request.ShortcutData;
import com.iflytek.rec.teacher.domain.agent.response.InferBody;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.interfaces.param.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.core.publisher.Flux;

import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
public class AbstractEngineFacadeTest {

    @Autowired
    private AbstractEngineFacade abstractEngineFacade;


    private LlmRequest buildRequest() {
        LlmRequest llmRequest = new LlmRequest();
        InferBody inferBody = new InferBody();
        Request request = new Request();
        Header header = new Header();
        Body body = new Body();
        llmRequest.setInferBody(inferBody);
        llmRequest.setSceneInfo(new SceneInfo());
        llmRequest.setSessionInfo(new SessionInfo());
        inferBody.setRequest(request);
        request.setBody(body);
        request.setHeader(header);

        header.setAuthorization("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhaWQiOiJ4eGpjcHgteGNqengiLCJleHAiOiIxNzQ4MzQyMDI4In0.Ai0S1_eiU_xVLyZNka3uBdqwOR-LADyTqlP6qZUZQcs");
        header.setX_APPID("xxjcpx-xcjzx");
        header.setX_DEVICE_ID("021012242400100");
        header.setX_PLATFORM("Android");
        header.setX_TRACE_ID(UUID.randomUUID().toString());
        header.setX_USER_ID("04ef1c7f-1f3b-4c8b-837f-94b8d2160e88");
        header.setX_USER_TAG("tagtest");
        llmRequest.getInferBody().getRequest().setHeader(header);

        body.setAgentType(1);
        body.setVersionId("");
        ShortcutData shortcutData = new ShortcutData();
        shortcutData.setShortcutType(0);
        shortcutData.setWorkflowVersion("");
        body.setShortcutData(shortcutData);
        body.setContentType(1);
        body.setContent("txt-string");
        body.setDebugMode(true);
        Map<String, String> map = new HashMap<>();
        map.put("content", "用五句话回复我介绍下科大讯飞");
        body.setCustomVariables(map);
        body.setChatId(UUID.randomUUID().toString());
        return llmRequest;
    }

    private SceneInfo createSceneInfo() {
        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizAction("QUESTIONNAIRE");
        sceneInfo.setSubjectCode("MATH");
        sceneInfo.setPhaseCode("MIDDLE_SCHOOL");
        sceneInfo.setBookCode("BOOK001");
        sceneInfo.setBookName("Mathematics Book");
        sceneInfo.setAreaCode("AREA001");
        sceneInfo.setFunctionCode("FUNC001");
        sceneInfo.setUserId(UUID.randomUUID().toString());
        sceneInfo.setUserName("Test User");
        sceneInfo.setGraphVersion("1.0");
        sceneInfo.setPressCode("PRESS001");
        return sceneInfo;
    }

    private SessionInfo createSessionInfo() {
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(UUID.randomUUID().toString());
        sessionInfo.setRoundRecNum(1);
        sessionInfo.setRoundId(UUID.randomUUID().toString());
        return sessionInfo;
    }

    private Request createRequest() {
        Request request = new Request();
        // 根据实际业务需求设置Request的属性
        // 这里需要根据Request的具体实现来设置必要的属性
        return request;
    }

    private InferBody createInferBody() {
        InferBody inferBody = new InferBody();
        inferBody.setRequest(createRequest());
        inferBody.setCatalogId("CATALOG_001");
        inferBody.setPlanIds(Arrays.asList("PLAN_001", "PLAN_002"));
        return inferBody;
    }


    private PlanSearchRequest createPlanSearchRequest() {
        PlanSearchRequest request = new PlanSearchRequest();
        request.setSceneInfo(createSceneInfo());
        request.setSessionInfo(createSessionInfo());
        request.setCatalogId("CATALOG_" + UUID.randomUUID());
        return request;
    }

    @Test
    void questionnaire_ValidRequest_ShouldReturnResponse() {
        // Arrange
        QuestionnaireRequest request = new QuestionnaireRequest();
        request.setSceneInfo(createSceneInfo());
        request.setSessionInfo(createSessionInfo());

        // Act
        QuestionnaireResponse response = abstractEngineFacade.questionnaire(request);

        // Assert
        assertNotNull(response);
        // Add more specific assertions based on expected response
    }

    @Test
    void userPortrait_ValidRequest_ShouldReturnResponse() {
        // Arrange
        UserPortraitRequest request = new UserPortraitRequest();
        request.setSceneInfo(createSceneInfo());
        request.setSessionInfo(createSessionInfo());

        // Act
        UserPortraitResponse response = abstractEngineFacade.userPortrait(request);

        // Assert
        assertNotNull(response);
        // Add more specific assertions
    }

    @Test
    void explainQuestionnaire_ValidRequest_ShouldReturnFlux() throws InterruptedException {
        // Arrange
        LlmRequest request = buildRequest();
        CountDownLatch latch = new CountDownLatch(1);

        // Act
        Flux<LlmResource> flux = abstractEngineFacade.explainQuestionnaire(request, LlmType.PORTRAIT_INFER);

        // Assert
        flux.subscribe(resource -> {
            log.info("Received LlmResource:{},isEnd:{},isFluxEnd:{} ", JSON.toJSONString(resource), resource.isEnd(), resource.isFluxEnd());
            latch.countDown();
        }, error -> {
            fail("Error occurred: " + error.getMessage());
            latch.countDown();
        });

        assertTrue(latch.await(5, TimeUnit.MINUTES), "Test timed out");
    }

    @Test
    void conductPreClassDialogue_ValidRequest_ShouldReturnFlux() throws InterruptedException {
        // Arrange
        LlmRequest request = buildRequest();
        CountDownLatch latch = new CountDownLatch(1);

        // Act
        Flux<LlmResource> flux = abstractEngineFacade.conductPreClassDialogue(request, LlmType.CLASS_CHAT);

        // Assert
        flux.subscribe(resource -> {
            log.info("Received LlmResource:{},isEnd:{},isFluxEnd:{} ", JSON.toJSONString(resource), resource.isEnd(), resource.isFluxEnd());
            latch.countDown();
        }, error -> {
            fail("Error occurred: " + error.getMessage());
            latch.countDown();
        });

        assertTrue(latch.await(5, TimeUnit.SECONDS), "Test timed out");
    }

    @Test
    void selectKnowledgeCluster_ValidRequest_ShouldReturnFlux() throws InterruptedException {
        // Arrange
        LlmRequest request = buildRequest();

        // Act
        LlmResource llmResource = abstractEngineFacade.selectKnowledgeCluster(request);

        log.info(JSON.toJSONString(llmResource));
    }

    @Test
    void createStudyPlan4Stream_ValidRequest_ShouldReturnFlux() throws InterruptedException {
        // Arrange
        LlmRequest request = buildRequest();
        CountDownLatch latch = new CountDownLatch(1);

        // Act
        Flux<LlmResource> flux = abstractEngineFacade.createStudyPlan4Stream(request, LlmType.LEARN_PLAN);

        // Assert
        flux.subscribe(resource -> {
            System.out.println("Received LlmResource: " + JSON.toJSONString(resource));
            latch.countDown();
        }, error -> {
            fail("Error occurred: " + error.getMessage());
            latch.countDown();
        });

        assertTrue(latch.await(5, TimeUnit.SECONDS), "Test timed out");
    }

    @Test
    void planAjust_ValidRequest_ShouldReturnResponse() {
        // Arrange
        PlanAjustRequest request = new PlanAjustRequest();
        request.setSceneInfo(createSceneInfo());
        request.setSessionInfo(createSessionInfo());

        // Act
        PlanAjustResponse response = abstractEngineFacade.planAjust(request);

        // Assert
        assertNotNull(response);
        // Add more specific assertions
    }

    @Test
    void planSearch_ValidRequest_ShouldReturnResponse() {
        // Arrange
        PlanSearchRequest request = createPlanSearchRequest();

        // Act
        PlanSearchResponse response = abstractEngineFacade.planSearch(request);

        // Assert
        assertNotNull(response);
        // Add more specific assertions
    }
} 