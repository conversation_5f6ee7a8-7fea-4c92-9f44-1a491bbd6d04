package com.iflytek.ebgai.ai.teacher.engine;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.AiTeacherServiceApplication;
import com.iflytek.ebgai.ai.teacher.dataapi.AITeacherDataHub;
import com.iflytek.ebgai.ai.teacher.dataapi.enums.PromptType;
import com.iflytek.rec.teacher.IflowLlmInstance;
import com.iflytek.rec.teacher.domain.agent.request.Body;
import com.iflytek.rec.teacher.domain.agent.request.Header;
import com.iflytek.rec.teacher.domain.agent.request.Request;
import com.iflytek.rec.teacher.domain.agent.request.ShortcutData;
import com.iflytek.rec.teacher.domain.agent.response.InferBody;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.rec.teacher.interfaces.impl.LearnPlanImpl;
import com.iflytek.rec.teacher.interfaces.param.LlmRequest;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

/**
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = AiTeacherServiceApplication.class)
public class AITeacherDataHubTest {
    @Test
    public void test01() throws InterruptedException {
        String re = AITeacherDataHub.getPromptService().getContentByKey(PromptType.CLASS_CHAT);
        log.info(re);
    }

    @Test
    public void test02() throws InterruptedException {
        Map<String, String> re = AITeacherDataHub.getAnchorKnowledgeClasschatQaService().getContent();
        log.info("返回结果：{}", re);
    }


}
