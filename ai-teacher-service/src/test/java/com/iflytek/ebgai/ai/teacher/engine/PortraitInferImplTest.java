package com.iflytek.ebgai.ai.teacher.engine;

import com.alibaba.fastjson2.JSON;
import com.iflytek.ebgai.ai.teacher.AiTeacherServiceApplication;
import com.iflytek.ebgai.ai.teacher.dataapi.entity.UserPortraitData;
import com.iflytek.rec.teacher.IflowLlmInstance;
import com.iflytek.rec.teacher.domain.agent.request.*;
import com.iflytek.rec.teacher.domain.agent.response.InferBody;
import com.iflytek.rec.teacher.domain.model.UserPortraits;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.rec.teacher.interfaces.impl.PortraitInferImpl;
import com.iflytek.rec.teacher.interfaces.param.LlmRequest;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;

import lombok.extern.slf4j.Slf4j;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import reactor.core.publisher.Flux;

/**
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = AiTeacherServiceApplication.class)
public class PortraitInferImplTest {
    @Test
    public void test01() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);

        IflowLlmInstance instance = new PortraitInferImpl();
        instance.createSession(buildRequest());
        Flux<LlmResource> sseFlux = instance.getResource();
        sseFlux.doFinally(signalType -> {
            log.info("Flux completed with signal: " + signalType);
            latch.countDown(); // 流结束时释放锁
        }).subscribe(llmResource -> log.info("Received LlmResource:{},isEnd:{},isFluxEnd:{} ", JSON.toJSONString(llmResource), llmResource.isEnd(), llmResource.isFluxEnd()), throwable -> {
            if (throwable instanceof EngineException) {
                EngineException ex = (EngineException) throwable;
                log.error("Business Error [Code: " + ex.getErrorCode() + "] - " + ex.getMessage());
            } else {
                log.error("Unexpected error occurred: " + throwable.getMessage());
                throwable.printStackTrace();
            }
            latch.countDown(); // 发生错误时也释放锁
        });
        // 无限等待，直到流正常结束或发生错误
        latch.await();
//		latch.await(60, TimeUnit.SECONDS);
    }

    private LlmRequest buildRequest() {
        LlmRequest llmRequest = new LlmRequest();
        InferBody inferBody = new InferBody();
        Request request = new Request();
        Header header = new Header();
        Body body = new Body();
        llmRequest.setInferBody(inferBody);
        llmRequest.setSceneInfo(new SceneInfo());
        llmRequest.setSessionInfo(new SessionInfo());
        inferBody.setRequest(request);
        request.setBody(body);
        request.setHeader(header);

        header.setAuthorization("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhaWQiOiJ4eGpjcHgteGNqengiLCJleHAiOiIxNzQ4MzQyMDI4In0.Ai0S1_eiU_xVLyZNka3uBdqwOR-LADyTqlP6qZUZQcs");
        header.setX_APPID("xxjcpx-xcjzx");
        header.setX_DEVICE_ID("021012242400100");
        header.setX_PLATFORM("Android");
        header.setX_TRACE_ID(UUID.randomUUID().toString());
        header.setX_USER_ID("04ef1c7f-1f3b-4c8b-837f-94b8d2160e88");
        header.setX_USER_TAG("tagtest");
        llmRequest.getInferBody().getRequest().setHeader(header);

        body.setAgentType(1);
        body.setVersionId("");
        ShortcutData shortcutData = new ShortcutData();
        shortcutData.setShortcutType(0);
        shortcutData.setWorkflowVersion("");
        body.setShortcutData(shortcutData);
        body.setContentType(1);
        body.setContent("txt-string");
        body.setDebugMode(true);
        Map<String, String> map = new HashMap<>();
        map.put("content", "用五句话回复我介绍下科大讯飞");
        body.setCustomVariables(map);
        body.setChatId(UUID.randomUUID().toString());
        return llmRequest;
    }

    public static void main(String[] args) {
        UserPortraitData data = new UserPortraitData();
        data.setUserId("1111");
        UserPortraits copy = new UserPortraits();
        BeanUtils.copyProperties(data, copy);
        log.info(JSON.toJSONString(copy));
    }
}
