package com.iflytek.ebgai.ai.teacher.engine;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.iflytek.ebgai.ai.teacher.AiTeacherServiceApplication;
import com.iflytek.rec.teacher.IflowLlmInstance;
import com.iflytek.rec.teacher.domain.agent.request.*;
import com.iflytek.rec.teacher.domain.agent.response.InferBody;
import com.iflytek.rec.teacher.domain.pojo.SceneInfo;
import com.iflytek.rec.teacher.domain.pojo.SessionInfo;
import com.iflytek.rec.teacher.exception.EngineException;
import com.iflytek.rec.teacher.feature.FeaQueryExpSchema;
import com.iflytek.rec.teacher.interfaces.impl.LearnPlanImpl;
import com.iflytek.rec.teacher.interfaces.param.LlmRequest;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;

import lombok.extern.slf4j.Slf4j;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.CountDownLatch;

import reactor.core.publisher.Flux;

/**
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = AiTeacherServiceApplication.class)
public class LearnPlanImplTest {
    String authorization = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhaWQiOiJ4eGpjcHgteGNqengiLCJleHAiOiIxNzQ5NjkxMTgyIn0.nyPHDNZtvg_1xleXRDrBvxKF_r-7FBc-yGWvHwZ45gk";

    @Test
    public void test01() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);

        IflowLlmInstance instance = new LearnPlanImpl();
        instance.createSession(buildRequest());
        Flux<LlmResource> sseFlux = instance.getResource();
        sseFlux.doFinally(signalType -> {
            System.out.println("Flux completed with signal: " + signalType);
            latch.countDown(); // 流结束时释放锁
        }).subscribe(llmResource -> System.out.println("Received LlmResource: " + JSON.toJSONString(llmResource)), throwable -> {
            if (throwable instanceof EngineException) {
                EngineException ex = (EngineException) throwable;
                System.err.println("Business Error [Code: " + ex.getErrorCode() + "] - " + ex.getMessage());
            } else {
                System.err.println("Unexpected error occurred: " + throwable.getMessage());
                throwable.printStackTrace();
            }
            latch.countDown(); // 发生错误时也释放锁
        });
        // 无限等待，直到流正常结束或发生错误
        latch.await();
    }

    private LlmRequest buildRequest() {
        LlmRequest llmRequest = new LlmRequest();
        InferBody inferBody = new InferBody();
        inferBody.setCatalogId("272_07020101272-6314_07020101272-6314-188948");
        Request request = new Request();
        Header header = new Header();
        Body body = new Body();
        llmRequest.setInferBody(inferBody);
        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode("SYNC_TUTORING");
        sceneInfo.setSubjectCode("02");
        sceneInfo.setPhaseCode("04");
        sceneInfo.setUserId("yyg");
        sceneInfo.setGraphVersion("20250530_001");
        llmRequest.setSceneInfo(sceneInfo);
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId("123vbibsbsveesaf");
        sessionInfo.setRoundId("ywebviaivafvaavavada");
        llmRequest.setSessionInfo(sessionInfo);

        inferBody.setRequest(request);
        request.setBody(body);
        request.setHeader(header);

        header.setAuthorization(authorization);
        header.setX_APPID("xxjcpx-xcjzx");
        header.setX_DEVICE_ID("021012242400100");
        header.setX_PLATFORM("Android");
        header.setX_TRACE_ID(UUID.randomUUID().toString());
        header.setX_USER_ID("04ef1c7f-1f3b-4c8b-837f-94b8d2160e88");
        header.setX_USER_TAG("tagtest");
        llmRequest.getInferBody().getRequest().setHeader(header);

        body.setAgentType(1);
        body.setVersionId("");
        ShortcutData shortcutData = new ShortcutData();
        // deepseek-r1
        shortcutData.setWorkflowId("682554b25cf2002c9fb76e1a");
        shortcutData.setShortcutType(0);
        shortcutData.setWorkflowVersion("");
        body.setShortcutData(shortcutData);
        body.setContentType(1);
        body.setContent("txt-string");
        body.setDebugMode(true);
        Map<String, String> map = new HashMap<>();
        map.put("content", "用五句话回复我介绍下科大讯飞");
        body.setCustomVariables(map);
        body.setChatId(UUID.randomUUID().toString());
        log.info("测试学习规划接口入参：{}", JSON.toJSONString(llmRequest));
        return llmRequest;
    }


}
