package com.iflytek.ebgai.ai.teacher.dataapifacade;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.ebgai.ai.teacher.AiTeacherServiceApplication;
import com.iflytek.ebgai.ai.teacher.service.plan.MasterServiceFacade;
import com.iflytek.rec.teacher.domain.agent.response.Intention;
import com.iflytek.rec.teacher.domain.agent.response.StorageInfo;
import com.iflytek.rec.teacher.domain.pojo.NodeInfo;
import com.iflytek.rec.teacher.interfaces.param.LlmResource;
import com.iflytek.skylab.core.constant.CatalogTypeEnum;
import com.iflytek.skylab.core.dataapi.util.SubCollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = AiTeacherServiceApplication.class)
class MasterServiceFacadeTest {

    @Autowired
    private MasterServiceFacade masterServiceFacade;


    /**
     * user_id ='xiangzhang182_test'
     * skylab.xxj_user_mastery_record31
     */
    @Test
    void updateMasterData_Success() {
        // 准备测试数据
        String llmResourceJson = "{\n" + "\t\"dynamic\": false,\n" + "\t\"end\": true,\n" + "\t\"fluxEnd\": true,\n" + "\t\"intention\": {\n" + "\t\t\"allSteps\": 0,\n" + "\t\t\"nodeInfos\": [\n" + "\t\t\t{\n" + "\t\t\t\t\"catalogId\": \"278_08020107278-9309_08020107278-9309-231845_08020107278-9309-231849\",\n" + "\t\t\t\t\"fusionMasterScore\": 0.71,\n" + "\t\t\t\t\"learndTimes\": 0,\n" + "\t\t\t\t\"masterScore\": 0.71,\n" + "\t\t\t\t\"nodeId\": \"9ae14de9-9ce2-456d-9404-95a9c34b2b26\",\n" + "\t\t\t\t\"nodeType\": \"ANCHOR_POINT\",\n" + "\t\t\t\t\"order\": 0,\n" + "\t\t\t\t\"plan\": true,\n" + "\t\t\t\t\"predictMasterScore\": -1.0,\n" + "\t\t\t\t\"realMasterScore\": 0.71\n" + "\t\t\t},\n" + "\t\t\t{\n" + "\t\t\t\t\"catalogId\": \"278_08020107278-9309_08020107278-9309-231845_08020107278-9309-231849\",\n" + "\t\t\t\t\"fusionMasterScore\": 0.54,\n" + "\t\t\t\t\"learndTimes\": 0,\n" + "\t\t\t\t\"masterScore\": 0.54,\n" + "\t\t\t\t\"nodeId\": \"d3bd0ce7-ef76-405b-802d-023821bc91f5\",\n" + "\t\t\t\t\"nodeType\": \"ANCHOR_POINT\",\n" + "\t\t\t\t\"order\": 0,\n" + "\t\t\t\t\"plan\": true,\n" + "\t\t\t\t\"predictMasterScore\": -1.0,\n" + "\t\t\t\t\"realMasterScore\": 0.54\n" + "\t\t\t},\n" + "\t\t\t{\n" + "\t\t\t\t\"catalogId\": \"278_08020107278-9309_08020107278-9309-231845_08020107278-9309-231849\",\n" + "\t\t\t\t\"fusionMasterScore\": 0.72,\n" + "\t\t\t\t\"learndTimes\": 0,\n" + "\t\t\t\t\"masterScore\": 0.72,\n" + "\t\t\t\t\"nodeId\": \"a91e2eff-32c2-4114-be6f-57b8f315f193\",\n" + "\t\t\t\t\"nodeType\": \"ANCHOR_POINT\",\n" + "\t\t\t\t\"order\": 0,\n" + "\t\t\t\t\"plan\": true,\n" + "\t\t\t\t\"predictMasterScore\": -1.0,\n" + "\t\t\t\t\"realMasterScore\": 0.72\n" + "\t\t\t}\n" + "\t\t],\n" + "\t\t\"planTimes\": 0,\n" + "\t\t\"results\": \"{  \\\"plugin\\\": \\\"read\\\",  \\\"function\\\": \\\"motivation\\\",  \\\"results\\\": {    \\\"课堂感受\\\": \\\"很轻松，基本都能理解\\\",    \\\"作业情况\\\": \\\"未知\\\",    \\\"学习时间\\\": \\\"20min\\\",    \\\"知识点-待定系数法求一次函数解析式\\\": \\\"正确\\\",    \\\"知识点-一次函数与二元一次方程的关系\\\": \\\"正确\\\",    \\\"知识点-一次函数与面积分类讨论\\\": \\\"错误\\\"  }}\",\n" + "\t\t\"step\": 0\n" + "\t},\n" + "\t\"output\": true,\n" + "\t\"storage\": true,\n" + "\t\"storageInfo\": {\n" + "\t\t\"bizAction\": \"AI_TUTORING_LEARNING_PLAN\",\n" + "\t\t\"catalogId\": \"278_08020107278-9309_08020107278-9309-231845_08020107278-9309-231849\",\n" + "\t\t\"functionCode\": \"LEARNINGPLAN_CHAT\",\n" + "\t\t\"oriContent\": \"{\\\"课堂感受\\\":\\\"很轻松，基本都能理解\\\",\\\"作业情况\\\":\\\"未知\\\",\\\"学习时间\\\":\\\"20min\\\",\\\"知识点-待定系数法求一次函数解析式\\\":\\\"正确\\\",\\\"知识点-一次函数与二元一次方程的关系\\\":\\\"正确\\\",\\\"知识点-一次函数与面积分类讨论\\\":\\\"错误\\\"}\",\n" + "\t\t\"query\": \"两条直线相交则有两个解\",\n" + "\t\t\"roundId\": \"3673f7fa-2878-4ddb-8749-612ff96ae276\",\n" + "\t\t\"traceId\": \"4dc04ba6-48ca-4cd3-b622-27ac984a2bf9\",\n" + "\t\t\"updateTime\": 1750642146590,\n" + "\t\t\"userId\": \"8bff555f-282b-4688-a47b-452b281a88fa\"\n" + "\t},\n" + "\t\"updateMastery\": true\n" + "}";
        LlmResource llmResource1 = JSONObject.parseObject(llmResourceJson, LlmResource.class);
        List<NodeInfo> nodeInfos = llmResource1.getIntention().getNodeInfos();
        for (NodeInfo nodeInfo : nodeInfos) {
            nodeInfo.setCatalogType(CatalogTypeEnum.AIEXAM);
        }
        // 执行测试
        masterServiceFacade.updateMasterData(llmResource1, "test_trace_id");

    }

    @Test
    public void testGetFenKuName() {
        String dbName = SubCollectionUtils.getSubUserMasteryRecordCollectionName("8bff555f-282b-4688-a47b-452b281a88fa");
        log.info(dbName);
    }


}