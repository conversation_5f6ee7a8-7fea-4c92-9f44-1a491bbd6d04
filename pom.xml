<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-starter-parent</artifactId>
        <version>4.0.18</version>
    </parent>

    <name>ai-teacher</name>
    <description>recommend platform ai teacher service</description>
    <groupId>com.iflytek.ebg.ai</groupId>
    <artifactId>ai-teacher</artifactId>
    <version>${revision}</version>

    <packaging>pom</packaging>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <revision>1.0.2-SNAPSHOT</revision>
    </properties>
    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>mvn-releases</id>
            <url>https://depend.iflytek.com/artifactory/ebg-mvn-release-private/</url>
        </repository>
        <snapshotRepository>
            <id>mvn-snapshots</id>
            <url>https://depend.iflytek.com/artifactory/ebg-mvn-snapshot-private/</url>
        </snapshotRepository>
    </distributionManagement>
    <modules>
        <module>ai-teacher-common</module>
        <module>ai-teacher-api</module>
        <module>ai-teacher-dataapi</module>
        <module>ai-teacher-service</module>
        <module>ai-teacher-engine</module>
        <module>ai-teacher-dubbo</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.iflytek.ebg.ai</groupId>
                <artifactId>ai-teacher-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.ebg.ai</groupId>
                <artifactId>ai-teacher-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.ebg.ai</groupId>
                <artifactId>ai-teacher-dataapi</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.ebg.ai</groupId>
                <artifactId>ai-teacher-engine</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
